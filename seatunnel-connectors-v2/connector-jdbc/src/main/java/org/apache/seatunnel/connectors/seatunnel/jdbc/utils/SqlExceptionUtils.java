package org.apache.seatunnel.connectors.seatunnel.jdbc.utils;

import java.sql.SQLException;

public class SqlExceptionUtils {

    public static boolean isIgnoreSingleRecord(
            String pkStrategy, String insertErrorStrategy, String url, SQLException e) {
        if ("stop".equalsIgnoreCase(insertErrorStrategy)) {
            return false;
        }
        if ("continue".equalsIgnoreCase(insertErrorStrategy)) {
            if (isPkConflict(url, e)) {
                if ("stop".equalsIgnoreCase(pkStrategy)) {
                    return false;
                } else if ("continue".equalsIgnoreCase(pkStrategy)) {
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

    private static boolean isPkConflict(String url, SQLException e) {
        String sqlState = e.getSQLState();
        int errorCode = e.getErrorCode();
        if (null != url && url.toLowerCase().contains(":oracle")) {
            return e.getMessage().contains("ORA-00001");
        }
        if (null != url && url.toLowerCase().contains(":db2")) {
            return (e.getSQLState().equalsIgnoreCase("23000"))
                    || e.getMessage().contains("ERRORCODE=-4229, SQLSTATE=null");
        }
        return "23000".equals(sqlState) || "23505".equals(sqlState);
    }
}
