/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.utils.SqlExceptionUtils;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
public class BufferedBatchStatementExecutor implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    @NonNull private final JdbcBatchStatementExecutor<SeaTunnelRow> statementExecutor;

    private final JdbcBatchStatementExecutor<SeaTunnelRow> singleExecutor;
    @NonNull private final Function<SeaTunnelRow, SeaTunnelRow> valueTransform;

    @NonNull private final List<SeaTunnelRow> buffer = new ArrayList<>();

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        statementExecutor.prepareStatements(connection);
        if (null != singleExecutor) {
            // gpload的时候，这个singleExecutor 会是空的
            singleExecutor.prepareStatements(connection);
        }
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        buffer.add(valueTransform.apply(record));
    }

    @Override
    public void executeBatch(boolean openTransaction) throws SQLException {
        if (!buffer.isEmpty()) {
            for (SeaTunnelRow row : buffer) {
                statementExecutor.addToBatch(row);
            }
            statementExecutor.executeBatch(openTransaction);
            buffer.clear();
        }
    }

    @Override
    public void executeBatch(
            String databaseName, String tableName, String fieldIde, List<String> partitionKeys)
            throws SQLException {
        if (!buffer.isEmpty()) {
            // hive 底层没有实现批量方法，这里通过集合批量拼接values实现
            if (statementExecutor.getClass() == HiveBatchStatementExecutor.class) {
                ((HiveBatchStatementExecutor) statementExecutor)
                        .executeBatchList(databaseName, tableName, buffer, fieldIde, partitionKeys);
            } else {
                statementExecutor.executeBatch(false);
            }
            buffer.clear();
        }
    }

    @Override
    public void executeErrorStopBatch(
            String databaseName,
            String tableName,
            String pkStrategy,
            String insertErrorStrategy,
            String url,
            List<String> primaryKeys,
            String fieldIde,
            List<String> partitionKeys,
            boolean openTransaction)
            throws SQLException {
        if (!buffer.isEmpty()) {
            // hive 底层没有实现批量方法，这里通过集合批量拼接values实现
            if (statementExecutor.getClass() == HiveBatchStatementExecutor.class) {
                ((HiveBatchStatementExecutor) statementExecutor)
                        .executeBatchList(databaseName, tableName, buffer, fieldIde, partitionKeys);
            } else {
                for (SeaTunnelRow row : buffer) {
                    statementExecutor.addToBatch(row);
                }
                try {
                    statementExecutor.executeBatch(openTransaction);
                } catch (SQLException e) {
                    for (SeaTunnelRow row : buffer) {
                        singleExecutor.addToBatch(row);
                        try {
                            singleExecutor.executeBatch(openTransaction);
                        } catch (SQLException e1) {
                            if (SqlExceptionUtils.isIgnoreSingleRecord(
                                    pkStrategy, insertErrorStrategy, url, e1)) {
                                log.error(
                                        "当前记录错误，错误信息是 {},行数据内容是{} ,忽略此异常",
                                        e1.getMessage(),
                                        row.getFields());
                            } else {
                                log.error(
                                        "当前记录错误，错误信息是 {},行数据内容是{} ,不忽略此异常",
                                        e1.getMessage(),
                                        row.getFields());
                                throw e1;
                            }
                        }
                    }
                }
            }
            buffer.clear();
        }
    }

    @Override
    public void closeStatements() throws SQLException {
        if (!buffer.isEmpty()) {
            executeBatch(false);
        }
        statementExecutor.closeStatements();
    }
}
