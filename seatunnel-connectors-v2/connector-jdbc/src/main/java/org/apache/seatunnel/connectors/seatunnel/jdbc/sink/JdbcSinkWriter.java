/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.sink;

import org.apache.seatunnel.api.sink.MultiTableResourceManager;
import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.sink.SupportMultiTableSinkWriter;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.jdbc.config.JdbcSinkConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.JdbcOutputFormat;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.JdbcOutputFormatBuilder;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.JdbcConnectionProvider;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.connection.SimpleJdbcConnectionPoolProviderProxy;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.JdbcDialect;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.hive.HiveJdbcConnectionProvider;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor.JdbcBatchStatementExecutor;
import org.apache.seatunnel.connectors.seatunnel.jdbc.state.JdbcSinkState;
import org.apache.seatunnel.connectors.seatunnel.jdbc.state.XidInfo;

import org.apache.commons.collections4.CollectionUtils;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
public class JdbcSinkWriter
        implements SinkWriter<SeaTunnelRow, XidInfo, JdbcSinkState>,
                SupportMultiTableSinkWriter<ConnectionPoolManager> {
    private JdbcOutputFormat<SeaTunnelRow, JdbcBatchStatementExecutor<SeaTunnelRow>> outputFormat;
    private final JdbcDialect dialect;
    private final TableSchema tableSchema;
    private final TableSchema databaseTableSchema;
    private JdbcConnectionProvider connectionProvider;
    private transient boolean isOpen;
    private final Integer primaryKeyIndex;
    private final JdbcSinkConfig jdbcSinkConfig;

    public JdbcSinkWriter(
            JdbcDialect dialect,
            JdbcSinkConfig jdbcSinkConfig,
            TableSchema tableSchema,
            TableSchema databaseTableSchema,
            Integer primaryKeyIndex) {
        this.jdbcSinkConfig = jdbcSinkConfig;
        this.dialect = dialect;
        this.tableSchema = tableSchema;
        this.databaseTableSchema = databaseTableSchema;
        this.primaryKeyIndex = primaryKeyIndex;
        this.connectionProvider =
                dialect.getJdbcConnectionProvider(jdbcSinkConfig.getJdbcConnectionConfig());
        this.outputFormat =
                new JdbcOutputFormatBuilder(
                                dialect,
                                connectionProvider,
                                jdbcSinkConfig,
                                tableSchema,
                                databaseTableSchema)
                        .build();
    }

    /*   @Override
    public void open() throws IOException {
        if (CollectionUtils.isNotEmpty(jdbcSinkConfig.getPreSQL())) {
            try {
                tryOpen();
                Connection conn = connectionProvider.getOrEstablishConnection();
                try (Statement stat = conn.createStatement()) {
                    for (String preSql : jdbcSinkConfig.getPreSQL()) {
                        stat.execute(preSql);
                    }
                    if (!conn.getAutoCommit()) {
                        conn.commit();
                    }
                } catch (SQLException e) {
                    if (!conn.getAutoCommit()) {
                        conn.rollback();
                    }
                }
            } catch (SQLException | ClassNotFoundException e) {
                throw new JdbcConnectorException(
                        JdbcConnectorErrorCode.PRE_SQL_FAILED,
                        "execute pre sql failed",
                        e);
            }
        }
    }*/

    @Override
    public MultiTableResourceManager<ConnectionPoolManager> initMultiTableResourceManager(
            int tableSize, int queueSize) {
        HikariDataSource ds = new HikariDataSource();
        ds.setIdleTimeout(30 * 1000);
        ds.setMaximumPoolSize(queueSize);
        ds.setDriverClassName(jdbcSinkConfig.getJdbcConnectionConfig().getDriverName());
        ds.setJdbcUrl(jdbcSinkConfig.getJdbcConnectionConfig().getUrl());
        if (jdbcSinkConfig.getJdbcConnectionConfig().getUrl().startsWith("jdbc:as400:")) {
            // as400需要显式加载驱动类，不然会报找不到合适的驱动
            /*try {
                Class.forName(jdbcSinkConfig.getJdbcConnectionConfig().getDriverName());
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }*/
            // as400的驱动包中没有实现isValid()方法，可以用这种方式代替
            ds.setConnectionTestQuery("SELECT 1 FROM SYSIBM.SYSDUMMY1");
        }
        if (jdbcSinkConfig.getJdbcConnectionConfig().getUsername().isPresent()) {
            ds.setUsername(jdbcSinkConfig.getJdbcConnectionConfig().getUsername().get());
        }
        if (jdbcSinkConfig.getJdbcConnectionConfig().getPassword().isPresent()) {
            ds.setPassword(jdbcSinkConfig.getJdbcConnectionConfig().getPassword().get());
        }
        ds.setAutoCommit(jdbcSinkConfig.getJdbcConnectionConfig().isAutoCommit());
        return new JdbcMultiTableResourceManager(new ConnectionPoolManager(ds));
    }

    @Override
    public void setMultiTableResourceManager(
            MultiTableResourceManager<ConnectionPoolManager> multiTableResourceManager,
            int queueIndex) {
        connectionProvider.closeConnection();
        if (connectionProvider instanceof HiveJdbcConnectionProvider) {
            this.connectionProvider =
                    dialect.getJdbcConnectionProvider(jdbcSinkConfig.getJdbcConnectionConfig());
        } else {
            this.connectionProvider =
                    new SimpleJdbcConnectionPoolProviderProxy(
                            multiTableResourceManager.getSharedResource().get(),
                            jdbcSinkConfig.getJdbcConnectionConfig(),
                            queueIndex);
        }
        this.outputFormat =
                new JdbcOutputFormatBuilder(
                                dialect,
                                connectionProvider,
                                jdbcSinkConfig,
                                tableSchema,
                                databaseTableSchema)
                        .build();
    }

    @Override
    public Optional<Integer> primaryKey() {
        return primaryKeyIndex != null ? Optional.of(primaryKeyIndex) : Optional.empty();
    }

    private void tryOpen() throws IOException {
        if (!isOpen) {
            isOpen = true;
            outputFormat.open();
        }
    }

    @Override
    public List<JdbcSinkState> snapshotState(long checkpointId) {
        return Collections.emptyList();
    }

    @Override
    public void write(SeaTunnelRow element) throws IOException {
        int bytesSize = element.getBytesSize();
        boolean flag = Arrays.stream(element.getFields()).allMatch(field -> field == null);
        if (bytesSize == 0 && flag) {
            return;
        }
        tryOpen();
        outputFormat.writeRecord(element);
    }

    @Override
    public Optional<XidInfo> prepareCommit() throws IOException {
        tryOpen();
        outputFormat.checkFlushException();
        outputFormat.flush();
        try {
            if (!connectionProvider.getConnection().getAutoCommit()) {
                connectionProvider.getConnection().commit();
            }
        } catch (SQLException e) {
            throw new JdbcConnectorException(
                    JdbcConnectorErrorCode.TRANSACTION_OPERATION_FAILED,
                    "commit failed," + e.getMessage(),
                    e);
        }
        return Optional.empty();
    }

    @Override
    public void abortPrepare() {}

    @Override
    public void close() throws IOException {
        tryOpen();
        outputFormat.flush();
        try {
            if (!connectionProvider.getConnection().getAutoCommit()) {
                connectionProvider.getConnection().commit();
            }
        } catch (SQLException e) {
            throw new JdbcConnectorException(
                    CommonErrorCodeDeprecated.WRITER_OPERATION_FAILED,
                    "unable to close JDBC sink write",
                    e);
        }
        if (CollectionUtils.isNotEmpty(jdbcSinkConfig.getPostSQL())) {
            try {
                Connection conn = connectionProvider.getOrEstablishConnection();
                try (Statement stat = conn.createStatement()) {
                    for (String postSql : jdbcSinkConfig.getPostSQL()) {
                        stat.execute(postSql);
                    }
                    if (!conn.getAutoCommit()) {
                        conn.commit();
                    }
                } catch (Exception e) {
                    if (!conn.getAutoCommit()) {
                        conn.rollback();
                    }
                    throw new JdbcConnectorException(
                            JdbcConnectorErrorCode.POST_SQL_FAILED, "Execute postSQL failed", e);
                }
            } catch (SQLException | ClassNotFoundException e) {
                //                log.info("seatunnel:目标端后置sql执行失败");
                throw new JdbcConnectorException(
                        JdbcConnectorErrorCode.POST_SQL_FAILED, "Execute postSQL failed", e);
            }
            //            log.info("seatunnel:目标端后置sql执行{}条，成功",
            // jdbcSinkConfig.getPostSQL().size());
        } else {
            //            log.info("seatunnel:目标端无后置sql");
        }
        outputFormat.close();
    }
}
