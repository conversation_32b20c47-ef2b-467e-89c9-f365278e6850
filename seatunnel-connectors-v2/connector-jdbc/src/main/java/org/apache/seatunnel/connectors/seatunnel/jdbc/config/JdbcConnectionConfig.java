/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.config;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.dialect.dialectenum.FieldIdeEnum;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class JdbcConnectionConfig implements Serializable {
    private static final long serialVersionUID = 2L;

    public String url;
    public String driverName;
    public String compatibleMode;
    public int connectionCheckTimeoutSeconds =
            JdbcOptions.CONNECTION_CHECK_TIMEOUT_SEC.defaultValue();
    public int maxRetries = JdbcOptions.MAX_RETRIES.defaultValue();
    public String username;
    public String password;
    public String query;
    public String table;
    public String databaseName;
    public FieldIdeEnum fieldIde;
    private List<String> primaryKeys;
    public List<String> partitionKeys = new ArrayList<>();
    private String schema;

    public boolean autoCommit = JdbcOptions.AUTO_COMMIT.defaultValue();

    public int batchSize = JdbcOptions.BATCH_SIZE.defaultValue();

    public String xaDataSourceClassName;

    public int maxCommitAttempts = JdbcOptions.MAX_COMMIT_ATTEMPTS.defaultValue();

    public int transactionTimeoutSec = JdbcOptions.TRANSACTION_TIMEOUT_SEC.defaultValue();

    public boolean useKerberos = JdbcOptions.USE_KERBEROS.defaultValue();

    public String kerberosPrincipal;

    public String kerberosKeytabPath;

    public String krb5Path = JdbcOptions.KRB5_PATH.defaultValue();

    private Map<String, String> properties;

    private String pkStrategy;

    private String insertErrorStrategy;

    public static JdbcConnectionConfig of(ReadonlyConfig config) {
        JdbcConnectionConfig.Builder builder = JdbcConnectionConfig.builder();
        builder.url(config.get(JdbcOptions.URL));
        builder.compatibleMode(config.get(JdbcOptions.COMPATIBLE_MODE));
        builder.driverName(config.get(JdbcOptions.DRIVER));
        builder.autoCommit(config.get(JdbcOptions.AUTO_COMMIT));
        builder.maxRetries(config.get(JdbcOptions.MAX_RETRIES));
        builder.connectionCheckTimeoutSeconds(config.get(JdbcOptions.CONNECTION_CHECK_TIMEOUT_SEC));
        builder.batchSize(config.get(JdbcOptions.BATCH_SIZE));
        if (config.get(JdbcOptions.IS_EXACTLY_ONCE)) {
            builder.xaDataSourceClassName(config.get(JdbcOptions.XA_DATA_SOURCE_CLASS_NAME));
            builder.maxCommitAttempts(config.get(JdbcOptions.MAX_COMMIT_ATTEMPTS));
            builder.transactionTimeoutSec(config.get(JdbcOptions.TRANSACTION_TIMEOUT_SEC));
            builder.maxRetries(0);
        }
        if (config.get(JdbcOptions.USE_KERBEROS)) {
            builder.useKerberos(config.get(JdbcOptions.USE_KERBEROS));
            builder.kerberosPrincipal(config.get(JdbcOptions.KERBEROS_PRINCIPAL));
            builder.kerberosKeytabPath(config.get(JdbcOptions.KERBEROS_KEYTAB_PATH));
            builder.krb5Path(config.get(JdbcOptions.KRB5_PATH));
        }
        config.getOptional(JdbcOptions.USER).ifPresent(builder::username);
        config.getOptional(JdbcOptions.PASSWORD).ifPresent(builder::password);
        config.getOptional(JdbcOptions.PROPERTIES).ifPresent(builder::properties);
        config.getOptional(JdbcOptions.PK_STRATEGY).ifPresent(builder::pkStrategy);
        config.getOptional(JdbcOptions.INSERT_ERROR_STRATEGY)
                .ifPresent(builder::insertErrorStrategy);

        config.getOptional(JdbcOptions.TABLE).ifPresent(builder::table);
        config.getOptional(JdbcOptions.DATABASE).ifPresent(builder::databaseName);
        config.getOptional(JdbcOptions.PRIMARY_KEYS).ifPresent(builder::primaryKeys);
        config.getOptional(JdbcOptions.PARTITION_KEYS).ifPresent(builder::partitionKeys);
        config.getOptional(JdbcOptions.FIELD_IDE).ifPresent(builder::fieldIde);
        config.getOptional(JdbcOptions.SCHEMA).ifPresent(builder::schema);
        return builder.build();
    }

    public String getUrl() {
        return url;
    }

    public String getDriverName() {
        return driverName;
    }

    public String getCompatibleMode() {
        return compatibleMode;
    }

    public boolean isAutoCommit() {
        return autoCommit;
    }

    public int getConnectionCheckTimeoutSeconds() {
        return connectionCheckTimeoutSeconds;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public Optional<String> getUsername() {
        return Optional.ofNullable(username);
    }

    public Optional<String> getPassword() {
        return Optional.ofNullable(password);
    }

    public int getBatchSize() {
        return batchSize;
    }

    public String getXaDataSourceClassName() {
        return xaDataSourceClassName;
    }

    public int getMaxCommitAttempts() {
        return maxCommitAttempts;
    }

    public Optional<Integer> getTransactionTimeoutSec() {
        return transactionTimeoutSec < 0 ? Optional.empty() : Optional.of(transactionTimeoutSec);
    }

    public Map<String, String> getProperties() {
        return properties;
    }

    public String getPkStrategy() {
        return pkStrategy;
    }

    public String getInsertErrorStrategy() {
        return insertErrorStrategy;
    }

    public void setInsertErrorStrategy(String insertErrorStrategy) {
        this.insertErrorStrategy = insertErrorStrategy;
    }

    public static JdbcConnectionConfig.Builder builder() {
        return new JdbcConnectionConfig.Builder();
    }

    public List<String> getPrimaryKeys() {
        return primaryKeys;
    }

    public void setPrimaryKeys(List<String> primaryKeys) {
        this.primaryKeys = primaryKeys;
    }

    public List<String> getPartitionKeys() {
        return partitionKeys;
    }

    public void setPartitionKeys(List<String> partitionKeys) {
        this.partitionKeys = partitionKeys;
    }

    public String getSchema() {
        return schema;
    }

    public void setSchema(String schema) {
        this.schema = schema;
    }

    public static final class Builder {
        private String url;
        private String driverName;
        private String compatibleMode;
        private int connectionCheckTimeoutSeconds =
                JdbcOptions.CONNECTION_CHECK_TIMEOUT_SEC.defaultValue();
        private int maxRetries = JdbcOptions.MAX_RETRIES.defaultValue();
        private String username;
        private String password;
        private String table;
        private String databaseName;
        private String query;
        private boolean autoCommit = JdbcOptions.AUTO_COMMIT.defaultValue();
        private int batchSize = JdbcOptions.BATCH_SIZE.defaultValue();
        private String xaDataSourceClassName;
        private int maxCommitAttempts = JdbcOptions.MAX_COMMIT_ATTEMPTS.defaultValue();
        private int transactionTimeoutSec = JdbcOptions.TRANSACTION_TIMEOUT_SEC.defaultValue();
        private Map<String, String> properties;
        public boolean useKerberos = JdbcOptions.USE_KERBEROS.defaultValue();
        public String kerberosPrincipal;
        public String kerberosKeytabPath;
        public String krb5Path = JdbcOptions.KRB5_PATH.defaultValue();
        public String pkStrategy = JdbcOptions.PK_STRATEGY.defaultValue();
        public String insertErrorStrategy = JdbcOptions.INSERT_ERROR_STRATEGY.defaultValue();
        private List<String> primaryKeys = JdbcOptions.PRIMARY_KEYS.defaultValue();
        private List<String> partitionKeys = JdbcOptions.PARTITION_KEYS.defaultValue();

        private FieldIdeEnum fieldIde;
        private String schema;

        private Builder() {}

        public Builder url(String url) {
            //            if (url.startsWith("jdbc:mysql")) {
            //                url = JdbcUrlUtils.initMysqlJdbcDefaultParams(url);
            //            }
            this.url = url;
            return this;
        }

        public Builder driverName(String driverName) {
            this.driverName = driverName;
            return this;
        }

        public Builder compatibleMode(String compatibleMode) {
            this.compatibleMode = compatibleMode;
            return this;
        }

        public Builder connectionCheckTimeoutSeconds(int connectionCheckTimeoutSeconds) {
            this.connectionCheckTimeoutSeconds = connectionCheckTimeoutSeconds;
            return this;
        }

        public Builder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder table(String table) {
            this.table = table;
            return this;
        }

        public Builder databaseName(String databaseName) {
            this.databaseName = databaseName;
            return this;
        }

        public Builder primaryKeys(List<String> primaryKeys) {
            this.primaryKeys = primaryKeys;
            return this;
        }

        public Builder partitionKeys(List<String> partitionKeys) {
            this.partitionKeys = partitionKeys;
            return this;
        }

        public Builder query(String query) {
            this.query = query;
            return this;
        }

        public Builder autoCommit(boolean autoCommit) {
            this.autoCommit = autoCommit;
            return this;
        }

        public Builder batchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public Builder xaDataSourceClassName(String xaDataSourceClassName) {
            this.xaDataSourceClassName = xaDataSourceClassName;
            return this;
        }

        public Builder maxCommitAttempts(int maxCommitAttempts) {
            this.maxCommitAttempts = maxCommitAttempts;
            return this;
        }

        public Builder transactionTimeoutSec(int transactionTimeoutSec) {
            this.transactionTimeoutSec = transactionTimeoutSec;
            return this;
        }

        public Builder useKerberos(boolean useKerberos) {
            this.useKerberos = useKerberos;
            return this;
        }

        public Builder kerberosPrincipal(String kerberosPrincipal) {
            this.kerberosPrincipal = kerberosPrincipal;
            return this;
        }

        public Builder kerberosKeytabPath(String kerberosKeytabPath) {
            this.kerberosKeytabPath = kerberosKeytabPath;
            return this;
        }

        public Builder krb5Path(String krb5Path) {
            this.krb5Path = krb5Path;
            return this;
        }

        public Builder properties(Map<String, String> properties) {
            this.properties = properties;
            return this;
        }

        public Builder pkStrategy(String pkStrategy) {
            this.pkStrategy = pkStrategy;
            return this;
        }

        public Builder insertErrorStrategy(String insertErrorStrategy) {
            this.insertErrorStrategy = insertErrorStrategy;
            return this;
        }

        public Builder fieldIde(FieldIdeEnum fieldIde) {
            this.fieldIde = fieldIde;
            return this;
        }

        public Builder schema(String schema) {
            this.schema = schema;
            return this;
        }

        public JdbcConnectionConfig build() {
            JdbcConnectionConfig jdbcConnectionConfig = new JdbcConnectionConfig();
            jdbcConnectionConfig.batchSize = this.batchSize;
            jdbcConnectionConfig.driverName = this.driverName;
            jdbcConnectionConfig.compatibleMode = this.compatibleMode;
            jdbcConnectionConfig.maxRetries = this.maxRetries;
            jdbcConnectionConfig.password = this.password;
            jdbcConnectionConfig.connectionCheckTimeoutSeconds = this.connectionCheckTimeoutSeconds;
            jdbcConnectionConfig.url = this.url;
            jdbcConnectionConfig.autoCommit = this.autoCommit;
            jdbcConnectionConfig.username = this.username;
            jdbcConnectionConfig.transactionTimeoutSec = this.transactionTimeoutSec;
            jdbcConnectionConfig.maxCommitAttempts = this.maxCommitAttempts;
            jdbcConnectionConfig.xaDataSourceClassName = this.xaDataSourceClassName;
            jdbcConnectionConfig.useKerberos = this.useKerberos;
            jdbcConnectionConfig.kerberosPrincipal = this.kerberosPrincipal;
            jdbcConnectionConfig.kerberosKeytabPath = this.kerberosKeytabPath;
            jdbcConnectionConfig.krb5Path = this.krb5Path;
            jdbcConnectionConfig.properties =
                    this.properties == null ? new HashMap<>() : this.properties;
            jdbcConnectionConfig.pkStrategy = this.pkStrategy;
            jdbcConnectionConfig.insertErrorStrategy = this.insertErrorStrategy;
            jdbcConnectionConfig.table = this.table;
            jdbcConnectionConfig.databaseName = this.databaseName;
            jdbcConnectionConfig.primaryKeys = this.primaryKeys;
            jdbcConnectionConfig.fieldIde = this.fieldIde;
            jdbcConnectionConfig.partitionKeys = this.partitionKeys;
            jdbcConnectionConfig.schema = this.schema;
            return jdbcConnectionConfig;
        }
    }
}
