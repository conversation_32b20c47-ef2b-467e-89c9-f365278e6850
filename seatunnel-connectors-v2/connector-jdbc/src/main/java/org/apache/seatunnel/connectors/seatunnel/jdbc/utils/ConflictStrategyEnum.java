package org.apache.seatunnel.connectors.seatunnel.jdbc.utils;

/**
 * <AUTHOR>
 * @date 2024/5/15
 */
public enum ConflictStrategyEnum {
    NONE(""),
    ONLY_DELETE_CONFLICTING_ROWS("only_delete_conflicting_rows"),
    ONLY_UPDATE_CONFLICTING_ROWS("only_update_conflicting_rows"),
    DELETE_CONFLICTING_BEFORE_INSERTING_ROWS("delete_conflicting_before_inserting_rows");

    private final String value;

    ConflictStrategyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
