package org.apache.seatunnel.connectors.seatunnel.jdbc.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

public class JdbcUrlUtils {

    public static Map<String, String> getQueryParams(String url)
            throws UnsupportedEncodingException {
        Map<String, String> queryParams = new HashMap<>();
        String[] pairs = url.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            queryParams.put(
                    URLDecoder.decode(pair.substring(0, idx), "UTF-8"),
                    URLDecoder.decode(pair.substring(idx + 1), "UTF-8"));
        }
        return queryParams;
    }

    static Map<String, String> defaultParams = new HashMap<>();

    static {
        defaultParams.put("useUnicode", "true");
        defaultParams.put("characterEncoding", "utf-8");
        defaultParams.put("zeroDateTimeBehavior", "convertToNull");
        defaultParams.put("useSSL", "false");
        defaultParams.put("serverTimezone", "Asia/Shanghai");
        defaultParams.put("rewriteBatchedStatements", "true");
    }

    public static String initMysqlJdbcDefaultParams(String jdbcUrl) {
        Map<String, String> queryParams = new HashMap<>(defaultParams);
        if (jdbcUrl.contains("?")) {
            String params = jdbcUrl.substring(jdbcUrl.indexOf("?") + 1);
            try {
                queryParams.putAll(getQueryParams(params));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }
        // 构建最终的 JDBC URL
        StringBuilder finalJdbcUrl = new StringBuilder(jdbcUrl.split("\\?")[0]);
        if (!queryParams.isEmpty()) {
            finalJdbcUrl.append("?");
            queryParams.forEach(
                    (key, value) -> finalJdbcUrl.append(key).append("=").append(value).append("&"));
            finalJdbcUrl.deleteCharAt(finalJdbcUrl.length() - 1); // 移除最后一个多余的 &
        }

        return finalJdbcUrl.toString();
    }
}
