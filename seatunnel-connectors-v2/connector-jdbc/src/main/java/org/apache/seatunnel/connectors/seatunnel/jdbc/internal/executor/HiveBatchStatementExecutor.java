/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.jdbc.exception.JdbcConnectorException;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import scala.Tuple2;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
public class HiveBatchStatementExecutor implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    @NonNull private final StatementFactory statementFactory;
    @NonNull private final TableSchema tableSchema;
    @NonNull private final JdbcRowConverter converter;
    private transient PreparedStatement statement;

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        statement = statementFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        converter.toExternal(tableSchema, record, statement);
        statement.execute();
    }

    public void executeBatchList(
            String databaseName,
            String tableName,
            List<SeaTunnelRow> records,
            String fieldIde,
            List<String> partitionKeys)
            throws SQLException {
        partitionKeys = partitionKeys == null ? new ArrayList<>() : partitionKeys;
        String sql = getSql(databaseName, tableName, records, fieldIde, partitionKeys);
        log.info("开始执行executeBatchList方法,记录条数是 {}", records.size());
        statement = statement.getConnection().prepareStatement(sql);
        statement.execute("SET hive.exec.dynamic.partition.mode=nonstrict");
        statement = toExternal(tableSchema, records, statement, partitionKeys);
        statement.execute();
        log.info("executeBatchList方法执行完毕...");
    }

    public PreparedStatement toExternal(
            TableSchema tableSchema,
            List<SeaTunnelRow> rows,
            PreparedStatement statement,
            List<String> partitionKeys)
            throws SQLException {
        AtomicInteger atomicInteger = new AtomicInteger(0);
        SeaTunnelRowType rowType = tableSchema.toPhysicalRowDataType();
        String[] originalFieldNames = rowType.getFieldNames();

        // 构建调整后的字段顺序
        List<String> adjustedFieldOrder = new ArrayList<>();
        // 先添加非分区字段
        for (String field : originalFieldNames) {
            if (!partitionKeys.contains(field)) {
                adjustedFieldOrder.add(field);
            }
        }
        // 再添加分区字段
        adjustedFieldOrder.addAll(partitionKeys);

        // 创建字段名到索引和类型的映射
        Map<String, Tuple2<Integer, SeaTunnelDataType<?>>> fieldNameToInfo = new HashMap<>();
        for (int i = 0; i < originalFieldNames.length; i++) {
            fieldNameToInfo.put(originalFieldNames[i], new Tuple2<>(i, rowType.getFieldType(i)));
        }

        log.info("字段顺序调整信息：");
        log.info("原始字段顺序: {}", Arrays.toString(originalFieldNames));
        log.info("调整后字段顺序: {}", adjustedFieldOrder);
        log.info("分区字段: {}", partitionKeys);

        for (SeaTunnelRow row : rows) {
            for (int adjustedIndex = 0;
                    adjustedIndex < adjustedFieldOrder.size();
                    adjustedIndex++) {
                String fieldName = adjustedFieldOrder.get(adjustedIndex);
                Tuple2<Integer, SeaTunnelDataType<?>> fieldInfo = fieldNameToInfo.get(fieldName);
                int originalIndex = fieldInfo._1;
                SeaTunnelDataType<?> seaTunnelDataType = fieldInfo._2;

                int totalIndex = atomicInteger.incrementAndGet();
                Object fieldValue = row.getField(originalIndex);

                log.debug(
                        "处理字段: {}, 原始位置: {}, 调整后位置: {}, 类型: {}, 值: {}",
                        fieldName,
                        originalIndex,
                        adjustedIndex,
                        seaTunnelDataType.getSqlType(),
                        fieldValue);

                if (fieldValue == null) {
                    statement.setNull(totalIndex, Types.NULL);
                    continue;
                }

                try {
                    switch (seaTunnelDataType.getSqlType()) {
                        case STRING:
                        case DECIMAL:
                            statement.setString(totalIndex, String.valueOf(fieldValue));
                            break;
                        case BOOLEAN:
                            statement.setBoolean(totalIndex, (Boolean) fieldValue);
                            break;
                        case TINYINT:
                            statement.setByte(totalIndex, (Byte) fieldValue);
                            break;
                        case SMALLINT:
                            statement.setShort(totalIndex, (Short) fieldValue);
                            break;
                        case INT:
                            statement.setInt(totalIndex, (Integer) fieldValue);
                            break;
                        case BIGINT:
                            statement.setLong(totalIndex, (Long) fieldValue);
                            break;
                        case FLOAT:
                            statement.setFloat(totalIndex, (Float) fieldValue);
                            break;
                        case DOUBLE:
                            statement.setDouble(totalIndex, (Double) fieldValue);
                            break;
                            //                        case DECIMAL:
                            //                            statement.setBigDecimal(totalIndex,
                            // (BigDecimal) fieldValue);
                            //                            break;
                        case DATE:
                            if (fieldValue instanceof LocalDate) {
                                statement.setDate(
                                        totalIndex, java.sql.Date.valueOf((LocalDate) fieldValue));
                            } else if (fieldValue instanceof String) {
                                statement.setDate(
                                        totalIndex,
                                        java.sql.Date.valueOf(
                                                LocalDate.parse((String) fieldValue)));
                            } else if (fieldValue instanceof Integer) {
                                // 如果是年份数字，转换为该年第一天的日期
                                statement.setDate(
                                        totalIndex,
                                        java.sql.Date.valueOf(
                                                LocalDate.of((Integer) fieldValue, 1, 1)));
                            } else {
                                throw new SQLException(
                                        "Unsupported date value type: " + fieldValue.getClass());
                            }
                            break;
                        case TIME:
                            if (fieldValue instanceof LocalTime) {
                                statement.setTime(
                                        totalIndex, java.sql.Time.valueOf((LocalTime) fieldValue));
                            } else if (fieldValue instanceof String) {
                                statement.setTime(
                                        totalIndex, java.sql.Time.valueOf((String) fieldValue));
                            } else {
                                throw new SQLException(
                                        "Unsupported time value type: " + fieldValue.getClass());
                            }
                            break;
                        case TIMESTAMP:
                            if (fieldValue instanceof LocalDateTime) {
                                statement.setTimestamp(
                                        totalIndex,
                                        java.sql.Timestamp.valueOf((LocalDateTime) fieldValue));
                            } else if (fieldValue instanceof String) {
                                statement.setTimestamp(
                                        totalIndex,
                                        java.sql.Timestamp.valueOf(
                                                LocalDateTime.parse((String) fieldValue)));
                            } else {
                                throw new SQLException(
                                        "Unsupported timestamp value type: "
                                                + fieldValue.getClass());
                            }
                            break;
                        case BYTES:
                            statement.setBytes(totalIndex, (byte[]) fieldValue);
                            break;
                        case NULL:
                            statement.setNull(totalIndex, Types.NULL);
                            break;
                        case MAP:
                        case ARRAY:
                        case ROW:
                        default:
                            throw new JdbcConnectorException(
                                    CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                                    "Unexpected value: " + seaTunnelDataType);
                    }
                } catch (Exception e) {
                    log.error(
                            "Error setting field value. Field: {}, Value: {}, Type: {}",
                            fieldName,
                            fieldValue,
                            seaTunnelDataType.getSqlType(),
                            e);
                    throw e;
                }
            }
        }
        return statement;
    }
    /*public PreparedStatement toExternal(
            TableSchema tableSchema, List<SeaTunnelRow> rows, PreparedStatement statement,List<String> partitionKeys)
            throws SQLException {
        AtomicInteger atomicInteger = new AtomicInteger(0);
        for (int i = 0; i < rows.size(); i++) {
            SeaTunnelRow row = rows.get(i);
            SeaTunnelRowType rowType = tableSchema.toPhysicalRowDataType();
            rowType.getFieldNames();
            for (int fieldIndex = 0; fieldIndex < rowType.getTotalFields(); fieldIndex++) {
                SeaTunnelDataType<?> seaTunnelDataType = rowType.getFieldType(fieldIndex);
                int totalIndex = atomicInteger.incrementAndGet();
                Object fieldValue = row.getField(fieldIndex);
                if (fieldValue == null) {
                    statement.setNull(totalIndex, java.sql.Types.NULL);
                    continue;
                }
                switch (seaTunnelDataType.getSqlType()) {
                    case STRING:
                        statement.setString(totalIndex, (String) row.getField(fieldIndex));
                        break;
                    case BOOLEAN:
                        statement.setBoolean(totalIndex, (Boolean) row.getField(fieldIndex));
                        break;
                    case TINYINT:
                        statement.setByte(totalIndex, (Byte) row.getField(fieldIndex));
                        break;
                    case SMALLINT:
                        statement.setShort(totalIndex, (Short) row.getField(fieldIndex));
                        break;
                    case INT:
                        statement.setInt(totalIndex, (Integer) row.getField(fieldIndex));
                        break;
                    case BIGINT:
                        statement.setLong(totalIndex, (Long) row.getField(fieldIndex));
                        break;
                    case FLOAT:
                        statement.setFloat(totalIndex, (Float) row.getField(fieldIndex));
                        break;
                    case DOUBLE:
                        statement.setDouble(totalIndex, (Double) row.getField(fieldIndex));
                        break;
                    case DECIMAL:
                        statement.setBigDecimal(totalIndex, (BigDecimal) row.getField(fieldIndex));
                        break;
                    case DATE:
                        LocalDate localDate = (LocalDate) row.getField(fieldIndex);
                        statement.setDate(totalIndex, java.sql.Date.valueOf(localDate));
                        break;
                    case TIME:
                        LocalTime localTime = (LocalTime) row.getField(fieldIndex);
                        statement.setTime(totalIndex, java.sql.Time.valueOf(localTime));
                        break;
                    case TIMESTAMP:
                        LocalDateTime localDateTime = (LocalDateTime) row.getField(fieldIndex);
                        statement.setTimestamp(
                                totalIndex, java.sql.Timestamp.valueOf(localDateTime));
                        break;
                    case BYTES:
                        statement.setBytes(totalIndex, (byte[]) row.getField(fieldIndex));
                        break;
                    case NULL:
                        statement.setNull(totalIndex, java.sql.Types.NULL);
                        break;
                    case MAP:
                    case ARRAY:
                    case ROW:
                    default:
                        throw new JdbcConnectorException(
                                CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                                "Unexpected value: " + seaTunnelDataType);
                }
            }
        }
        return statement;
    }*/
    /*private String getSql(String databaseName, String tableName, List<SeaTunnelRow> records, String fieldIde,List<String> partitionKeys) {
        String[] fieldNames = tableSchema.getFieldNames();
        if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toLowerCase();
        } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toUpperCase();
        }
        String name = databaseName + "." + tableName;
        String columns =
                Arrays.stream(fieldNames).map(fieldName -> {
                    if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
                        return "`"+fieldName.toLowerCase()+"`";
                    } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
                        return "`"+fieldName.toUpperCase()+"`";
                    } else {
                        return "`"+fieldName+"`";
                    }
                }).collect(Collectors.joining(", "));
        String placeholders =
                Arrays.stream(fieldNames)
                        .map(s -> "?")
                        .collect(Collectors.joining(","));
        StringBuilder values = new StringBuilder("");
        for (int i = 0; i < records.size(); i++) {
            values.append("(" + placeholders + "),");
        }
        String val = values.toString();
        val = val.substring(0, val.length() - 1);
        String sql = "INSERT INTO " + name + " (" + columns + ") VALUES " + val;
        return sql;
    }*/
    /*private String getSql(String databaseName, String tableName, List<SeaTunnelRow> records, String fieldIde, List<String> partitionKeys) {
        String[] fieldNames = tableSchema.getFieldNames();
        // 根据 fieldIde 参数处理表名和字段名的大小写
        if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toLowerCase();
        } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toUpperCase();
        }

        String name = databaseName + "." + tableName;

        // 处理字段名的大小写，并确保分区字段在最后
        List<String> allFields = new ArrayList<>(Arrays.asList(fieldNames));
        if (partitionKeys != null && !partitionKeys.isEmpty()) {
            // 移除分区字段（如果已经在字段列表中）
            allFields.removeAll(partitionKeys);
            // 将分区字段添加到字段列表的最后
            allFields.addAll(partitionKeys);
        }

        String columns = allFields.stream()
                .map(fieldName -> {
                    if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
                        return "`" + fieldName.toLowerCase() + "`";
                    } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
                        return "`" + fieldName.toUpperCase() + "`";
                    } else {
                        return "`" + fieldName + "`";
                    }
                })
                .collect(Collectors.joining(", "));

        // 生成占位符
        String placeholders = allFields.stream()
                .map(s -> "?")
                .collect(Collectors.joining(","));

        // 生成 VALUES 部分
        StringBuilder values = new StringBuilder();
        for (int i = 0; i < records.size(); i++) {
            values.append("(").append(placeholders).append("),");
        }
        String val = values.toString();
        val = val.substring(0, val.length() - 1); // 去掉最后一个逗号

        // 生成分区部分
        String partitionClause = "";
        if (partitionKeys != null && !partitionKeys.isEmpty()) {
            partitionClause = " PARTITION (" +
                    partitionKeys.stream()
                            .map(partitionKey -> {
                                if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
                                    return "`" + partitionKey.toLowerCase() + "`";
                                } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
                                    return "`" + partitionKey.toUpperCase() + "`";
                                } else {
                                    return "`" + partitionKey + "`";
                                }
                            })
                            .collect(Collectors.joining(", ")) +
                    ")";
        }

        // 生成最终的 SQL 语句
        String sql = "INSERT INTO " + name + partitionClause + " (" + columns + ") VALUES " + val;

        return sql;
    }*/
    private String getSql(
            String databaseName,
            String tableName,
            List<SeaTunnelRow> records,
            String fieldIde,
            List<String> partitionKeys) {
        String[] fieldNames = tableSchema.getFieldNames();

        // 根据 fieldIde 参数处理表名和字段名的大小写
        if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toLowerCase();
        } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
            tableName = tableName.toUpperCase();
        }

        String name = databaseName + "." + tableName;

        // 处理字段名的大小写，并确保分区字段在最后
        List<String> allFields = new ArrayList<>(Arrays.asList(fieldNames));
        if (partitionKeys != null && !partitionKeys.isEmpty()) {
            // 移除分区字段（如果已经在字段列表中）
            allFields.removeAll(partitionKeys);
            // 将分区字段添加到字段列表的最后
            allFields.addAll(partitionKeys);
        }
        // 调整 records 中的数据顺序
        List<SeaTunnelRow> adjustedRecords = new ArrayList<>();
        for (SeaTunnelRow record : records) {
            Object[] fields = record.getFields();
            Object[] adjustedFields = new Object[fields.length];
            int index = 0;

            // 先添加非分区字段
            for (String field : fieldNames) {
                if (null != partitionKeys && !partitionKeys.contains(field)) {
                    adjustedFields[index++] = fields[Arrays.asList(fieldNames).indexOf(field)];
                }
            }
            // 再添加分区字段
            for (String partitionKey : partitionKeys) {
                adjustedFields[index++] = fields[Arrays.asList(fieldNames).indexOf(partitionKey)];
            }

            adjustedRecords.add(new SeaTunnelRow(adjustedFields));
        }
        // 生成字段列表
        String columns =
                allFields.stream()
                        .map(
                                fieldName -> {
                                    if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
                                        return "`" + fieldName.toLowerCase() + "`";
                                    } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
                                        return "`" + fieldName.toUpperCase() + "`";
                                    } else {
                                        return "`" + fieldName + "`";
                                    }
                                })
                        .collect(Collectors.joining(", "));

        // 生成占位符
        String placeholders = allFields.stream().map(s -> "?").collect(Collectors.joining(","));

        // 生成 VALUES 部分
        StringBuilder values = new StringBuilder();
        for (int i = 0; i < adjustedRecords.size(); i++) {
            values.append("(").append(placeholders).append("),");
        }
        String val = values.toString();
        val = val.substring(0, val.length() - 1); // 去掉最后一个逗号

        // 生成分区部分
        String partitionClause = "";
        if (partitionKeys != null && !partitionKeys.isEmpty()) {
            partitionClause =
                    " PARTITION ("
                            + partitionKeys.stream()
                                    .map(
                                            partitionKey -> {
                                                if ("LOWERCASE".equalsIgnoreCase(fieldIde)) {
                                                    return "`" + partitionKey.toLowerCase() + "`";
                                                } else if ("UPPERCASE".equalsIgnoreCase(fieldIde)) {
                                                    return "`" + partitionKey.toUpperCase() + "`";
                                                } else {
                                                    return "`" + partitionKey + "`";
                                                }
                                            })
                                    .collect(Collectors.joining(", "))
                            + ")";
        }

        // 生成最终的 SQL 语句
        String sql = "INSERT INTO " + name + partitionClause + " (" + columns + ") VALUES " + val;

        return sql;
    }

    @Override
    public void executeBatch(boolean openTransaction) throws SQLException {
        statement.executeBatch();
        statement.clearBatch();
    }

    @Override
    public void closeStatements() throws SQLException {
        if (statement != null) {
            statement.close();
        }
    }
}
