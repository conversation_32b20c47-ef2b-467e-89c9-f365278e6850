package org.apache.seatunnel.connectors.seatunnel.jdbc.internal.executor;

import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.connectors.seatunnel.jdbc.internal.converter.JdbcRowConverter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@Slf4j
@RequiredArgsConstructor
public class DeleteBatchStatementExecutor implements JdbcBatchStatementExecutor<SeaTunnelRow> {
    private final StatementFactory existStmtFactory;
    @NonNull private final StatementFactory deleteStmtFactory;
    private final StatementFactory updateStmtFactory;
    private final TableSchema keyTableSchema;
    private final Function<SeaTunnelRow, SeaTunnelRow> keyExtractor;
    // valueTableSchema 中只包含了主键列
    @NonNull private final TableSchema valueTableSchema;

    private final TableSchema databaseTableSchema;
    @NonNull private final JdbcRowConverter rowConverter;
    private transient PreparedStatement existStatement;
    private transient PreparedStatement deleteStatement;
    private transient PreparedStatement updateStatement;
    private transient Boolean preExistFlag;
    private transient boolean submitted;

    public DeleteBatchStatementExecutor(
            StatementFactory deleteStmtFactory,
            TableSchema valueTableSchema,
            TableSchema databaseTableSchema,
            JdbcRowConverter rowConverter) {
        this(
                null,
                deleteStmtFactory,
                null,
                null,
                null,
                valueTableSchema,
                databaseTableSchema,
                rowConverter);
    }

    @Override
    public void prepareStatements(Connection connection) throws SQLException {
        if (upsertMode()) {
            existStatement = existStmtFactory.createStatement(connection);
        }
        deleteStatement = deleteStmtFactory.createStatement(connection);
    }

    @Override
    public void addToBatch(SeaTunnelRow record) throws SQLException {
        boolean exist = existRow(record);
        if (exist) {
            if (null != preExistFlag && !preExistFlag) {
                deleteStatement.executeBatch();
                deleteStatement.clearBatch();
            }
            rowConverter.toExternal(valueTableSchema, databaseTableSchema, record, deleteStatement);
            deleteStatement.addBatch();
        }
        preExistFlag = exist;
        submitted = false;
    }

    @Override
    public void executeBatch(boolean openTransaction) throws SQLException {
        if (null != preExistFlag) {
            if (preExistFlag) {
                deleteStatement.executeBatch();
                deleteStatement.clearBatch();
            }
        }
        submitted = true;
    }

    @Override
    public void closeStatements() throws SQLException {
        if (!submitted) {
            executeBatch(false);
        }
        for (PreparedStatement statement :
                Arrays.asList(existStatement, deleteStatement, updateStatement)) {
            if (null != statement) {
                statement.close();
            }
        }
    }

    private boolean upsertMode() {
        return existStmtFactory != null;
    }

    private boolean existRow(SeaTunnelRow record) throws SQLException {
        if (upsertMode()) {
            return exist(keyExtractor.apply(record));
        }
        return true;
    }

    private boolean exist(SeaTunnelRow pk) throws SQLException {
        rowConverter.toExternal(keyTableSchema, databaseTableSchema, pk, existStatement);
        try (ResultSet resultSet = existStatement.executeQuery()) {
            return resultSet.next();
        }
    }
}
