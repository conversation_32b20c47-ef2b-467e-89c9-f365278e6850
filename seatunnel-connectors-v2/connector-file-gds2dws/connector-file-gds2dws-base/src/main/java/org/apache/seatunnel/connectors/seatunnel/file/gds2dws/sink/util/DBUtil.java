package org.apache.seatunnel.connectors.seatunnel.file.gds2dws.sink.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
@Slf4j
public class DBUtil {
    public static void executeSql(
            String url, String driver, String user, String password, String sql) {
        try {
            Class.forName(driver);
            try (Connection con = DriverManager.getConnection(url, user, password);
                    PreparedStatement pstmt = con.prepareStatement(sql)) {
                pstmt.execute();
            } catch (SQLException se) {
                log.info("sql执行失败，错误原因是: {}", se.getMessage());
            }
        } catch (ClassNotFoundException cnfe) {
            log.error("Driver class not found: {}", cnfe.getMessage());
        }
    }

    public static int executeQuerySql(
            String url, String driver, String user, String password, String tableName) {
        String sql = "select count(1) from " + tableName;
        try {
            Class.forName(driver);
            try (Connection con = DriverManager.getConnection(url, user, password);
                    PreparedStatement pstmt = con.prepareStatement(sql)) {
                ResultSet resultSet = pstmt.executeQuery();
                if (resultSet.next()) {
                    return resultSet.getInt(1);
                }
            } catch (SQLException se) {
                log.info("sql执行失败，错误原因是: {}", se.getMessage());
            }
        } catch (ClassNotFoundException cnfe) {
            log.error("Driver class not found: ", cnfe);
        }
        return -1;
    }

    public static String getAdbCreateTableSQL(
            List<String> fields, String schema, String tableName, String gpfdistAddress) {
        // List<String> fields = keySet.stream().collect(Collectors.toList());
        StringBuilder sb =
                new StringBuilder("CREATE EXTERNAL TABLE " + schema + "." + tableName + " (");
        for (int i = 0; i < fields.size(); i++) {
            sb.append(fields.get(i) + " text");
            if (i < fields.size() - 1) { // 如果不是最后一个元素，则添加逗号
                sb.append(",");
            }
        }
        // sb.append(") LOCATION ('"+gpfdistAddress+"') FORMAT 'CSV' (delimiter ',')");
        sb.append(") LOCATION (");
        String[] addresses = gpfdistAddress.split(",");
        for (int i = 0; i < addresses.length; i++) {
            sb.append('\'').append(addresses[i].trim()).append('\'');
            if (i < addresses.length - 1) { // 如果不是最后一个地址，则添加逗号和单引号
                sb.append(",");
            }
        }

        sb.append(") FORMAT 'CSV' (delimiter ',')");
        return sb.toString();
    }

    public static String getInsertSql(
            String dbSchema, String tmpTableName, String tableName, List<String> fields) {
        return "insert into "
                + dbSchema
                + "."
                + tableName
                + " select "
                + String.join(",", fields)
                + " from "
                + dbSchema
                + "."
                + tmpTableName;
    }

    public static String getDropTable(String dbSchema, String tmpTableName) {
        return "drop foreign table " + dbSchema + "." + tmpTableName;
    }

    public static String getGpfdistAddress(String gpfdistAddress, String argoPrefix) {
        List<String> gpfdistAddressList =
                Arrays.stream(gpfdistAddress.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());

        // 去除 argoPrefix 开头的斜杠
        if (argoPrefix.startsWith("/")) {
            argoPrefix = argoPrefix.substring(1);
        }
        // 处理 gpfdistAddressList 中的每个地址
        String finalArgoPrefix = argoPrefix;
        String gpfdistAddr =
                gpfdistAddressList.stream()
                        .map(
                                addr -> {
                                    // 去除地址结尾的斜杠
                                    if (addr.endsWith("/")) {
                                        addr = addr.substring(0, addr.length() - 1);
                                    }
                                    // 提取端口号
                                    int portIndex = addr.lastIndexOf(':');
                                    String port = addr.substring(portIndex + 1);

                                    // 重新组合地址
                                    return addr + "/" + finalArgoPrefix;
                                })
                        .collect(Collectors.joining(","));
        return gpfdistAddr;
    }

    public static String mkdirFile(String filePath, String adbTmpFilePath) {
        File file = new File(filePath);
        log.info("当前路径下有文件{}个，这里只获取第一个", file.listFiles());
        if (file.listFiles().length > 0) {
            String path = file.listFiles()[0].getAbsolutePath();
            if (path.contains(adbTmpFilePath)) {
                return path.replace(adbTmpFilePath, "");
            }
            return path;
        }
        return null;
    }

    public static void checkFile(List<String> filePaths) {
        for (String path : filePaths) {
            File newfile = new File(path);
            while (newfile.length() <= 0) {
                try {
                    log.info("当前文件大小是{}", newfile.length());
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 保持中断状态
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void checkFile(String path) {
        File newfile = new File(path);
        while (newfile.length() <= 0) {
            try {
                log.info("当前文件大小是{}", newfile.length());
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 保持中断状态
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void deleteFile(String filePath) {
        Path path = Paths.get(filePath);
        try {
            changePermissionsRecursively(path);
            if (Files.isDirectory(path)) {
                Files.walk(path)
                        .sorted((p1, p2) -> -p1.compareTo(p2)) // 先删除子目录和文件，再删除父目录
                        .map(Path::toFile)
                        .forEach(file -> file.delete());
            } else {
                Files.delete(path);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void changePermissionsRecursively(Path path) throws IOException {
        // 设置文件或目录的权限（这里以 POSIX 权限为例）
        Set<PosixFilePermission> filePerms = new HashSet<>();
        Set<PosixFilePermission> dirPerms = new HashSet<>();

        // 文件权限：读、写
        filePerms.add(PosixFilePermission.OWNER_READ);
        filePerms.add(PosixFilePermission.OWNER_WRITE);
        filePerms.add(PosixFilePermission.OWNER_EXECUTE);
        // 目录权限：读、写、执行
        dirPerms.addAll(filePerms);
        dirPerms.add(PosixFilePermission.OWNER_EXECUTE);
        dirPerms.add(PosixFilePermission.OWNER_READ);
        dirPerms.add(PosixFilePermission.OWNER_WRITE);

        // 添加组和其他人的权限
        addGroupAndOthersPermissions(filePerms, dirPerms);

        if (Files.isDirectory(path)) {
            // 对于目录，设置目录权限并遍历其内容
            Files.setPosixFilePermissions(path, dirPerms);
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(path)) {
                for (Path entry : stream) {
                    changePermissionsRecursively(entry); // 递归调用
                }
            }
        } else {
            // 对于文件，设置文件权限
            Files.setPosixFilePermissions(path, filePerms);
        }
    }

    private static void addGroupAndOthersPermissions(
            Set<PosixFilePermission> filePerms, Set<PosixFilePermission> dirPerms) {
        // 添加读和执行权限给组和其他人（对于文件和目录）
        filePerms.add(PosixFilePermission.GROUP_READ);
        filePerms.add(PosixFilePermission.OTHERS_READ);

        dirPerms.add(PosixFilePermission.GROUP_READ);
        dirPerms.add(PosixFilePermission.GROUP_EXECUTE);
        dirPerms.add(PosixFilePermission.OTHERS_READ);
        dirPerms.add(PosixFilePermission.OTHERS_EXECUTE);
    }

    /**
     * dwsGdsAddress 需以/结尾
     *
     * @param csvFileName
     * @param tmpTableName
     * @param errorTableName
     * @param dwsGdsAddress
     * @param dwsFields
     */
    public static String getDWSCreateTable(
            String csvFileName,
            String tmpTableName,
            String errorTableName,
            String dwsGdsAddress,
            List<String> dwsFields,
            String dwsSchema) {
        if (!dwsGdsAddress.endsWith("/")) {
            dwsGdsAddress = dwsGdsAddress + "/";
        }
        StringBuffer sqlBuffer = new StringBuffer("CREATE FOREIGN TABLE ");
        sqlBuffer.append(dwsSchema + "." + tmpTableName + " (");
        for (int i = 0; i < dwsFields.size(); i++) {
            sqlBuffer.append(" " + dwsFields.get(i) + " text null ");
            if (i < dwsFields.size() - 1) {
                sqlBuffer.append(",");
            }
        }
        sqlBuffer.append(") SERVER gsmpp_server ");
        sqlBuffer.append(
                " OPTIONS("
                        + " LOCATION '"
                        + dwsGdsAddress
                        + csvFileName
                        + "',"
                        + " FORMAT 'CSV' ,"
                        + " DELIMITER ',',"
                        + " QUOTE E'\u007F',"
                        + " ENCODING 'utf8',"
                        + " HEADER 'false',"
                        + " FILL_MISSING_FIELDS 'true',"
                        + " IGNORE_EXTRA_DATA 'true'"
                        + ") "
                        + " READ ONLY"
                        + " LOG INTO "
                        + errorTableName
                        + " PER NODE REJECT LIMIT 'unlimited'");
        return sqlBuffer.toString();
    }
}
