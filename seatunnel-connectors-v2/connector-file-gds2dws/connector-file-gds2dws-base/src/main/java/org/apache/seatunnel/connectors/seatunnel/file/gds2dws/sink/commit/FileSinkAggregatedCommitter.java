/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.gds2dws.sink.commit;

import org.apache.seatunnel.api.sink.SinkAggregatedCommitter;
import org.apache.seatunnel.connectors.seatunnel.file.gds2dws.config.HadoopConf;
import org.apache.seatunnel.connectors.seatunnel.file.gds2dws.hadoop.HadoopFileSystemProxy;
import org.apache.seatunnel.connectors.seatunnel.file.gds2dws.sink.util.DBUtil;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class FileSinkAggregatedCommitter
        implements SinkAggregatedCommitter<FileCommitInfo, FileAggregatedCommitInfo> {
    protected HadoopFileSystemProxy hadoopFileSystemProxy;

    public FileSinkAggregatedCommitter(HadoopConf hadoopConf) {
        this.hadoopFileSystemProxy = new HadoopFileSystemProxy(hadoopConf);
    }

    @Override
    public List<FileAggregatedCommitInfo> commit(
            List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws IOException {
        List<FileAggregatedCommitInfo> errorAggregatedCommitInfoList = new ArrayList<>();
        List<String> files = new ArrayList<>();
        String finalFilePath = null;
        String dwsUrl = null;
        String dwsDriver = null;
        String dwsUser = null;
        String dwsPassword = null;
        String dwsTable = null;
        String dwsSchema = null;
        String dwsGdsAddress = null;
        List<String> dwsFields = null;
        boolean dwsCleanCache = true;
        if (null != aggregatedCommitInfos && aggregatedCommitInfos.size() > 0) {
            finalFilePath = aggregatedCommitInfos.get(0).getFinalFilePath();
            dwsUrl = aggregatedCommitInfos.get(0).getDwsUrl();
            dwsDriver = aggregatedCommitInfos.get(0).getDwsDriver();
            dwsUser = aggregatedCommitInfos.get(0).getDwsUser();
            dwsPassword = aggregatedCommitInfos.get(0).getDwsPassword();
            dwsTable = aggregatedCommitInfos.get(0).getDwsTable();
            dwsSchema = aggregatedCommitInfos.get(0).getDwsSchema();
            dwsGdsAddress = aggregatedCommitInfos.get(0).getDwsGdsAddress();
            dwsFields = aggregatedCommitInfos.get(0).getDwsFields();
            dwsCleanCache = aggregatedCommitInfos.get(0).isDwsCleanCache();
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                // first rename temp file
                                hadoopFileSystemProxy.renameFile(
                                        mvFileEntry.getKey(), mvFileEntry.getValue(), true);
                                files.add(mvFileEntry.getValue());
                            }
                            // second delete transaction directory
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Throwable e) {
                        log.error(
                                "commit aggregatedCommitInfo error, aggregatedCommitInfo = {} ",
                                aggregatedCommitInfo,
                                e);
                        errorAggregatedCommitInfoList.add(aggregatedCommitInfo);
                    }
                });

        if (null != aggregatedCommitInfos && aggregatedCommitInfos.size() > 0 && files.size() > 0) {
            mergeFiles(files, finalFilePath);
            // 处理dws gds信息
            gdsLoadData(
                    finalFilePath,
                    dwsUrl,
                    dwsDriver,
                    dwsUser,
                    dwsPassword,
                    dwsTable,
                    dwsSchema,
                    dwsGdsAddress,
                    dwsFields,
                    dwsCleanCache);
        }
        return errorAggregatedCommitInfoList;
    }

    private void gdsLoadData(
            String finalFilePath,
            String dwsUrl,
            String dwsDriver,
            String dwsUser,
            String dwsPassword,
            String dwsTable,
            String dwsSchema,
            String dwsGdsAddress,
            List<String> dwsFields,
            boolean dwsCleanCache) {
        log.info("开始使用gdsload数据到dws中");
        File file = new File(finalFilePath);
        String csvFileName = file.getName();
        String tmpTableName = "dsg_" + UUID.randomUUID().toString().replace("-", "");
        String errorTableName = tmpTableName + "_error";
        // 建表
        String ddlSql =
                DBUtil.getDWSCreateTable(
                        csvFileName,
                        tmpTableName,
                        errorTableName,
                        dwsGdsAddress,
                        dwsFields,
                        dwsSchema);
        log.info("获取到dws建临时表的语句是 {}", ddlSql);
        DBUtil.executeSql(dwsUrl, dwsDriver, dwsUser, dwsPassword, ddlSql);
        log.info("建表完毕，准备插入真实表中");
        // insert  into
        String insertSql = DBUtil.getInsertSql(dwsSchema, tmpTableName, dwsTable, dwsFields);
        log.info("插入语句是 {}", insertSql);
        DBUtil.executeSql(dwsUrl, dwsDriver, dwsUser, dwsPassword, insertSql);
        log.info("数据插入真实表完毕，开始清理数据,判断是否需要清理数据{}", dwsCleanCache);
        if (dwsCleanCache) {
            // 删除临时表
            String dropTabelSql = DBUtil.getDropTable(dwsSchema, tmpTableName);
            log.info("开始清理数据，删除表语句是 {}", dropTabelSql);
            DBUtil.executeSql(dwsUrl, dwsDriver, dwsUser, dwsPassword, dropTabelSql);
            // 删除文件
            log.info("临时表删除完毕，开始清理临时文件");
            file.delete();
        }
    }

    public void mergeFiles(List<String> filePaths, String outputFilePath) throws IOException {
        if (null == outputFilePath) {
            return;
        }
        File parentDir = new File(outputFilePath).getParentFile();
        if (null != parentDir) {
            parentDir.mkdirs();
        }

        try (FileOutputStream fos = new FileOutputStream(outputFilePath, false);
                FileChannel targetChannel = fos.getChannel()) {

            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (file.length() == 0) {
                    file.delete();
                    continue;
                }
                if (file.exists() && file.length() > 0) {
                    try (FileInputStream fis = new FileInputStream(file);
                            FileChannel sourceChannel = fis.getChannel()) {
                        long size = sourceChannel.size();
                        long position = 0;
                        while (position < size) {
                            long transferred =
                                    sourceChannel.transferTo(
                                            position, size - position, targetChannel);
                            position += transferred;
                        }
                        // 在每个文件之间家一个换行符以确保记录不会连接在一起
                        targetChannel.write(
                                java.nio.ByteBuffer.wrap(System.lineSeparator().getBytes()));
                    }
                    file.delete();
                }
            }
        }
    }

    /**
     * The logic about how to combine commit message.
     *
     * @param commitInfos The list of commit message.
     * @return The commit message after combine.
     */
    @Override
    public FileAggregatedCommitInfo combine(List<FileCommitInfo> commitInfos) {
        if (commitInfos == null || commitInfos.size() == 0) {
            return null;
        }
        LinkedHashMap<String, LinkedHashMap<String, String>> aggregateCommitInfo =
                new LinkedHashMap<>();
        LinkedHashMap<String, List<String>> partitionDirAndValuesMap = new LinkedHashMap<>();
        commitInfos.forEach(
                commitInfo -> {
                    LinkedHashMap<String, String> needMoveFileMap =
                            aggregateCommitInfo.computeIfAbsent(
                                    commitInfo.getTransactionDir(), k -> new LinkedHashMap<>());
                    needMoveFileMap.putAll(commitInfo.getNeedMoveFiles());
                    if (commitInfo.getPartitionDirAndValuesMap() != null
                            && !commitInfo.getPartitionDirAndValuesMap().isEmpty()) {
                        partitionDirAndValuesMap.putAll(commitInfo.getPartitionDirAndValuesMap());
                    }
                });
        return new FileAggregatedCommitInfo(
                aggregateCommitInfo,
                partitionDirAndValuesMap,
                commitInfos.get(0).getLastFilePath(),
                commitInfos.get(0).getDwsUrl(),
                commitInfos.get(0).getDwsDriver(),
                commitInfos.get(0).getDwsUser(),
                commitInfos.get(0).getDwsPassword(),
                commitInfos.get(0).getDwsTable(),
                commitInfos.get(0).getDwsSchema(),
                commitInfos.get(0).getDwsGdsAddress(),
                commitInfos.get(0).getDwsFields(),
                commitInfos.get(0).isDwsCleanCache());
    }

    /**
     * If {@link #commit(List)} failed, this method will be called (**Only** on Spark engine at
     * now).
     *
     * @param aggregatedCommitInfos The list of combine commit message.
     * @throws Exception throw Exception when abort failed.
     */
    @Override
    public void abort(List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws Exception {
        log.info("rollback aggregate commit");
        if (aggregatedCommitInfos == null || aggregatedCommitInfos.size() == 0) {
            return;
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            // rollback the file
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                if (hadoopFileSystemProxy.fileExist(mvFileEntry.getValue())
                                        && !hadoopFileSystemProxy.fileExist(mvFileEntry.getKey())) {
                                    hadoopFileSystemProxy.renameFile(
                                            mvFileEntry.getValue(), mvFileEntry.getKey(), true);
                                }
                            }
                            // delete the transaction dir
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Exception e) {
                        log.error("abort aggregatedCommitInfo error ", e);
                    }
                });
    }

    /**
     * Close this resource.
     *
     * @throws IOException throw IOException when close failed.
     */
    @Override
    public void close() throws IOException {
        hadoopFileSystemProxy.close();
    }
}
