/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.cdc.base.source.split;

import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.cdc.base.source.offset.Offset;

import io.debezium.relational.TableId;
import lombok.Getter;

@Getter
public class SnapshotSplit extends SourceSplitBase {
    private static final long serialVersionUID = 1L;
    private final TableId tableId;
    private final SeaTunnelRowType splitKeyType;
    private final Object[] splitStart;
    private final Object[] splitEnd;

    private final Offset lowWatermark;
    private final Offset highWatermark;

    public SnapshotSplit(
            String splitId,
            TableId tableId,
            SeaTunnelRowType splitKeyType,
            Object[] splitStart,
            Object[] splitEnd) {
        this(splitId, tableId, splitKeyType, splitStart, splitEnd, null, null);
    }

    public SnapshotSplit(
            String splitId,
            TableId tableId,
            SeaTunnelRowType splitKeyType,
            Object[] splitStart,
            Object[] splitEnd,
            Offset lowWatermark,
            Offset highWatermark) {
        super(splitId);
        this.tableId = tableId;
        this.splitKeyType = splitKeyType;
        this.splitStart = splitStart;
        this.splitEnd = splitEnd;
        this.lowWatermark = lowWatermark;
        this.highWatermark = highWatermark;
    }

    @Override
    public String splitId() {
        return this.splitId;
    }

    public boolean isSnapshotReadFinished() {
        return lowWatermark != null && highWatermark != null;
    }
}
