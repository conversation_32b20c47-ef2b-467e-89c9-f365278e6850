/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.hbase.utils;

import org.apache.seatunnel.connectors.seatunnel.hbase.config.HbaseParameters;
import org.apache.seatunnel.connectors.seatunnel.hbase.kerberos.MRSKerberosUtils;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HbaseConnectionUtil {
    public static Connection getHbaseConnection(HbaseParameters hbaseParameters) {

        if (null != hbaseParameters.getServerPrincipal()) {
            log.info("Hbasesource 开始kerberos认证");
            Configuration hbaseConfiguration =
                    MRSKerberosUtils.login(
                            hbaseParameters.getFilePath(),
                            hbaseParameters.getUser(),
                            hbaseParameters.getServerPrincipal());
            log.info("Hbasesource 完成kerberos认证，开始创建连接");

            // Configuration hbaseConfiguration = HBaseConfiguration.create();
            hbaseConfiguration.set("hbase.zookeeper.quorum", hbaseParameters.getZookeeperQuorum());
            hbaseConfiguration.set("hadoop.security.authentication", "kerberos");
            String keytabPath = hbaseParameters.getFilePath() + "user.keytab";
            String krb5Path = hbaseParameters.getFilePath() + "krb5.conf";
            log.info(
                    "keytabPath={},krb5Path={},user={}",
                    keytabPath,
                    krb5Path,
                    hbaseParameters.getUser());
            if (hbaseParameters.getHbaseExtraConfig() != null) {
                hbaseParameters.getHbaseExtraConfig().forEach(hbaseConfiguration::set);
            }
            try {
                MRSKerberosUtils.loginHadoop(
                        hbaseConfiguration, krb5Path, hbaseParameters.getUser(), keytabPath);
                MRSKerberosUtils.HbaseConnectionProduceFunction hbaseConf =
                        new MRSKerberosUtils.HbaseConnectionProduceFunction(hbaseConfiguration);
                return MRSKerberosUtils.loginHadoopWithKerberos(
                        hbaseConf,
                        krb5Path,
                        hbaseParameters.getUser(),
                        keytabPath,
                        hbaseParameters.getFilePath());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
            // initialize hbase connection
            /*try {
                Connection connection = ConnectionFactory.createConnection(hbaseConfiguration);
                return connection;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }*/
        } else {
            Configuration hbaseConfiguration = HBaseConfiguration.create();
            hbaseConfiguration.set("hbase.zookeeper.quorum", hbaseParameters.getZookeeperQuorum());
            try {
                Connection connection = ConnectionFactory.createConnection(hbaseConfiguration);
                return connection;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
