/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.hbase.source;

import org.apache.seatunnel.shade.com.typesafe.config.Config;

import org.apache.seatunnel.api.common.PrepareFailException;
import org.apache.seatunnel.api.common.SeaTunnelAPIErrorCode;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.apache.seatunnel.api.table.catalog.CatalogTableUtil;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.config.CheckConfigUtil;
import org.apache.seatunnel.common.config.CheckResult;
import org.apache.seatunnel.common.constants.PluginType;
import org.apache.seatunnel.connectors.seatunnel.hbase.config.HbaseParameters;
import org.apache.seatunnel.connectors.seatunnel.hbase.exception.HbaseConnectorException;
import org.apache.seatunnel.connectors.seatunnel.hbase.kerberos.MRSKerberosUtils;

import org.apache.hadoop.conf.Configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.auto.service.AutoService;
import lombok.extern.slf4j.Slf4j;

import static org.apache.seatunnel.connectors.seatunnel.hbase.config.HbaseConfig.QUERY_COLUMNS;
import static org.apache.seatunnel.connectors.seatunnel.hbase.config.HbaseConfig.TABLE;
import static org.apache.seatunnel.connectors.seatunnel.hbase.config.HbaseConfig.ZOOKEEPER_QUORUM;

@Slf4j
@AutoService(SeaTunnelSource.class)
public class HbaseSource
        implements SeaTunnelSource<SeaTunnelRow, HbaseSourceSplit, HbaseSourceState> {
    private static final Logger LOG = LoggerFactory.getLogger(HbaseSource.class);
    public static final String PLUGIN_NAME = "Hbase";
    private Config pluginConfig;
    private SeaTunnelRowType seaTunnelRowType;
    private HbaseParameters hbaseParameters;

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }

    @Override
    public void prepare(Config pluginConfig) throws PrepareFailException {
        this.pluginConfig = pluginConfig;
        CheckResult result =
                CheckConfigUtil.checkAllExists(
                        pluginConfig, ZOOKEEPER_QUORUM.key(), TABLE.key(), QUERY_COLUMNS.key());
        if (!result.isSuccess()) {
            throw new HbaseConnectorException(
                    SeaTunnelAPIErrorCode.CONFIG_VALIDATION_FAILED,
                    String.format(
                            "PluginName: %s, PluginType: %s, Message: %s",
                            getPluginName(), PluginType.SOURCE, result.getMsg()));
        }
        this.hbaseParameters = HbaseParameters.buildWithSinkConfig(pluginConfig);
        SeaTunnelRowType typeInfo;
        typeInfo = CatalogTableUtil.buildWithConfig(pluginConfig).getSeaTunnelRowType();
        this.seaTunnelRowType = typeInfo;
        if (null != hbaseParameters.getServerPrincipal()) {
            log.info("Hbasesource 开始kerberos认证");
            Configuration configuration =
                    MRSKerberosUtils.login(
                            hbaseParameters.getFilePath(),
                            hbaseParameters.getUser(),
                            hbaseParameters.getServerPrincipal());
            log.info("Hbasesource 完成kerberos认证，开始创建连接");
            configuration.set("hbase.zookeeper.quorum", hbaseParameters.getZookeeperQuorum());
            configuration.set("hadoop.security.authentication", "kerberos");
            String keytabPath = hbaseParameters.getFilePath() + "user.keytab";
            String krb5Path = hbaseParameters.getFilePath() + "krb5.conf";
            try {
                MRSKerberosUtils.loginHadoop(
                        configuration, krb5Path, hbaseParameters.getUser(), keytabPath);
                log.info(
                        "keytabPath={},krb5Path={},user={}",
                        keytabPath,
                        krb5Path,
                        hbaseParameters.getUser());
                MRSKerberosUtils.HbaseConnectionProduceFunction hbaseConf =
                        new MRSKerberosUtils.HbaseConnectionProduceFunction(configuration);
                MRSKerberosUtils.loginHadoopWithKerberos(
                        hbaseConf,
                        krb5Path,
                        hbaseParameters.getUser(),
                        keytabPath,
                        hbaseParameters
                                .getFilePath()); // ConnectionFactory.createConnection(hbaseConfiguration);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public Boundedness getBoundedness() {
        return Boundedness.BOUNDED;
    }

    @Override
    public SeaTunnelDataType<SeaTunnelRow> getProducedType() {
        return seaTunnelRowType;
    }

    @Override
    public SourceReader<SeaTunnelRow, HbaseSourceSplit> createReader(
            SourceReader.Context readerContext) throws Exception {
        return new HbaseSourceReader(hbaseParameters, readerContext, seaTunnelRowType);
    }

    @Override
    public SourceSplitEnumerator<HbaseSourceSplit, HbaseSourceState> createEnumerator(
            SourceSplitEnumerator.Context<HbaseSourceSplit> enumeratorContext) throws Exception {
        return new HbaseSourceSplitEnumerator(enumeratorContext, hbaseParameters);
    }

    @Override
    public SourceSplitEnumerator<HbaseSourceSplit, HbaseSourceState> restoreEnumerator(
            SourceSplitEnumerator.Context<HbaseSourceSplit> enumeratorContext,
            HbaseSourceState checkpointState)
            throws Exception {
        return new HbaseSourceSplitEnumerator(enumeratorContext, hbaseParameters, checkpointState);
    }
}
