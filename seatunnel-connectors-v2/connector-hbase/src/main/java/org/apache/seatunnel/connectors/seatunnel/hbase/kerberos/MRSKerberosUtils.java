package org.apache.seatunnel.connectors.seatunnel.hbase.kerberos;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.security.UserGroupInformation;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Slf4j
public class MRSKerberosUtils {
    public static final String CLIENT_CORE_FILE = "core-site.xml";
    /** Configuration file of hdfs-site.xml */
    public static final String CLIENT_HDFS_FILE = "hdfs-site.xml";

    /** Configuration file of hbase-site.xml */
    public static final String CLIENT_HBASE_FILE = "hbase-site.xml";

    private static final String USER_KEYTAB_FILE = "user.keytab";
    private static final String KRB5_CONF_FILE = "krb5.conf";
    private static final String ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME = "Client";
    private static final String ZOOKEEPER_SERVER_PRINCIPAL_KEY = "zookeeper.server.principal";
    private static final String ZK_CLIENT_CNXN_SOCKET = "zookeeper.clientCnxnSocket";
    private static final String ZK_SSL_SOCKET_CLASS = "org.apache.zookeeper.ClientCnxnSocketNetty";
    private static final String ZK_CLIENT_SECURE = "zookeeper.client.secure";

    // private static final String ZOOKEEPER_DEFAULT_SERVER_PRINCIPAL =
    // "zookeeper/hadoop.YourDomainName";

    public static Configuration login(String path, String user, String serverPrincipal) {
        try {
            log.info("开始login");
            Configuration clientConf = createClientConf(path);
            log.info("clientConf 初始化完毕");
            handleZkSslEnabled(clientConf);
            log.info("handleZkSslEnabled执行完毕");
            String userKeytabFile = path + USER_KEYTAB_FILE;
            String krb5File = path + KRB5_CONF_FILE;
            log.info("userKeytabFile = {}, krb5File= {},user={}", userKeytabFile, krb5File, user);
            LoginUtil.setJaasConf(ZOOKEEPER_DEFAULT_LOGIN_CONTEXT_NAME, user, userKeytabFile);
            log.info("setJaasConf完毕");
            LoginUtil.setZookeeperServerPrincipal(ZOOKEEPER_SERVER_PRINCIPAL_KEY, serverPrincipal);
            log.info("setZookeeperServerPrincipal完毕");
            LoginUtil.login(user, userKeytabFile, krb5File, clientConf);
            log.info("登陆完毕");
            return clientConf;
        } catch (Exception e) {
            log.error("kerberos认证出现异常...{} ", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private static Configuration createClientConf(String path) {
        Configuration conf = HBaseConfiguration.create();
        log.info("加载配置文件{}  {}", CLIENT_CORE_FILE, path + CLIENT_CORE_FILE);
        log.info("加载配置文件{}  {}", CLIENT_HDFS_FILE, path + CLIENT_HDFS_FILE);
        log.info("加载配置文件{}  {}", CLIENT_HBASE_FILE, path + CLIENT_HBASE_FILE);
        conf.addResource(new Path(path + CLIENT_CORE_FILE));
        conf.addResource(new Path(path + CLIENT_HDFS_FILE));
        conf.addResource(new Path(path + CLIENT_HBASE_FILE));
        return conf;
    }

    private static void handleZkSslEnabled(Configuration conf) {
        if (conf == null) {
            return;
        }
        boolean zkSslEnabled = conf.getBoolean("HBASE_ZK_SSL_ENABLED", false);
        log.info("zkSslEnabled  === {}", zkSslEnabled);
        if (zkSslEnabled) {
            System.setProperty(ZK_CLIENT_CNXN_SOCKET, ZK_SSL_SOCKET_CLASS);
            System.setProperty(ZK_CLIENT_SECURE, "true");
        } else {
            log.info("ZK_CLIENT_CNXN_SOCKET =={}", System.getProperty(ZK_CLIENT_CNXN_SOCKET));
            if (System.getProperty(ZK_CLIENT_CNXN_SOCKET) != null) {
                System.clearProperty(ZK_CLIENT_CNXN_SOCKET);
            }
            log.info("ZK_CLIENT_SECURE =={}", System.getProperty(ZK_CLIENT_SECURE));
            if (System.getProperty(ZK_CLIENT_SECURE) != null) {
                System.clearProperty(ZK_CLIENT_SECURE);
            }
        }
    }

    public static void loginHadoop(
            Configuration configuration,
            String krb5FilePath,
            String kerberosPrincipal,
            String kerberosKeytabPath)
            throws IOException {
        synchronized (UserGroupInformation.class) {
            System.setProperty("java.security.krb5.conf", krb5FilePath);
            UserGroupInformation.setConfiguration(configuration);
            UserGroupInformation userGroupInformation =
                    UserGroupInformation.loginUserFromKeytabAndReturnUGI(
                            kerberosPrincipal, kerberosKeytabPath);
            log.info("hadoop logging {}", userGroupInformation.getUserName());
        }
    }

    public static Connection loginHadoopWithKerberos(
            HbaseConnectionProduceFunction hbase,
            String krb5Path,
            String user,
            String keytabPath,
            String filePath)
            throws IOException {
        Configuration configuration = new Configuration();
        configuration.set("hadoop.security.authentication", "kerberos");
        System.setProperty("java.security.krb5.conf", krb5Path);
        configuration.addResource(new Path(filePath + CLIENT_CORE_FILE));
        configuration.addResource(new Path(filePath + CLIENT_HDFS_FILE));
        configuration.addResource(new Path(filePath + CLIENT_HBASE_FILE));
        try {
            return HadoopLoginFactory.loginWithKerberos(
                    configuration,
                    krb5Path,
                    user,
                    keytabPath,
                    (conf, userGroupInformation) -> hbase.connection());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static class HbaseConnectionProduceFunction {
        private Configuration conf;

        public HbaseConnectionProduceFunction(Configuration conf) {
            this.conf = conf;
        }

        public Connection connection() throws IOException {
            return ConnectionFactory.createConnection(conf);
        }
    }
}
