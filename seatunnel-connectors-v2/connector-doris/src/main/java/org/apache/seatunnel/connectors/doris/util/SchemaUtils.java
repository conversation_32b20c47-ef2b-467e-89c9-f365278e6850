/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.doris.util;

import org.apache.seatunnel.connectors.doris.rest.models.Field;
import org.apache.seatunnel.connectors.doris.rest.models.Schema;

import org.apache.doris.sdk.thrift.TScanColumnDesc;

import java.util.List;

public class SchemaUtils {

    /**
     * convert Doris return schema to inner schema struct.
     *
     * @param tscanColumnDescs Doris BE return schema
     * @return inner schema struct
     */
    public static Schema convertToSchema(List<TScanColumnDesc> tscanColumnDescs) {
        Schema schema = new Schema(tscanColumnDescs.size());
        tscanColumnDescs.stream()
                .forEach(
                        desc ->
                                schema.put(
                                        new Field(
                                                desc.getName(),
                                                desc.getType().name(),
                                                "",
                                                0,
                                                0,
                                                "")));
        return schema;
    }
}
