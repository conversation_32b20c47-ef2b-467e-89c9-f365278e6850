/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.merge.sink.commit;

import org.apache.seatunnel.api.sink.SinkAggregatedCommitter;
import org.apache.seatunnel.connectors.seatunnel.file.merge.config.HadoopConf;
import org.apache.seatunnel.connectors.seatunnel.file.merge.hadoop.HadoopFileSystemProxy;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FileSinkAggregatedCommitter
        implements SinkAggregatedCommitter<FileCommitInfo, FileAggregatedCommitInfo> {
    protected HadoopFileSystemProxy hadoopFileSystemProxy;
    private String sinkType;

    public FileSinkAggregatedCommitter(HadoopConf hadoopConf) {
        this.hadoopFileSystemProxy = new HadoopFileSystemProxy(hadoopConf);
        sinkType = hadoopConf.getHdfsNameKey();
    }

    @Override
    public List<FileAggregatedCommitInfo> commit(
            List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws IOException {
        List<FileAggregatedCommitInfo> errorAggregatedCommitInfoList = new ArrayList<>();
        List<String> files = new ArrayList<>();
        String finalFilePath = null;
        boolean overwriteFile = false;
        String validateFile = null;
        String validateContent = null;
        boolean validates = false;
        if (null != aggregatedCommitInfos && aggregatedCommitInfos.size() > 0) {
            finalFilePath = aggregatedCommitInfos.get(0).getFinalFilePath();
            overwriteFile = aggregatedCommitInfos.get(0).isOverwriteFile();
            validateFile = aggregatedCommitInfos.get(0).getValidateFile();
            validateContent = aggregatedCommitInfos.get(0).getValidateContent();
            validates = aggregatedCommitInfos.get(0).isValidates();
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                // first rename temp file
                                hadoopFileSystemProxy.renameFile(
                                        mvFileEntry.getKey(), mvFileEntry.getValue(), true);
                                files.add(mvFileEntry.getValue());
                            }
                            // second delete transaction directory
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Throwable e) {
                        log.error(
                                "commit aggregatedCommitInfo error, aggregatedCommitInfo = {} ",
                                aggregatedCommitInfo,
                                e);
                        errorAggregatedCommitInfoList.add(aggregatedCommitInfo);
                    }
                });

        // 如果是文件系统，则合并文件
        if (sinkType.startsWith("file:")) {
            if (null != aggregatedCommitInfos
                    && aggregatedCommitInfos.size() > 0
                    && files.size() > 0) {
                mergeFiles(files, finalFilePath, overwriteFile);
            } else if (StringUtils.isNotBlank(finalFilePath)) { // files为空时，生成空文件
                emptyFile(finalFilePath, overwriteFile);
            }
        }

        if (StringUtils.isNotBlank(validateFile)) {
            hadoopFileSystemProxy.writeValidateFile(
                    validateFile, validateContent, finalFilePath, validates);
        }
        return errorAggregatedCommitInfoList;
    }

    private void emptyFile(String outputFilePath, boolean overwriteFile) throws IOException {
        Path path = Paths.get(outputFilePath);
        Files.createDirectories(path.getParent());
        if (overwriteFile) { // 要覆盖时，先删除已存在的文件
            Files.deleteIfExists(path);
        }
        if (!Files.exists(path)) {
            Files.createFile(path);
        }
    }

    public void mergeFiles(List<String> filePaths, String outputFilePath, boolean overwriteFile)
            throws IOException {
        if (null == outputFilePath) {
            return;
        }
        File parentDir = new File(outputFilePath).getParentFile();
        if (null != parentDir) {
            parentDir.mkdirs();
        }

        try (FileOutputStream fos = new FileOutputStream(outputFilePath, !overwriteFile);
                FileChannel targetChannel = fos.getChannel()) {

            for (String filePath : filePaths) {
                File file = new File(filePath);
                if (file.length() == 0) {
                    file.delete();
                    continue;
                }
                if (file.exists() && file.length() > 0) {
                    try (FileInputStream fis = new FileInputStream(file);
                            FileChannel sourceChannel = fis.getChannel()) {
                        long size = sourceChannel.size();
                        long position = 0;
                        while (position < size) {
                            long transferred =
                                    sourceChannel.transferTo(
                                            position, size - position, targetChannel);
                            position += transferred;
                        }
                        // 在每个文件之间家一个换行符以确保记录不会连接在一起
                        targetChannel.write(
                                java.nio.ByteBuffer.wrap(System.lineSeparator().getBytes()));
                    }
                    file.delete();
                }
            }
        }
    }

    /*    private void mergeFile(String orgFilePath, String newFilePath) {
        try(FileOutputStream fos = new FileOutputStream(newFilePath);
            FileChannel targetChannel = fos.getChannel()){
            File file = new File(orgFilePath);
            try(FileInputStream fis = new FileInputStream(file);
            FileChannel sourceChannel = fis.getChannel()){
                long size = sourceChannel.size();
                long position = 0;
                while (position<size){
                    long transferred = sourceChannel.transferTo(position, size - position, targetChannel);
                    position += transferred;
                }
            }
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }*/

    /**
     * The logic about how to combine commit message.
     *
     * @param commitInfos The list of commit message.
     * @return The commit message after combine.
     */
    @Override
    public FileAggregatedCommitInfo combine(List<FileCommitInfo> commitInfos) {
        if (commitInfos == null || commitInfos.size() == 0) {
            return null;
        }
        LinkedHashMap<String, LinkedHashMap<String, String>> aggregateCommitInfo =
                new LinkedHashMap<>();
        LinkedHashMap<String, List<String>> partitionDirAndValuesMap = new LinkedHashMap<>();
        commitInfos.forEach(
                commitInfo -> {
                    LinkedHashMap<String, String> needMoveFileMap =
                            aggregateCommitInfo.computeIfAbsent(
                                    commitInfo.getTransactionDir(), k -> new LinkedHashMap<>());
                    needMoveFileMap.putAll(commitInfo.getNeedMoveFiles());
                    if (commitInfo.getPartitionDirAndValuesMap() != null
                            && !commitInfo.getPartitionDirAndValuesMap().isEmpty()) {
                        partitionDirAndValuesMap.putAll(commitInfo.getPartitionDirAndValuesMap());
                    }
                });
        return new FileAggregatedCommitInfo(
                aggregateCommitInfo,
                partitionDirAndValuesMap,
                commitInfos.get(0).getLastFilePath(),
                commitInfos.get(0).isOverwriteFile(),
                commitInfos.get(0).getValidateFile(),
                commitInfos.get(0).getValidateContent(),
                commitInfos.get(0).isValidates());
    }

    /**
     * If {@link #commit(List)} failed, this method will be called (**Only** on Spark engine at
     * now).
     *
     * @param aggregatedCommitInfos The list of combine commit message.
     * @throws Exception throw Exception when abort failed.
     */
    @Override
    public void abort(List<FileAggregatedCommitInfo> aggregatedCommitInfos) throws Exception {
        log.info("rollback aggregate commit");
        if (aggregatedCommitInfos == null || aggregatedCommitInfos.size() == 0) {
            return;
        }
        aggregatedCommitInfos.forEach(
                aggregatedCommitInfo -> {
                    try {
                        for (Map.Entry<String, LinkedHashMap<String, String>> entry :
                                aggregatedCommitInfo.getTransactionMap().entrySet()) {
                            // rollback the file
                            for (Map.Entry<String, String> mvFileEntry :
                                    entry.getValue().entrySet()) {
                                if (hadoopFileSystemProxy.fileExist(mvFileEntry.getValue())
                                        && !hadoopFileSystemProxy.fileExist(mvFileEntry.getKey())) {
                                    hadoopFileSystemProxy.renameFile(
                                            mvFileEntry.getValue(), mvFileEntry.getKey(), true);
                                }
                            }
                            // delete the transaction dir
                            hadoopFileSystemProxy.deleteFile(entry.getKey());
                        }
                    } catch (Exception e) {
                        log.error("abort aggregatedCommitInfo error ", e);
                    }
                });
    }

    /**
     * Close this resource.
     *
     * @throws IOException throw IOException when close failed.
     */
    @Override
    public void close() throws IOException {
        hadoopFileSystemProxy.close();
    }
}
