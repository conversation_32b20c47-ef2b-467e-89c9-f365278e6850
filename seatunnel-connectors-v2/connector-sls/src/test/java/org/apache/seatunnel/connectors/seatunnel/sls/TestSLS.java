package org.apache.seatunnel.connectors.seatunnel.sls;

import org.apache.seatunnel.connectors.seatunnel.sls.config.StartMode;

import org.junit.jupiter.api.Test;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.Consts;
import com.aliyun.openservices.log.common.ConsumerGroup;
import com.aliyun.openservices.log.common.ConsumerGroupShardCheckPoint;
import com.aliyun.openservices.log.common.FastLog;
import com.aliyun.openservices.log.common.FastLogContent;
import com.aliyun.openservices.log.common.FastLogGroup;
import com.aliyun.openservices.log.common.LogGroupData;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.Shard;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.request.PullLogsRequest;
import com.aliyun.openservices.log.request.PutLogsRequest;
import com.aliyun.openservices.log.response.ConsumerGroupCheckPointResponse;
import com.aliyun.openservices.log.response.ListConsumerGroupResponse;
import com.aliyun.openservices.log.response.ListShardResponse;
import com.aliyun.openservices.log.response.PullLogsResponse;
import lombok.SneakyThrows;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.aliyun.openservices.log.common.Consts.CursorMode.BEGIN;
import static org.apache.seatunnel.connectors.seatunnel.sls.config.StartMode.EARLIEST;

public class TestSLS {

    private static final String ENDPOINT = "http://cn-heyuan.log.aliyuncs.com";
    private static final String ACCESS_KEY_ID = "LTAI5tJNJJZyPNM5x549MDZU";
    private static final String ACCESS_KEY_SECRET = "******************************";
    private static final String PROJECT = "aliyun-product-data-1396574853001032-cn-heyuan";
    private static final String TOPIC = "test-topic";
    private static final String logStore = "test_logstore01";
    private static final String SOURCE = "SeaTunnel-Source";
    private static final Client client = new Client(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);

    @SneakyThrows
    @Test
    public void write() {
        final PutLogsRequest putLogsRequest =
                new PutLogsRequest(PROJECT, logStore, TOPIC, SOURCE, getData());
        client.PutLogs(putLogsRequest);
    }

    private List<LogItem> getData() {
        List<LogItem> log = new ArrayList<LogItem>();
        for (int i = 0; i < 10; i++) {
            LogItem logItem = new LogItem((int) (new Date().getTime() / 1000));
            logItem.PushBack("level", "info");
            logItem.PushBack("name", String.valueOf(i));
            logItem.PushBack("safe", "true");
            logItem.PushBack("message", "it's a test message" + i);
            log.add(logItem);
        }
        return log;
    }

    @Test
    public void GetLogs() {
        pullLogs("SeaTunnel-Consumer-Group", EARLIEST, BEGIN);
    }

    public void pullLogs(String consumer, StartMode cursorMode, Consts.CursorMode autoCursorReset) {
        final List<Integer> integers = listShard();
        for (Integer shardId : integers) {
            try {
                String cursor = getCursor(consumer, cursorMode, shardId, autoCursorReset);
                int iteration = 100;

                for (int i = 0; i < iteration; i++) {
                    PullLogsRequest request =
                            new PullLogsRequest(PROJECT, logStore, shardId, 1000, cursor);
                    PullLogsResponse response = client.pullLogs(request);

                    String next_cursor = response.getNextCursor();
                    System.out.print("The Next cursor:" + next_cursor);

                    List<LogGroupData> logGroups = response.getLogGroups();
                    for (LogGroupData logGroup : logGroups) {
                        FastLogGroup fastLogGroup = logGroup.GetFastLogGroup();
                        System.out.println(
                                "==============Source=============:" + fastLogGroup.getSource());
                        System.out.println(
                                "==============Topic==============:" + fastLogGroup.getTopic());
                        for (FastLog log : fastLogGroup.getLogs()) {
                            System.out.println(
                                    "==============LogTime==============:" + log.getTime());
                            List<FastLogContent> contents = log.getContents();
                            for (FastLogContent content : contents) {
                                System.out.println(content.getKey() + "====>" + content.getValue());
                            }
                        }
                    }

                    if (cursor.equals(next_cursor)) {
                        break;
                    }
                    cursor = next_cursor;
                }

            } catch (LogException e) {
                e.printStackTrace();
            }
        }
    }

    @SneakyThrows
    public String getCursor(
            String consumer, StartMode cursorMode, int shardId, Consts.CursorMode autoCursorReset) {
        switch (cursorMode) {
            case EARLIEST:
                try {
                    return client.GetCursor(PROJECT, logStore, shardId, BEGIN).GetCursor();
                } catch (LogException e) {
                    throw new RuntimeException(e);
                }
            case LATEST:
                try {
                    return client.GetCursor(PROJECT, logStore, shardId, Consts.CursorMode.END)
                            .GetCursor();
                } catch (LogException e) {
                    throw new RuntimeException(e);
                }
            case GROUP_CURSOR:
                try {
                    boolean groupExists = checkConsumerGroupExists(PROJECT, logStore, consumer);
                    if (!groupExists) {
                        createConsumerGroup(PROJECT, logStore, consumer);
                    }
                    ConsumerGroupCheckPointResponse response =
                            client.GetCheckPoint(PROJECT, logStore, consumer, shardId);
                    List<ConsumerGroupShardCheckPoint> checkpoints = response.getCheckPoints();
                    if (checkpoints.size() == 1) {
                        ConsumerGroupShardCheckPoint checkpoint = checkpoints.get(0);
                        if (!checkpoint.getCheckPoint().equals("")) {
                            return checkpoint.getCheckPoint();
                        }
                    }
                    return client.GetCursor(PROJECT, logStore, shardId, autoCursorReset)
                            .GetCursor();
                } catch (LogException e) {
                    if (e.GetErrorCode().equals("ConsumerGroupNotExist")) {
                        return client.GetCursor(PROJECT, logStore, shardId, autoCursorReset)
                                .GetCursor();
                    }
                    throw new RuntimeException(e);
                }
        }
        return null;
    }

    public List<Integer> listShard() {
        try {
            ListShardResponse res = client.ListShard(PROJECT, logStore);
            System.out.println("RequestId:" + res.GetRequestId());
            return res.GetShards().stream().map(Shard::getShardId).collect(Collectors.toList());
        } catch (LogException e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean checkConsumerGroupExists(String project, String logstore, String consumerGroup)
            throws Exception {
        ListConsumerGroupResponse response = client.ListConsumerGroup(project, logstore);
        if (response != null) {
            for (ConsumerGroup item : response.GetConsumerGroups()) {
                if (item.getConsumerGroupName().equals(consumerGroup)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void createConsumerGroup(
            final String project, final String logstore, final String consumerGroupName)
            throws LogException {
        ConsumerGroup consumerGroup = new ConsumerGroup(consumerGroupName, 100, false);
        try {
            client.CreateConsumerGroup(project, logstore, consumerGroup);
        } catch (LogException ex) {
            if ("ConsumerGroupAlreadyExist".equals(ex.GetErrorCode())) {}

            throw ex;
        }
    }
}
