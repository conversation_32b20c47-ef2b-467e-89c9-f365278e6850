/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.config;

import com.aliyun.datahub.client.model.CursorType;

public enum CursorMode {
    SUBSCRIBER("subscriber"),

    OLDEST("oldest"),

    LATEST("latest"),

    SEQUENCE("sequence"),

    SYSTEM_TIME("system_time");

    private String mode;

    CursorMode(String mode) {
        this.mode = mode;
    }

    public static CursorType convert(CursorMode cursorMode) {
        switch (cursorMode.mode) {
            case "oldest":
                return CursorType.OLDEST;
            case "latest":
                return CursorType.LATEST;
            case "sequence":
                return CursorType.SEQUENCE;
            case "system_time":
                return CursorType.SYSTEM_TIME;
            default:
                return null;
        }
    }

    @Override
    public String toString() {
        return mode;
    }
}
