/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.common.JobContext;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.apache.seatunnel.api.source.SupportParallelism;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.common.constants.JobMode;
import org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig;

import com.google.auto.service.AutoService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@AutoService(SeaTunnelSource.class)
public class DataHubSource
        implements SeaTunnelSource<SeaTunnelRow, DataHubSourceSplit, DataHubSourceState>,
                SupportParallelism {
    private JobContext jobContext;

    private final DataHubSourceConfig dataHubSourceConfig;

    public DataHubSource(ReadonlyConfig readonlyConfig) {
        dataHubSourceConfig = new DataHubSourceConfig(readonlyConfig);
    }

    @Override
    public Boundedness getBoundedness() {
        return JobMode.BATCH.equals(jobContext.getJobMode())
                ? Boundedness.BOUNDED
                : Boundedness.UNBOUNDED;
    }

    @Override
    public String getPluginName() {
        return DataHubConfig.PLUGIN_NAME;
    }

    @Override
    public List<CatalogTable> getProducedCatalogTables() {
        return Lists.newArrayList(dataHubSourceConfig.getCatalogTable());
    }

    @Override
    public SourceReader<SeaTunnelRow, DataHubSourceSplit> createReader(
            SourceReader.Context readerContext) {
        return new DataHubReader(
                dataHubSourceConfig.getMetadata(),
                dataHubSourceConfig.getDeserializationSchema(),
                readerContext);
    }

    @Override
    public SourceSplitEnumerator<DataHubSourceSplit, DataHubSourceState> createEnumerator(
            SourceSplitEnumerator.Context<DataHubSourceSplit> enumeratorContext) {
        return new DataHubSourceSplitEnumerator(
                dataHubSourceConfig.getMetadata(),
                enumeratorContext,
                dataHubSourceConfig.getDiscoveryIntervalMillis());
    }

    @Override
    public SourceSplitEnumerator<DataHubSourceSplit, DataHubSourceState> restoreEnumerator(
            SourceSplitEnumerator.Context<DataHubSourceSplit> enumeratorContext,
            DataHubSourceState checkpointState) {
        return new DataHubSourceSplitEnumerator(
                dataHubSourceConfig.getMetadata(),
                enumeratorContext,
                checkpointState,
                dataHubSourceConfig.getDiscoveryIntervalMillis());
    }

    @Override
    public void setJobContext(JobContext jobContext) {
        this.jobContext = jobContext;
    }
}
