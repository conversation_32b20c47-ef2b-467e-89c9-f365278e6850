/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.Collector;
import org.apache.seatunnel.api.source.SourceReader;
import org.apache.seatunnel.api.table.catalog.SeaTunnelDataTypeConvertorUtil;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.datahub.config.CursorMode;
import org.apache.seatunnel.connectors.seatunnel.datahub.exception.DataHubConnectorException;
import org.apache.seatunnel.connectors.seatunnel.datahub.util.DataHubTypeMapper;

import org.apache.commons.lang3.StringUtils;

import com.aliyun.datahub.client.DatahubClient;
import com.aliyun.datahub.client.DatahubClientBuilder;
import com.aliyun.datahub.client.auth.AliyunAccount;
import com.aliyun.datahub.client.common.DatahubConfig;
import com.aliyun.datahub.client.example.examples.Constant;
import com.aliyun.datahub.client.exception.DatahubClientException;
import com.aliyun.datahub.client.exception.InvalidParameterException;
import com.aliyun.datahub.client.exception.LimitExceededException;
import com.aliyun.datahub.client.exception.ResourceNotFoundException;
import com.aliyun.datahub.client.exception.SeekOutOfRangeException;
import com.aliyun.datahub.client.exception.ShardSealedException;
import com.aliyun.datahub.client.http.HttpConfig;
import com.aliyun.datahub.client.model.BlobRecordData;
import com.aliyun.datahub.client.model.CompressType;
import com.aliyun.datahub.client.model.CreateSubscriptionResult;
import com.aliyun.datahub.client.model.CursorType;
import com.aliyun.datahub.client.model.Field;
import com.aliyun.datahub.client.model.GetCursorResult;
import com.aliyun.datahub.client.model.ListShardResult;
import com.aliyun.datahub.client.model.RecordData;
import com.aliyun.datahub.client.model.RecordEntry;
import com.aliyun.datahub.client.model.ShardEntry;
import com.aliyun.datahub.client.model.SubscriptionOffset;
import com.aliyun.datahub.client.model.TupleRecordData;
import com.aliyun.datahub.clientlibrary.common.ExceptionChecker;
import com.aliyun.datahub.clientlibrary.config.ConsumerConfig;
import com.aliyun.datahub.clientlibrary.consumer.DatahubConsumer;
import com.aliyun.datahub.clientlibrary.exception.InvalidCursorException;
import com.aliyun.datahub.clientlibrary.models.ConsumeOffset;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

@Slf4j
public class DataHubReader implements SourceReader<SeaTunnelRow, DataHubSourceSplit> {

    private static final long THREAD_WAIT_TIME = 500L;
    private static final long POLL_TIMEOUT = 20000L;

    private final Context context;
    private final DataHubMetadata metadata;
    private final Set<DataHubSourceSplit> sourceSplits;
    private final LinkedBlockingQueue<DataHubSourceSplit> pendingPartitionsQueue;
    private final DeserializationSchema<SeaTunnelRow> deserializationSchema;
    private volatile boolean running = false;
    private DatahubClient dataHubClient;

    DataHubReader(
            DataHubMetadata metadata,
            DeserializationSchema<SeaTunnelRow> deserializationSchema,
            Context context) {
        this.metadata = metadata;
        this.context = context;
        this.sourceSplits = new HashSet<>();
        // this.executorService = Executors.newCachedThreadPool(r -> new Thread(r, "Kafka Source
        // Data Consumer"));
        pendingPartitionsQueue = new LinkedBlockingQueue<>();
        this.deserializationSchema = deserializationSchema;
        this.dataHubClient =
                DatahubClientBuilder.newBuilder()
                        .setDatahubConfig(
                                // Protocol可不设置，不设置默认使用PROTOBUF传输协议
                                new DatahubConfig(
                                        metadata.getEndpoint(),
                                        new AliyunAccount(
                                                metadata.getAccessId(), metadata.getAccessKey())))
                        .setHttpConfig(
                                new HttpConfig()
                                        .setCompressType(CompressType.ZSTD)
                                        .setConnTimeout(metadata.getTimeout()))
                        .build();
    }

    @Override
    public void open() {}

    @Override
    public void close() throws IOException {}

    @Override
    public void pollNext(Collector<SeaTunnelRow> output) throws Exception {

        if (!running) {
            Thread.sleep(THREAD_WAIT_TIME);
            return;
        }

        while (!pendingPartitionsQueue.isEmpty()) {
            sourceSplits.add(pendingPartitionsQueue.poll());
        }

        sourceSplits.forEach(
                source -> {
                    String curTopic = source.getTopicPartition().topic();
                    ListShardResult listShardResult =
                            dataHubClient.listShard(this.metadata.getProject(), curTopic);
                    List<ShardEntry> shardEntryList = listShardResult.getShards();
                    List<String> shardIdList = new ArrayList<>();
                    for (ShardEntry shardId : shardEntryList) {
                        shardIdList.add(shardId.getShardId());
                    }

                    // 流式模式
                    String subId = "";
                    if (StringUtils.isNotEmpty(metadata.getSubId())) {
                        subId = metadata.getSubId();
                    }
                    if (StringUtils.isEmpty(subId)) {
                        try {
                            // 创建 Subscription
                            CreateSubscriptionResult createSubscriptionResult =
                                    dataHubClient.createSubscription(
                                            metadata.getProject(),
                                            source.getTopicPartition().getTopic(),
                                            Constant.subscribtionComment);
                            subId = createSubscriptionResult.getSubId();
                            log.info(
                                    "create subscription successful，Subscription Name：{}",
                                    createSubscriptionResult.getSubId());
                        } catch (DatahubClientException e) {
                            log.error("create subscription error", e.getErrorMessage());
                        }
                    }
                    log.info("topic：{} 订阅者：{}", curTopic, subId);

                    ////////////////////////////// STEP1. 创建DatahubConsumer
                    // //////////////////////////
                    ConsumerConfig config =
                            new ConsumerConfig(
                                    metadata.getEndpoint(),
                                    metadata.getAccessId(),
                                    metadata.getAccessKey());
                    DatahubConsumer datahubConsumer =
                            new DatahubConsumer(
                                    metadata.getProject(), curTopic, subId, shardIdList, config);

                    if (Objects.nonNull(metadata.getCursorMode())
                            && !metadata.getCursorMode().equals(CursorMode.SUBSCRIBER)) {
                        CursorMode cursorMode = metadata.getCursorMode();
                        // Map<String, SubscriptionOffset> subscriptionOffsetMap =
                        // dataHubClient.getSubscriptionOffset(metadata.getProject(), curTopic,
                        // subId, shardIdList).getOffsets();

                        ConsumeOffset consumeOffset = null;
                        if (cursorMode.equals(CursorMode.OLDEST)) {
                            // 点位被重置，需要重新获取SubscriptionOffset版本信息
                            for (String shardId : shardIdList) {
                                /*subscriptionOffsetMap.get(shardId).setSequence(-1);
                                subscriptionOffsetMap.get(shardId).setTimestamp(-1);*/
                                consumeOffset = new ConsumeOffset(-1, -1, -1);
                                datahubConsumer
                                        .getShardGroupReader()
                                        .getShardReader(shardId)
                                        .setReadOffset(consumeOffset);
                            }
                            /*datahubConsumer.getMetaClient().resetSubscriptionOffset(metadata.getProject(), curTopic, subId, subscriptionOffsetMap);
                            datahubConsumer.getCoordinator().onOffsetReset();*/
                        }
                        //                        if (cursorMode.equals(CursorMode.SEQUENCE) &&
                        // Objects.nonNull(metadata.getCustomSequence())) {
                        //                            consumeOffset = new
                        // ConsumeOffset(metadata.getCustomSequence(), -1, -1);
                        //                        }
                        if ((cursorMode.equals(CursorMode.SYSTEM_TIME))
                                && Objects.nonNull(metadata.getCustomTimestamp())) {
                            for (String shardId : shardIdList) {
                                GetCursorResult oldReaderCursor =
                                        dataHubClient.getCursor(
                                                metadata.getProject(),
                                                curTopic,
                                                shardId,
                                                CursorType.OLDEST);
                                GetCursorResult latestReaderCursor =
                                        dataHubClient.getCursor(
                                                metadata.getProject(),
                                                curTopic,
                                                shardId,
                                                CursorType.LATEST);
                                if (oldReaderCursor.getTimestamp() <= metadata.getCustomTimestamp()
                                        && metadata.getCustomTimestamp()
                                                <= latestReaderCursor.getTimestamp()) {
                                    consumeOffset =
                                            new ConsumeOffset(
                                                    -1, metadata.getCustomTimestamp(), -1);
                                } else {
                                    consumeOffset =
                                            new ConsumeOffset(
                                                    -1, latestReaderCursor.getTimestamp(), -1);
                                }
                                datahubConsumer
                                        .getShardGroupReader()
                                        .getShardReader(shardId)
                                        .setReadOffset(consumeOffset);
                            }
                        }
                        if (cursorMode.equals(CursorMode.LATEST)) {
                            for (String shardId : shardIdList) {
                                GetCursorResult messageReaderCursor =
                                        dataHubClient.getCursor(
                                                metadata.getProject(),
                                                curTopic,
                                                shardId,
                                                CursorType.LATEST);
                                consumeOffset =
                                        new ConsumeOffset(
                                                -1, messageReaderCursor.getTimestamp(), -1);
                                datahubConsumer
                                        .getShardGroupReader()
                                        .getShardReader(shardId)
                                        .setReadOffset(consumeOffset);
                            }
                        }
                    } else {
                        Map<String, SubscriptionOffset> subscriptionOffsetMap =
                                dataHubClient
                                        .getSubscriptionOffset(
                                                metadata.getProject(), curTopic, subId, shardIdList)
                                        .getOffsets();
                        for (String shardId : shardIdList) {
                            if (subscriptionOffsetMap.get(shardId).getSequence() == -1) {
                                // 因为当为订阅者模式时，发现会漏掉第一条记录，因此在这里做下处理
                                ConsumeOffset consumeOffset = new ConsumeOffset(-1, -1, -1);
                                datahubConsumer
                                        .getShardGroupReader()
                                        .getShardReader(shardId)
                                        .setReadOffset(consumeOffset);
                            }
                        }
                    }

                    ///////////////////////// STEP2：循环读取数据 /////////////////////////
                    try {
                        boolean exit = false;
                        while (true) {
                            try {
                                RecordEntry record = datahubConsumer.read(3000);
                                if (record == null) {
                                    // 3s内未读取到数据，(1). 无数据 (2). 内部状态未Ready，比如协同消费暂时未分配到shard
                                    continue;
                                }

                                if (metadata.getEndTimestampMillis() != null
                                        && Boundedness.BOUNDED.equals(context.getBoundedness())
                                        && record.getSystemTime()
                                                >= metadata.getEndTimestampMillis()) {
                                    log.info(
                                            "当前topic：{} 当前分片：{} 当前记录序列号：{} 系统时间戳：{}大于等于自定义结束时间：{}，退出当前循环操作，执行下一个分片记录",
                                            curTopic,
                                            record.getShardId(),
                                            record.getSequence(),
                                            record.getSystemTime(),
                                            metadata.getEndTimestampMillis());
                                    exit = true;
                                }

                                RecordData recordData = record.getRecordData();
                                // 根据Topic为BLOB类型还是TUPLE类型进行不同的数据处理逻辑， 一种topic只有一种类型
                                if (recordData instanceof TupleRecordData) {
                                    TupleRecordData data = (TupleRecordData) recordData;
                                    List<Field> fields = data.getRecordSchema().getFields();

                                    LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
                                    SeaTunnelDataType<?>[] fieldTypes =
                                            new SeaTunnelDataType[fields.size()];

                                    for (int j = 0; j < fields.size(); j++) {
                                        Field field = fields.get(j);
                                        fieldMap.put(field.getName(), field.getType().name());
                                        fieldTypes[j] =
                                                SeaTunnelDataTypeConvertorUtil
                                                        .deserializeSeaTunnelDataType(
                                                                field.getName(),
                                                                field.getType().name());
                                    }
                                    // 获取Map中的所有键，这是一个Set
                                    Set<String> keys = fieldMap.keySet();
                                    // 将Set转换为List
                                    List<String> listOfKeys = new ArrayList<>(keys);
                                    // 将List转换为String数组
                                    String[] arrayOfKeys = listOfKeys.toArray(new String[0]);

                                    SeaTunnelRowType seaTunnelRowType =
                                            new SeaTunnelRowType(arrayOfKeys, fieldTypes);
                                    SeaTunnelRow seaTunnelRow =
                                            DataHubTypeMapper.getSeaTunnelRowData(
                                                    data, fieldMap, seaTunnelRowType);
                                    output.collect(seaTunnelRow);

                                } else {
                                    BlobRecordData data = (BlobRecordData) recordData;
                                    log.debug(
                                            "Read record. shardId:{}, seq:{}, ts:{}, batchIndex:{}, batchSize:{}, data:{}",
                                            record.getShardId(),
                                            record.getSequence(),
                                            record.getSystemTime(),
                                            record.getSegmentIndexForBatch(),
                                            record.getSegmentSizeForBatch(),
                                            new String(data.getData()));
                                }
                            } catch (DataHubConnectorException e) {
                                log.error("topic：{}，convert error", curTopic);
                            } catch (SeekOutOfRangeException e) {
                                log.error(
                                        "topic：{}，offset invalid,error info：{}",
                                        curTopic,
                                        e.getErrorMessage());
                            } catch (ResourceNotFoundException e) {
                                log.error(
                                        "topic：{}，project or topic or shard not found,error info：{}",
                                        curTopic,
                                        e.getErrorMessage());
                            } catch (ShardSealedException e) {
                                log.error(
                                        "topic：{}，shard is closed, all data has been read,error info：{}",
                                        curTopic,
                                        e.getErrorMessage());
                            } catch (LimitExceededException e) {
                                log.error(
                                        "topic：{}，maybe exceed limit, retry,error info：{}",
                                        curTopic,
                                        e.getErrorMessage());
                            } catch (InvalidParameterException | InvalidCursorException ex) {
                                // 非法游标或游标已过期，建议重新定位后开始消费
                                log.error(
                                        "topic：{}，InvalidCursor,error info：{}",
                                        curTopic,
                                        ex.getErrorMessage());
                            } catch (DatahubClientException e) {
                                if (!ExceptionChecker.isRetryableException(e)) {
                                    log.info("topic：{} Read data fail", curTopic, e);
                                    break;
                                }
                            }

                            if (exit) {
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Read data fail", e);
                    } finally {
                        // 关闭consumer相关资源
                        datahubConsumer.close();
                    }
                    /* } else {
                        // 注释的这部分代码用于处理批模式，通过sdk方式获取，区别于协同消费
                        String cursor = "";
                        // 获取cursor, 这里获取有效数据中时间最久远的record游标
                        // 注: 正常情况下，getCursor只需在初始化时获取一次，然后使用getRecords的nextCursor进行下一次读取
                        for (ShardEntry shardEntry : shardEntryList) {
                            String shardId = shardEntry.getShardId();

                            long curShardLatestSequence = -1L;
                            try {
                                String beginCursor = "";
                                if (metadata.getCursorMode().equals(CursorMode.SYSTEM_TIME)) {
                                    beginCursor = dataHubClient.getCursor(metadata.getProject(), curTopic, shardId, CursorMode.convert(metadata.getCursorMode()), metadata.getCustomTimestamp()).getCursor();
                                } else if (metadata.getCursorMode().equals(CursorMode.SEQUENCE)) {
                                    beginCursor = dataHubClient.getCursor(metadata.getProject(), curTopic, shardId, CursorMode.convert(metadata.getCursorMode()), metadata.getCustomSequence()).getCursor();
                                } else {
                                    beginCursor = dataHubClient.getCursor(metadata.getProject(), curTopic, shardId, CursorMode.convert(metadata.getCursorMode())).getCursor();
                                }

                                if (StringUtils.isEmpty(beginCursor)) {
                                    log.info("Topic:{}-Shard:{} has no data!", curTopic, shardId);
                                    return;
                                }

                                cursor = beginCursor;
                                curShardLatestSequence = dataHubClient.getCursor(metadata.getProject(), curTopic, shardId, CursorType.LATEST).getSequence();
                                log.info("当前topic：{} 当前分片：{} 最新序列号：{}", curTopic, shardId, curShardLatestSequence);


                            } catch (InvalidParameterException e) {
                                log.error("topic：{}，invalid parameter, please check your parameter", curTopic);
                            } catch (AuthorizationFailureException e) {
                                log.error("topic：{}，AK error, please check your accessId and accessKey", curTopic);
                            } catch (ResourceNotFoundException e) {
                                log.error("topic：{}，project or topic or shard not found", curTopic);
                            } catch (SeekOutOfRangeException e) {
                                log.error("topic：{}，offset invalid or has expired", curTopic);
                            } catch (DatahubClientException e) {
                                log.error("other error");
                            }

                            GetTopicResult getTopicResult = dataHubClient.getTopic(metadata.getProject(), curTopic);
                            RecordSchema recordSchema = getTopicResult.getRecordSchema();

                            boolean exit = false;
                            while (true) {
                                try {
                                    GetRecordsResult result = dataHubClient.getRecords(metadata.getProject(), curTopic, shardId, recordSchema, cursor, metadata.getRecordLimit());
                                    log.info("当前topic：{} 当前分片：{} 记录数：{}", curTopic, shardId, result.getRecordCount());

                                    List<RecordEntry> records = result.getRecords();
                                    if (records.isEmpty()) {
                                        // 无数据，sleep后读取
                                        try {
                                            Thread.sleep(1000);
                                        } catch (InterruptedException e) {
                                            throw new RuntimeException(e);
                                        }
                                        log.info("当前topic：{} 当前分片：{} 未获取到记录行，退出当前分片，继续下一分片", curTopic, shardId);
                                        break;
                                    }

                                    for (RecordEntry entry : records) {
                                        try {
                                            if (Boundedness.BOUNDED.equals(context.getBoundedness()) && entry.getSystemTime() >= metadata.getEndTimestampMillis()) {
                                                exit = true;
                                                log.info("当前topic：{} 当前分片：{} 当前记录序列号：{} 系统时间戳：{}大于等于自定义结束时间：{}，退出当前循环操作，执行下一个分片记录", curTopic, shardId, entry.getSequence(), entry.getSystemTime(), metadata.getEndTimestampMillis());
                                                break;
                                            }
                                            if (entry.getRecordData() instanceof TupleRecordData) {
                                                TupleRecordData data = (TupleRecordData) entry.getRecordData();
                                                List<Field> fields = data.getRecordSchema().getFields();

                                                LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
                                                SeaTunnelDataType<?>[] fieldTypes = new SeaTunnelDataType[fields.size()];

                                                for (int j = 0; j < fields.size(); j++) {
                                                    Field field = fields.get(j);
                                                    fieldMap.put(field.getName(), field.getType().name());
                                                    fieldTypes[j] = SeaTunnelDataTypeConvertorUtil.deserializeSeaTunnelDataType(field.getName(), field.getType().name());
                                                }
                                                // 获取Map中的所有键，这是一个Set
                                                Set<String> keys = fieldMap.keySet();
                                                // 将Set转换为List
                                                List<String> listOfKeys = new ArrayList<>(keys);
                                                // 将List转换为String数组
                                                String[] arrayOfKeys = listOfKeys.toArray(new String[0]);

                                                SeaTunnelRowType seaTunnelRowType = new SeaTunnelRowType(arrayOfKeys, fieldTypes);
                                                SeaTunnelRow seaTunnelRow =
                                                        DataHubTypeMapper.getSeaTunnelRowData(
                                                                data, fieldMap, seaTunnelRowType);
                                                output.collect(seaTunnelRow);
                                            } else {
                                                BlobRecordData data = (BlobRecordData) entry.getRecordData();
                                                String val = new String(data.getData());
                                                log.info("当前topic：{} 数据类型为：BLOB 获取到的数据：{}", curTopic, val);
                                            }

                                            if (Boundedness.BOUNDED.equals(context.getBoundedness())
                                                    && curShardLatestSequence != -1
                                                    && entry.getSequence() >= curShardLatestSequence) {
                                                //记录当前批次开始序列号和最新时间戳
                                                log.info("当前topic：{} 当前分片：{} 最后一条记录行的当前序列号：{} 当前时间戳：{}", curTopic, shardId, entry.getSequence(), entry.getLatestTime());
                                                break;
                                            }
                                        } catch (DataHubConnectorException e) {
                                            log.error("topic：{}，convert error", curTopic);
                                        } catch (SeekOutOfRangeException e) {
                                            log.error("topic：{}，offset invalid", curTopic);
                                        } catch (ResourceNotFoundException e) {
                                            log.error("topic：{}，project or topic or shard not found", curTopic);
                                        } catch (ShardSealedException e) {
                                            log.error("topic：{}，shard is closed, all data has been read", curTopic);
                                        } catch (LimitExceededException e) {
                                            log.error("topic：{}，maybe exceed limit, retry", curTopic);
                                        } catch (DatahubClientException e) {
                                            log.error("topic：{}，other error：{}", curTopic, e.getErrorMessage());
                                        }
                                    }

                                    if (exit) {
                                        break;
                                    }

                                    // 拿到下一个游标,获取下一条数据点位，继续消费
                                    cursor = result.getNextCursor();
                                } catch (Exception e) {
                                    log.error(e.getMessage());
                                    break;
                                }
                            }
                        }
                    }*/
                });
        // todo 有限流，运行完毕会关闭。
        if (metadata.getEndTimestampMillis() != null
                && Boundedness.BOUNDED.equals(context.getBoundedness())) {
            // signal to the source that we have reached the end of the data.
            context.signalNoMoreElement();
        }
    }

    @Override
    public List<DataHubSourceSplit> snapshotState(long checkpointId) throws Exception {
        return sourceSplits.stream().map(DataHubSourceSplit::copy).collect(Collectors.toList());
    }

    @Override
    public void addSplits(List<DataHubSourceSplit> splits) {
        running = true;
        splits.forEach(
                s -> {
                    try {
                        pendingPartitionsQueue.put(s);
                    } catch (InterruptedException e) {
                        try {
                            throw new Exception("分片拆分失败", e);
                        } catch (Exception ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                });
    }

    @Override
    public void handleNoMoreSplits() {
        log.info("receive no more splits message, this reader will not add new split.");
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {}
}
