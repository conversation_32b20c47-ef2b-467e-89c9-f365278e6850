/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.util;

import org.apache.seatunnel.api.table.type.ArrayType;
import org.apache.seatunnel.api.table.type.MapType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.connectors.seatunnel.datahub.exception.DataHubConnectorException;

import com.aliyun.datahub.client.model.TupleRecordData;
import lombok.extern.slf4j.Slf4j;
import scala.Char;

import java.io.Serializable;
import java.sql.Date;
import java.sql.Time;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DataHubTypeMapper implements Serializable {

    public static SeaTunnelRow getSeaTunnelRowData(
            TupleRecordData data, Map<String, String> fieldMap, SeaTunnelRowType typeInfo) {
        List<Object> fields = new ArrayList<>();
        for (int i = 0; i < fieldMap.size(); i++) {
            Object field = data.getField(i);
            fields.add(resolveObject2SeaTunnel(field, typeInfo.getFieldType(i)));
        }
        return new SeaTunnelRow(fields.toArray());
    }

    private static Object resolveObject2SeaTunnel(Object field, SeaTunnelDataType<?> fieldType) {
        if (field == null) {
            return null;
        }
        switch (fieldType.getSqlType()) {
            case ARRAY:
                ArrayList<Object> origArray = new ArrayList<>();
                ((ArrayList) field).iterator().forEachRemaining(origArray::add);
                SeaTunnelDataType<?> elementType = ((ArrayType<?, ?>) fieldType).getElementType();
                switch (elementType.getSqlType()) {
                    case STRING:
                        return origArray.toArray(new String[0]);
                    case BOOLEAN:
                        return origArray.toArray(new Boolean[0]);
                    case INT:
                        return origArray.toArray(new Integer[0]);
                    case BIGINT:
                        return origArray.toArray(new Long[0]);
                    case FLOAT:
                        return origArray.toArray(new Float[0]);
                    case DOUBLE:
                        return origArray.toArray(new Double[0]);
                    default:
                        throw new DataHubConnectorException(
                                CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                                String.format(
                                        "SeaTunnel type not support this type [%s] now",
                                        fieldType.getSqlType().name()));
                }
            case MAP:
                HashMap<Object, Object> dataMap = new HashMap<>();
                SeaTunnelDataType<?> keyType = ((MapType<?, ?>) fieldType).getKeyType();
                SeaTunnelDataType<?> valueType = ((MapType<?, ?>) fieldType).getValueType();
                HashMap<Object, Object> origDataMap = (HashMap<Object, Object>) field;
                origDataMap.forEach(
                        (key, value) -> {
                            try {
                                dataMap.put(
                                        resolveObject2SeaTunnel(key, keyType),
                                        resolveObject2SeaTunnel(value, valueType));
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                return dataMap;
            case TINYINT:
            case SMALLINT:
            case INT:
            case INTEGER:
            case FLOAT:
            case DOUBLE:
            case BIGINT:
            case BOOLEAN:
            case DECIMAL:
                return field;
            case STRING:
                if (field instanceof byte[]) {
                    return new String((byte[]) field);
                }
                if (field instanceof Char) {
                    return rtrim(String.valueOf(field));
                }
                return String.valueOf(field);
            case DATE:
                if (field instanceof LocalDate) {
                    return Date.valueOf((LocalDate) field);
                }
                return ((Date) field).toLocalDate();
            case TIME:
                return ((Time) field).toLocalTime();
            case TIMESTAMP:
                return ((java.util.Date) field)
                        .toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
            case NULL:
            default:
                throw new DataHubConnectorException(
                        CommonErrorCodeDeprecated.UNSUPPORTED_DATA_TYPE,
                        String.format(
                                "SeaTunnel type not support this type [%s] now",
                                fieldType.getSqlType().name()));
        }
    }

    private static String rtrim(String s) {
        int i = s.length() - 1;
        while (i >= 0 && Character.isWhitespace(s.charAt(i))) {
            i--;
        }
        return s.substring(0, i + 1);
    }
}
