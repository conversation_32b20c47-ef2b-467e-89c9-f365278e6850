package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.connectors.seatunnel.datahub.config.SubscriptionOffset;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/7/1
 */
@Data
public class TopicPartition implements Serializable {
    private static final long serialVersionUID = -613627415771699627L;
    private int hash = 0;
    private final int partition;
    private final String topic;
    private ConcurrentHashMap<String, SubscriptionOffset> stringSubscriptionOffsetMap;

    public TopicPartition(
            String topic,
            int partition,
            ConcurrentHashMap<
                            String,
                            org.apache.seatunnel.connectors.seatunnel.datahub.config
                                    .SubscriptionOffset>
                    stringSubscriptionOffsetMap) {
        this.partition = partition;
        this.topic = topic;
        this.stringSubscriptionOffsetMap = stringSubscriptionOffsetMap;
    }

    public int partition() {
        return this.partition;
    }

    public String topic() {
        return this.topic;
    }

    public int hashCode() {
        if (this.hash != 0) {
            return this.hash;
        } else {
            int result = 31 + this.partition;
            result = 31 * result + Objects.hashCode(this.topic);
            this.hash = result;
            return result;
        }
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else if (obj == null) {
            return false;
        } else if (this.getClass() != obj.getClass()) {
            return false;
        } else {
            TopicPartition other = (TopicPartition) obj;
            return this.partition == other.partition && Objects.equals(this.topic, other.topic);
        }
    }

    public String toString() {
        return this.topic + "-" + this.partition;
    }
}
