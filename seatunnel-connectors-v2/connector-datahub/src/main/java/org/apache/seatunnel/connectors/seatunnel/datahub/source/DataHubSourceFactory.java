/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.source.SeaTunnelSource;
import org.apache.seatunnel.api.source.SourceSplit;
import org.apache.seatunnel.api.table.connector.TableSource;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableSourceFactory;
import org.apache.seatunnel.api.table.factory.TableSourceFactoryContext;

import com.google.auto.service.AutoService;

import java.io.Serializable;

import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.ACCESS_ID;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.ACCESS_KEY;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.COMMIT_NUM;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.CURSOR_MODE;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.ENDPOINT;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.PLUGIN_NAME;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.PROJECT;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.RECORD_LIMIT;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.RETRY_TIMES;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.SCHEMA;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.SUB_ID;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.TIMEOUT;
import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.TOPIC;

@AutoService(Factory.class)
public class DataHubSourceFactory implements TableSourceFactory {
    @Override
    public String factoryIdentifier() {
        return PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder()
                .required(ENDPOINT, ACCESS_ID, ACCESS_KEY, PROJECT, TOPIC)
                .optional(
                        SCHEMA, TIMEOUT, RETRY_TIMES, CURSOR_MODE, RECORD_LIMIT, SUB_ID, COMMIT_NUM)
                .build();
    }

    @Override
    public <T, SplitT extends SourceSplit, StateT extends Serializable>
            TableSource<T, SplitT, StateT> createSource(TableSourceFactoryContext context) {
        return () -> (SeaTunnelSource<T, SplitT, StateT>) new DataHubSource(context.getOptions());
    }

    @Override
    public Class<? extends SeaTunnelSource> getSourceClass() {
        return DataHubSource.class;
    }
}
