/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.source.SourceSplit;

import lombok.Data;

import java.util.Objects;

@Data
public class DataHubSourceSplit implements SourceSplit {

    // 分片模式
    private TopicPartition topicPartition;

    public DataHubSourceSplit(TopicPartition topicPartition) {
        this.topicPartition = topicPartition;
    }

    @Override
    public String splitId() {
        return topicPartition.topic() + "-" + topicPartition.partition();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DataHubSourceSplit that = (DataHubSourceSplit) o;
        return Objects.equals(topicPartition, that.topicPartition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(topicPartition);
    }

    public DataHubSourceSplit copy() {
        return new DataHubSourceSplit(this.topicPartition);
    }
}
