/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.connectors.seatunnel.datahub.config.CursorMode;
import org.apache.seatunnel.connectors.seatunnel.datahub.config.ShardMode;
import org.apache.seatunnel.connectors.seatunnel.datahub.config.TopicSchemaConfig;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Properties;

/** DataHub consumer metadata, include topic etc. */
@Data
public class DataHubMetadata implements Serializable {

    private String endpoint;
    private String accessId;
    private String accessKey;
    private String project;
    private String topic;
    // 订阅id
    private String subId;
    // 分片模式
    private ShardMode shardMode;
    private List<String> shardIds;
    // DataHub分为Tuple和Blob数据类型
    private String dataType;
    // Tuple类型 Topic字段格式为[(fieldName,fieldType,isNull)]，多个字段以逗号隔开
    private List<TopicSchemaConfig> topicSchemaConfigList;
    private Properties properties;
    private String consumerGroup;
    private boolean commitOnCheckpoint = false;
    private CursorMode cursorMode = CursorMode.OLDEST;
    private Integer timeout;
    private Integer retryTimes;
    private Integer commitNum;
    private Integer recordLimit;
    private Long customSequence;
    private Long customTimestamp;
    private Long endTimestampMillis;
}
