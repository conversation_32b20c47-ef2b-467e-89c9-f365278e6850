/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.source.SourceSplitEnumerator;
import org.apache.seatunnel.common.config.Common;

import com.aliyun.datahub.client.DatahubClient;
import com.aliyun.datahub.client.DatahubClientBuilder;
import com.aliyun.datahub.client.auth.AliyunAccount;
import com.aliyun.datahub.client.common.DatahubConfig;
import com.aliyun.datahub.client.http.HttpConfig;
import com.aliyun.datahub.client.model.ListShardResult;
import com.aliyun.datahub.client.model.ShardEntry;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public class DataHubSourceSplitEnumerator
        implements SourceSplitEnumerator<DataHubSourceSplit, DataHubSourceState> {

    private static final String CLIENT_ID_PREFIX = "seatunnel";

    private final DataHubMetadata metadata;
    private final Context<DataHubSourceSplit> context;
    private long discoveryIntervalMillis;
    private DatahubClient dataHubClient;

    private final Map<TopicPartition, DataHubSourceSplit> pendingSplit;
    private final Map<TopicPartition, DataHubSourceSplit> assignedSplit;
    private ScheduledExecutorService executor;
    private ScheduledFuture<?> scheduledFuture;

    DataHubSourceSplitEnumerator(DataHubMetadata metadata, Context<DataHubSourceSplit> context) {
        this.metadata = metadata;
        this.context = context;
        this.assignedSplit = new HashMap<>();
        this.pendingSplit = new HashMap<>();
        initConnect();
    }

    DataHubSourceSplitEnumerator(
            DataHubMetadata metadata,
            Context<DataHubSourceSplit> context,
            DataHubSourceState sourceState) {
        this(metadata, context);
    }

    DataHubSourceSplitEnumerator(
            DataHubMetadata metadata,
            Context<DataHubSourceSplit> context,
            long discoveryIntervalMillis) {
        this(metadata, context);
        this.discoveryIntervalMillis = discoveryIntervalMillis;
    }

    DataHubSourceSplitEnumerator(
            DataHubMetadata metadata,
            Context<DataHubSourceSplit> context,
            DataHubSourceState sourceState,
            long discoveryIntervalMillis) {
        this(metadata, context, sourceState);
        this.discoveryIntervalMillis = discoveryIntervalMillis;
    }

    @Override
    public void open() {
        if (discoveryIntervalMillis > 0) {
            this.executor =
                    Executors.newScheduledThreadPool(
                            1,
                            runnable -> {
                                Thread thread = new Thread(runnable);
                                thread.setDaemon(true);
                                thread.setName("datahub-shard-dynamic-discovery");
                                return thread;
                            });
            this.scheduledFuture =
                    executor.scheduleWithFixedDelay(
                            () -> {
                                try {
                                    discoverySplits();
                                } catch (Exception e) {
                                    log.error("Dynamic discovery failure:", e);
                                }
                            },
                            discoveryIntervalMillis,
                            discoveryIntervalMillis,
                            TimeUnit.MILLISECONDS);
        }
    }

    @Override
    public void run() throws ExecutionException, InterruptedException {
        fetchPendingPartitionSplit();
        assignSplit();
    }

    @Override
    public void close() throws IOException {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(false);
            if (executor != null) {
                executor.shutdownNow();
            }
        }
    }

    private void discoverySplits() throws ExecutionException, InterruptedException {
        fetchPendingPartitionSplit();
        assignSplit();
    }

    @Override
    public void addSplitsBack(List<DataHubSourceSplit> splits, int subtaskId) {
        if (!splits.isEmpty()) {
            pendingSplit.putAll(convertToNextSplit(splits));
        }
    }

    private Map<TopicPartition, ? extends DataHubSourceSplit> convertToNextSplit(
            List<DataHubSourceSplit> splits) {
        try {
            return splits.stream()
                    .collect(
                            Collectors.toMap(
                                    DataHubSourceSplit::getTopicPartition, split -> split));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public int currentUnassignedSplitSize() {
        return pendingSplit.size();
    }

    @Override
    public void handleSplitRequest(int subtaskId) {
        // Do nothing because Kafka source push split.
    }

    @Override
    public void registerReader(int subtaskId) {
        if (!pendingSplit.isEmpty()) {
            assignSplit();
        }
    }

    @Override
    public DataHubSourceState snapshotState(long checkpointId) throws Exception {
        return new DataHubSourceState(new HashSet<>(assignedSplit.values()));
    }

    @Override
    public void notifyCheckpointComplete(long checkpointId) throws Exception {
        // Do nothing
    }

    private synchronized void assignSplit() {
        Map<Integer, List<DataHubSourceSplit>> readySplit = new HashMap<>(Common.COLLECTION_SIZE);
        for (int taskID = 0; taskID < context.currentParallelism(); taskID++) {
            readySplit.computeIfAbsent(taskID, id -> new ArrayList<>());
        }

        pendingSplit.forEach(
                (key, value) -> {
                    if (!assignedSplit.containsKey(key)) {
                        readySplit.get(getSplitOwner(key, context.currentParallelism())).add(value);
                    }
                });

        readySplit.forEach(
                (id, split) -> {
                    context.assignSplit(id, split);
                    if (discoveryIntervalMillis <= 0) {
                        context.signalNoMoreSplits(id);
                    }
                });

        assignedSplit.putAll(pendingSplit);
        pendingSplit.clear();
    }

    private static int getSplitOwner(TopicPartition tp, int numReaders) {
        int startIndex = ((tp.topic().hashCode() * 31) & 0x7FFFFFFF) % numReaders;
        return (startIndex + tp.partition()) % numReaders;
    }

    private void initConnect() {
        this.dataHubClient =
                DatahubClientBuilder.newBuilder()
                        .setDatahubConfig(
                                new DatahubConfig(
                                        metadata.getEndpoint(),
                                        new AliyunAccount(
                                                metadata.getAccessId(), metadata.getAccessKey()),
                                        true))
                        .setHttpConfig(new HttpConfig().setConnTimeout(metadata.getTimeout()))
                        .build();
    }

    private Set<DataHubSourceSplit> getTopicInfo() throws ExecutionException, InterruptedException {
        Collection<String> topics = Arrays.asList(this.metadata.getTopic().split(","));
        log.info("Discovered topics: {}", topics);

        Set<DataHubSourceSplit> dataHubSourceSplitSet = new HashSet<>();
        topics.forEach(
                topic -> {
                    ListShardResult listShardResult =
                            dataHubClient.listShard(this.metadata.getProject(), topic);
                    List<ShardEntry> shardEntryList = listShardResult.getShards();
                    TopicPartition topicPartition =
                            new TopicPartition(
                                    topic, shardEntryList.size(), new ConcurrentHashMap<>());
                    DataHubSourceSplit dataHubSourceSplit = new DataHubSourceSplit(topicPartition);
                    dataHubSourceSplitSet.add(dataHubSourceSplit);
                });
        return dataHubSourceSplitSet;
    }

    private void fetchPendingPartitionSplit() throws ExecutionException, InterruptedException {
        getTopicInfo()
                .forEach(
                        split -> {
                            if (!assignedSplit.containsKey(split.getTopicPartition())) {
                                if (!pendingSplit.containsKey(split.getTopicPartition())) {
                                    pendingSplit.put(split.getTopicPartition(), split);
                                }
                            }
                        });
    }
}
