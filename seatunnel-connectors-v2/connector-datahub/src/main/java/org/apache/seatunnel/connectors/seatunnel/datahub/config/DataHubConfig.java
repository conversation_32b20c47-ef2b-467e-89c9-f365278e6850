/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.config;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

public class DataHubConfig {

    public static final String PLUGIN_NAME = "DataHub";

    public static Option<String> ENDPOINT =
            Options.key("endpoint")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Your DataHub endpoint start with http");

    public static Option<String> ACCESS_ID =
            Options.key("accessId")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "Your DataHub accessId which cloud be access from Alibaba Cloud");

    public static Option<String> ACCESS_KEY =
            Options.key("accessKey")
                    .stringType()
                    .noDefaultValue()
                    .withDescription(
                            "Your DataHub accessKey which cloud be access from Alibaba Cloud");

    public static Option<String> PROJECT =
            Options.key("project")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Your DataHub project which is created in Alibaba Cloud");

    public static Option<String> TOPIC =
            Options.key("topic")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Your DataHub topic which is created in Alibaba Cloud");

    public static Option<Integer> TIMEOUT =
            Options.key("timeout")
                    .intType()
                    .noDefaultValue()
                    .withDescription("The max connection timeout");

    public static Option<Integer> RETRY_TIMES =
            Options.key("retryTimes")
                    .intType()
                    .noDefaultValue()
                    .withDescription("The max retry times when your client put record failed");

    /** ===================新增配置项==================== */
    public static final Option<DataHubConfig> SCHEMA =
            Options.key("schema")
                    .objectType(DataHubConfig.class)
                    .noDefaultValue()
                    .withDescription(
                            "The structure of the data, including field names and field types.");

    public static final Option<CursorMode> CURSOR_MODE =
            Options.key("cursor_mode")
                    .objectType(CursorMode.class)
                    .defaultValue(CursorMode.OLDEST)
                    .withDescription(
                            "The initial consumption pattern of consumers,there are several types:\n"
                                    + "[earliest],[group_offsets],[latest],[specific_offsets],[timestamp]");

    public static Option<String> SUB_ID =
            Options.key("subId").stringType().noDefaultValue().withDescription("");

    /** Configuration key to define the consumer's partition discovery interval, in milliseconds. */
    public static final Option<Long> KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS =
            Options.key("partition-discovery.interval-millis")
                    .longType()
                    .defaultValue(-1L)
                    .withDescription(
                            "The interval for dynamically discovering topics and partitions.");

    public static Option<Integer> COMMIT_NUM =
            Options.key("commitNum").intType().defaultValue(1024).withDescription("commit number");
    public static Option<Integer> RECORD_LIMIT =
            Options.key("recordLimit")
                    .intType()
                    .defaultValue(1024)
                    .withDescription("get record number");
    public static Option<Long> CUSTOM_SEQUENCE =
            Options.key("customSequence")
                    .longType()
                    .noDefaultValue()
                    .withDescription("游标获取方式为SEQUENCE，指定序列号");
    public static Option<String> CUSTOM_TIMESTAMP =
            Options.key("customTimestamp")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("游标获取方式为SYSTEM_TIME，指定开始时间戳");

    public static Option<String> END_TIMESTAMP_MILLIS =
            Options.key("endTimestampMillis")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("批次读取结束时间");
}
