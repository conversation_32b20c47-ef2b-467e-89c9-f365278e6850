/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.datahub.source;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.CatalogTableUtil;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.catalog.schema.TableSchemaOptions;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

import static org.apache.seatunnel.connectors.seatunnel.datahub.config.DataHubConfig.PLUGIN_NAME;

@Slf4j
public class DataHubSourceConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Getter private final DataHubMetadata metadata;

    @Getter private final CatalogTable catalogTable;

    @Getter private final long discoveryIntervalMillis;

    @Getter private final DeserializationSchema<SeaTunnelRow> deserializationSchema;

    public DataHubSourceConfig(ReadonlyConfig readonlyConfig) {
        this.metadata = createConsumerMetadata(readonlyConfig);
        this.catalogTable = createCatalogTable(readonlyConfig);
        this.discoveryIntervalMillis =
                readonlyConfig.get(DataHubConfig.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS);
        this.deserializationSchema = createDeserializationSchema(catalogTable, readonlyConfig);
    }

    private DataHubMetadata createConsumerMetadata(ReadonlyConfig readonlyConfig) {
        DataHubMetadata consumerMetadata = new DataHubMetadata();
        consumerMetadata.setEndpoint(readonlyConfig.get(DataHubConfig.ENDPOINT));
        consumerMetadata.setAccessId(readonlyConfig.get(DataHubConfig.ACCESS_ID));
        consumerMetadata.setAccessKey(readonlyConfig.get(DataHubConfig.ACCESS_KEY));
        consumerMetadata.setProject(readonlyConfig.get(DataHubConfig.PROJECT));
        consumerMetadata.setTopic(readonlyConfig.get(DataHubConfig.TOPIC));
        consumerMetadata.setSubId(readonlyConfig.get(DataHubConfig.SUB_ID));
        consumerMetadata.setRetryTimes(readonlyConfig.get(DataHubConfig.RETRY_TIMES));
        consumerMetadata.setTimeout(readonlyConfig.get(DataHubConfig.TIMEOUT));
        consumerMetadata.setCommitNum(readonlyConfig.get(DataHubConfig.COMMIT_NUM));
        consumerMetadata.setRecordLimit(readonlyConfig.get(DataHubConfig.RECORD_LIMIT));
        consumerMetadata.setCustomSequence(readonlyConfig.get(DataHubConfig.CUSTOM_SEQUENCE));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            if (StringUtils.isNotEmpty(readonlyConfig.get(DataHubConfig.CUSTOM_TIMESTAMP))) {
                long customTimestamp =
                        formatter
                                .parse(readonlyConfig.get(DataHubConfig.CUSTOM_TIMESTAMP))
                                .getTime();
                consumerMetadata.setCustomTimestamp(customTimestamp);
            }
            if (StringUtils.isNotEmpty(readonlyConfig.get(DataHubConfig.END_TIMESTAMP_MILLIS))) {
                long endTimestamp =
                        formatter
                                .parse(readonlyConfig.get(DataHubConfig.END_TIMESTAMP_MILLIS))
                                .getTime();
                consumerMetadata.setEndTimestampMillis(endTimestamp);
            }
        } catch (ParseException e) {
            log.error("时间格式转换异常：{}", e.getMessage());
        }
        consumerMetadata.setProperties(new Properties());
        // parse cursor mode
        readonlyConfig
                .getOptional(DataHubConfig.CURSOR_MODE)
                .ifPresent(consumerMetadata::setCursorMode);
        return consumerMetadata;
    }

    private CatalogTable createCatalogTable(ReadonlyConfig readonlyConfig) {
        Optional<Map<String, Object>> schemaOptions =
                readonlyConfig.getOptional(TableSchemaOptions.SCHEMA);
        if (schemaOptions.isPresent()) {
            return CatalogTableUtil.buildWithConfig(readonlyConfig);
        } else {
            TableIdentifier tableIdentifier = TableIdentifier.of(PLUGIN_NAME, null, null);
            TableSchema tableSchema =
                    TableSchema.builder()
                            .column(
                                    PhysicalColumn.of(
                                            "content",
                                            new SeaTunnelRowType(
                                                    new String[] {"content"},
                                                    new SeaTunnelDataType<?>[] {
                                                        BasicType.STRING_TYPE
                                                    }),
                                            0,
                                            false,
                                            null,
                                            null))
                            .build();
            return CatalogTable.of(
                    tableIdentifier,
                    tableSchema,
                    Collections.emptyMap(),
                    Collections.emptyList(),
                    null);
        }
    }

    private DeserializationSchema<SeaTunnelRow> createDeserializationSchema(
            CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        SeaTunnelRowType seaTunnelRowType = catalogTable.getSeaTunnelRowType();
        return null;
    }
}
