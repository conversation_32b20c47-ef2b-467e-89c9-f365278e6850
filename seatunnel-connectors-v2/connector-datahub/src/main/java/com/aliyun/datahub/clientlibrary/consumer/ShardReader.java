package com.aliyun.datahub.clientlibrary.consumer;

import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aliyun.datahub.client.common.ErrorCode;
import com.aliyun.datahub.client.exception.DatahubClientException;
import com.aliyun.datahub.client.exception.InvalidParameterException;
import com.aliyun.datahub.client.exception.SeekOutOfRangeException;
import com.aliyun.datahub.client.metircs.ClientMetrics;
import com.aliyun.datahub.client.model.CursorType;
import com.aliyun.datahub.client.model.GetCursorResult;
import com.aliyun.datahub.client.model.GetRecordsResult;
import com.aliyun.datahub.client.model.RecordEntry;
import com.aliyun.datahub.clientlibrary.common.Constants;
import com.aliyun.datahub.clientlibrary.common.Timer;
import com.aliyun.datahub.clientlibrary.exception.InvalidCursorException;
import com.aliyun.datahub.clientlibrary.exception.TimeoutException;
import com.aliyun.datahub.clientlibrary.interceptor.ConsumerInterceptor;
import com.aliyun.datahub.clientlibrary.models.ConsumeOffset;
import com.aliyun.datahub.clientlibrary.models.MessageKey;
import com.codahale.metrics.Counter;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024/7/9
 */
public class ShardReader {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShardReader.class);
    private static final int MIN_TIMEOUT_MS_WAIT_FETCH = 2000;
    private volatile boolean closed;

    private final String shardId;
    private final int fetchNumber;
    private ConsumeOffset readOffset;
    private final MessageReader messageReader;
    private final OffsetCoordinator coordinator;

    private final Counter cachedRecordCounter;
    private Future<?> pendingFuture;
    private final Object fetchLock = new Object();
    private final ConcurrentLinkedDeque<CompletedFetch> completedFetches =
            new ConcurrentLinkedDeque<>();
    private final AtomicLong remainRecords = new AtomicLong(0);

    public ShardReader(OffsetCoordinator coordinator, String shardId, ConsumeOffset offset) {
        this.coordinator = coordinator;
        this.shardId = shardId;
        this.readOffset = offset;
        this.fetchNumber =
                Math.min(coordinator.getFetchNumber(), coordinator.getMaxBufferRecords());
        this.messageReader = (MessageReader) coordinator.getMetaData().getDataClient();
        this.cachedRecordCounter =
                ClientMetrics.getMetricProxy()
                        .getCounter(
                                Constants.READER_CACHED_RECORDS,
                                ClientMetrics.genMetricKey(
                                        coordinator.getProjectName(),
                                        coordinator.getTopicName(),
                                        coordinator.getSubId()));
    }

    public RecordEntry read(long timeoutMs) {
        if (closed) {
            throw new InvalidParameterException(
                    "ShardReader closed. key:" + coordinator.getUniqKey() + ", shardId:" + shardId);
        }
        RecordEntry record = readNext(new Timer(timeoutMs));
        if (record != null) {
            MessageKey messageKey =
                    new MessageKey(
                            shardId,
                            new ConsumeOffset(
                                    record.getSequence(),
                                    record.getSystemTime(),
                                    record.getSegmentIndexForBatch()));
            record.setKey(messageKey);
            coordinator.recordOffset(messageKey);
        }
        return record;
    }

    private synchronized RecordEntry readNext(Timer timer) {
        long expectTimeout = timer.getTimeoutMs();
        if (completedFetches.peek() == null && expectTimeout < MIN_TIMEOUT_MS_WAIT_FETCH) {
            // if no data, and user timeout is too small, reset timeout for wait read data
            timer = new Timer(MIN_TIMEOUT_MS_WAIT_FETCH);
        }

        RecordEntry record = null;
        do {
            CompletedFetch doneFetch = completedFetches.peek();
            if (doneFetch == null) {
                sendFetchAndWait(timer);
            } else {
                CompletedFetch.CompleteType type = doneFetch.type;
                if (type == CompletedFetch.CompleteType.T_EXCEPTION) {
                    completedFetches.poll();
                    if (ErrorCode.INVALID_CURSOR.equalsIgnoreCase(
                            doneFetch.exception.getErrorCode())) {
                        throw new InvalidCursorException(doneFetch.exception);
                    }
                    throw doneFetch.exception;
                } else if (type == CompletedFetch.CompleteType.T_DELAY) {
                    // if read end, wait for next read
                    if (doneFetch.timer.isExpired()) {
                        completedFetches.poll();
                    } else {
                        long needWaitTime = expectTimeout - doneFetch.timer.getElapsedMs();
                        if (needWaitTime > 0) {
                            doneFetch.timer.waitExpire(needWaitTime);
                            completedFetches.poll();
                        } else {
                            return null;
                        }
                    }
                } else {
                    record = doneFetch.getNext();
                    if (record == null) {
                        completedFetches.poll();
                    } else {
                        remainRecords.decrementAndGet();
                        if (cachedRecordCounter != null) {
                            cachedRecordCounter.dec();
                        }
                        // send async fetch if need before the record return
                        sendFetchIfNeed();
                    }
                }
            }
        } while (!closed && record == null && !timer.isExpired());

        return record;
    }

    private void sendFetchAndWait(Timer timer) {
        sendNextFetch();
        try {
            timer.waitObject(this, () -> completedFetches.size() > 0);
        } catch (TimeoutException e) {
            // ignore
        }
    }

    private void sendFetchIfNeed() {
        if (!closed && remainRecords.get() < coordinator.getMaxBufferRecords()) {
            // only last fetch is normal fetch, then send next fetch
            CompletedFetch tailFetch = completedFetches.peekLast();
            if (tailFetch != null && tailFetch.type == CompletedFetch.CompleteType.T_NORMAL) {
                sendNextFetch();
            }
        }
    }

    private void sendNextFetch() {
        synchronized (fetchLock) {
            if (pendingFuture == null || pendingFuture.isDone()) {
                FetchTask task = genNextFetchTask();
                try {
                    pendingFuture = messageReader.sendFetches(task);
                    LOGGER.debug(
                            "Send async fetch success. key:{}, shardId:{}, cursor:{}, remain:{}",
                            task.coordinator.getUniqKey(),
                            task.shardId,
                            readOffset.getNextCursor(),
                            remainRecords.get());
                } catch (Exception e) {
                    LOGGER.warn(
                            "Send async fetch fail. key:{}, shardId:{}, cursor:{}, remain:{}",
                            task.coordinator.getUniqKey(),
                            task.shardId,
                            readOffset.getNextCursor(),
                            remainRecords.get(),
                            e);
                    throw new DatahubClientException(e.getMessage(), e);
                }
            }
        }
    }

    private void notifyFetchWait() {
        synchronized (this) {
            notifyAll();
        }
    }

    private FetchTask genNextFetchTask() {
        FetchTask task = new FetchTask(this, fetchNumber);
        task.addListener(
                new FetchTaskListener<GetRecordsResult>() {
                    @Override
                    public void onSuccess(GetRecordsResult result) {
                        if (result.getRecordCount() == 0) {
                            LOGGER.debug(
                                    "Shard read end, will retry in {} ms. key:{}, shardId:{}, cursor:{}",
                                    Constants.CFG_DELAY_TIMEOUT_FOR_READ_END,
                                    coordinator.getUniqKey(),
                                    shardId,
                                    result.getNextCursor());
                            completedFetches.add(
                                    new CompletedFetch(Constants.CFG_DELAY_TIMEOUT_FOR_READ_END));
                            readOffset.setNextCursor(result.getNextCursor());
                        } else {
                            LOGGER.debug(
                                    "Read records success. key:{}, shardId:{}, count:{}, nextCursor:{}",
                                    coordinator.getUniqKey(),
                                    shardId,
                                    result.getRecordCount(),
                                    result.getNextCursor());

                            // check whether filter to start batch index
                            List<RecordEntry> records = filterToBatchIndex(result.getRecords());
                            for (ConsumerInterceptor interceptor :
                                    coordinator.getConsumerInterceptors()) {
                                try {
                                    records = interceptor.onConsume(shardId, records);
                                } catch (Exception e) {
                                    LOGGER.warn(
                                            "Consumer interceptor fail, shardId:{}, readCursor:{}",
                                            shardId,
                                            readOffset.getNextCursor(),
                                            e);
                                }
                            }
                            completedFetches.add(new CompletedFetch(records));
                            remainRecords.addAndGet(records.size());
                            readOffset.setNextCursor(result.getNextCursor());
                            if (cachedRecordCounter != null) {
                                cachedRecordCounter.inc(records.size());
                            }
                        }
                        notifyFetchWait();
                    }

                    @Override
                    public void onFailure(DatahubClientException e) {
                        completedFetches.add(new CompletedFetch(e));
                        notifyFetchWait();
                    }
                });
        return task;
    }

    private List<RecordEntry> filterToBatchIndex(List<RecordEntry> records) {
        long startSeq = readOffset.getSequence();
        long startBatchIndex = readOffset.getBatchIndex();

        int index = 0;
        for (; index < records.size(); ++index) {
            RecordEntry record = records.get(index);
            // record sequence must be bigger or equal than startSeq
            if (record.getSequence() > startSeq) {
                break;
            }
            if (record.getSequence() == startSeq
                    && record.getSegmentIndexForBatch() > startBatchIndex) {
                break;
            }
        }
        return index > 0 ? records.subList(index, records.size()) : records;
    }

    public void resetOffset() {
        readOffset.resetTimestamp(-1);
    }

    public void setReadOffset(ConsumeOffset offset) {
        this.readOffset = offset;
    }

    public String getShardId() {
        return shardId;
    }

    public void close() {
        this.closed = true;
        LOGGER.info("ShardReader closed. key:{}, shardId:{}", coordinator.getUniqKey(), shardId);
    }

    private static class FetchTask implements Runnable {
        private final String shardId;
        private final int fetchNumber;
        private final OffsetCoordinator coordinator;
        private final ShardReader reader;
        private FetchTaskListener<GetRecordsResult> listener;

        public FetchTask(ShardReader reader, int fetchNumber) {
            this.reader = reader;
            this.shardId = reader.shardId;
            this.coordinator = reader.coordinator;
            this.fetchNumber = fetchNumber;
        }

        public void addListener(FetchTaskListener<GetRecordsResult> listener) {
            this.listener = listener;
        }

        @Override
        public void run() {
            String nextCursor = "";
            try {
                nextCursor = initCursor();
                if (StringUtils.isNotEmpty(nextCursor)) {
                    GetRecordsResult result =
                            reader.messageReader.read(
                                    shardId, nextCursor, fetchNumber, coordinator.getFilterStr());
                    this.listener.onSuccess(result);
                }
            } catch (DatahubClientException e) {
                this.listener.onFailure(e);
                LOGGER.warn(
                        "FetchTask throw exception. key:{}, shardId:{}",
                        coordinator.getUniqKey(),
                        shardId,
                        e);
                /*if (e instanceof InvalidParameterException | e instanceof InvalidCursorException) {
                    String latestCursor = "";
                    if (StringUtils.isNotEmpty(e.getErrorMessage())) {
                        if (e.getErrorMessage().contains("[") && e.getErrorMessage().contains("]")) {
                            // 找到范围的开始索引
                            int startIndex = e.getErrorMessage().indexOf('[');
                            // 找到范围的结束索引
                            int endIndex = e.getErrorMessage().indexOf(']');

                            // 截取范围值
                            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                                String rangeValue = e.getErrorMessage().substring(startIndex + 1, endIndex);
                                LOGGER.warn("截取的范围值：" + rangeValue);
                                if (StringUtils.isNotEmpty(rangeValue) && rangeValue.contains(",")) {
                                    latestCursor = rangeValue.split(",")[1].trim();
                                }
                            } else {
                                LOGGER.warn("未找到有效的范围值:{}", e.getErrorMessage());
                            }
                        }
                    }
                    LOGGER.debug("key：{} 重置cursor：{}", coordinator.getUniqKey(), latestCursor);
                    GetRecordsResult recordsResult = reader.messageReader.read(shardId, latestCursor, fetchNumber, coordinator.getFilterStr());
                    this.listener.onSuccess(recordsResult);
                }*/
            } catch (Exception e) {
                this.listener.onFailure(new DatahubClientException(e.getMessage(), e));
                LOGGER.warn(
                        "FetchTask throw exception. key:{}, shardId:{}",
                        coordinator.getUniqKey(),
                        shardId,
                        e);
            }
        }

        private String initCursor() {
            ConsumeOffset offset = reader.readOffset;
            if (offset.getNextCursor() != null && !offset.getNextCursor().equals("null")) {
                return offset.getNextCursor();
            }

            GetCursorResult getCursorResult = null;
            CursorType seekType = CursorType.OLDEST;
            // seek by sequence
            if (offset.getSequence() != -1) {
                seekType = CursorType.SEQUENCE;
                getCursorResult = seekCursor(seekType, offset.getSequence());
            }
            // seek by timestamp
            if (getCursorResult == null && offset.getTimestamp() != -1) {
                seekType = CursorType.SYSTEM_TIME;
                /*GetCursorResult messageReaderCursor = reader.messageReader.getCursor(shardId, CursorType.LATEST, -1);
                if (messageReaderCursor.getSequence() <= 0) {
                    //表示该分片无数据
                    LOGGER.debug("key:{} shard:{} 无数据", coordinator.getUniqKey(), shardId);
                } else if (offset.getTimestamp() > messageReaderCursor.getTimestamp()) {
                    LOGGER.debug("key:{} shard:{} timestamp:{} sequence:{}", coordinator.getUniqKey(), shardId, messageReaderCursor.getTimestamp(), messageReaderCursor.getSequence());
                    getCursorResult = seekCursor(seekType, messageReaderCursor.getTimestamp());
                } else {*/
                getCursorResult = seekCursor(seekType, offset.getTimestamp());
                // }
            }
            // seek by oldest
            if (getCursorResult == null) {
                seekType = CursorType.OLDEST;
                getCursorResult = seekCursor(seekType, -1);
            }

            if (getCursorResult == null) {
                throw new DatahubClientException(
                        "initCursor failed. key:" + coordinator.getUniqKey());
            }
            LOGGER.info(
                    "Init cursor success. key:{}, shardId:{}, seekType:{}, offset:{}, nextCursor:{}",
                    coordinator.getUniqKey(),
                    shardId,
                    seekType.name(),
                    offset.toString(),
                    getCursorResult.getCursor());

            // reset back reader offset
            reader.readOffset.updateWithGetCursorResult(getCursorResult);
            return getCursorResult.getCursor();
        }

        private GetCursorResult seekCursor(CursorType type, long param) {
            try {
                return reader.messageReader.getCursor(shardId, type, param);
            } catch (SeekOutOfRangeException e) {
                return null;
            }
        }
    }

    private static class CompletedFetch {
        public enum CompleteType {
            /** Normal read state */
            T_NORMAL,
            /** Read with exception */
            T_EXCEPTION,
            /** Need retry with delay */
            T_DELAY,
        }

        private final CompleteType type;
        private Timer timer;
        private DatahubClientException exception;
        private Iterator<RecordEntry> records;

        public CompletedFetch(List<RecordEntry> records) {
            this.type = CompleteType.T_NORMAL;
            if (records != null && !records.isEmpty()) {
                this.records = records.iterator();
            }
        }

        public CompletedFetch(DatahubClientException e) {
            this.type = CompleteType.T_EXCEPTION;
            this.exception = e;
        }

        public CompletedFetch(long timeoutMs) {
            this.type = CompleteType.T_DELAY;
            this.timer = new Timer(timeoutMs);
        }

        public RecordEntry getNext() {
            if (records != null && records.hasNext()) {
                RecordEntry record = records.next();
                records.remove();
                return record;
            }
            return null;
        }
    }

    public interface FetchTaskListener<T> {
        void onSuccess(T value);

        void onFailure(DatahubClientException e);
    }
}
