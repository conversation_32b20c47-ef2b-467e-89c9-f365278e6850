package org.apache.seatunnel.connectors.seatunnel.console.hole.sink;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.sink.SupportMultiTableSink;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSimpleSink;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSinkWriter;

import java.io.IOException;

import static org.apache.seatunnel.connectors.seatunnel.console.hole.sink.ConsoleHoleSinkFactory.LOG_PRINT_DATA;
import static org.apache.seatunnel.connectors.seatunnel.console.hole.sink.ConsoleHoleSinkFactory.LOG_PRINT_DELAY;

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
public class ConsoleHoleSink extends AbstractSimpleSink<SeaTunnelRow, Void>
        implements SupportMultiTableSink {
    private final SeaTunnelRowType seaTunnelRowType;
    private final boolean isPrintData;
    private final int delayMs;

    public ConsoleHoleSink(SeaTunnelRowType seaTunnelRowType, ReadonlyConfig options) {
        this.seaTunnelRowType = seaTunnelRowType;
        this.isPrintData = options.get(LOG_PRINT_DATA);
        this.delayMs = options.get(LOG_PRINT_DELAY);
    }

    @Override
    public String getPluginName() {
        return "ConsoleHole";
    }

    @Override
    public AbstractSinkWriter<SeaTunnelRow, Void> createWriter(SinkWriter.Context context)
            throws IOException {
        return new ConsoleHoleSinkWriter(seaTunnelRowType, context, isPrintData, delayMs);
    }
}
