package org.apache.seatunnel.connectors.seatunnel.file.source.reader;

import org.apache.seatunnel.api.source.Collector;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.PrimitiveByteArrayType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.file.config.BaseSourceConfigOptions;
import org.apache.seatunnel.connectors.seatunnel.file.config.HadoopConf;
import org.apache.seatunnel.connectors.seatunnel.file.exception.FileConnectorException;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/11/5
 */
public class BinaryReadStrategy extends AbstractReadStrategy {

    public static SeaTunnelRowType binaryRowType =
            new SeaTunnelRowType(
                    new String[] {"data", "relativePath", "partIndex"},
                    new SeaTunnelDataType[] {
                        PrimitiveByteArrayType.INSTANCE, BasicType.STRING_TYPE, BasicType.LONG_TYPE
                    });

    private File basePath;
    private String filePath;

    @Override
    public void init(HadoopConf conf) {
        super.init(conf);
        basePath = new File(pluginConfig.getString(BaseSourceConfigOptions.FILE_PATH.key()));
        filePath = pluginConfig.getString(BaseSourceConfigOptions.FILE_PATH.key());
    }

    @Override
    public void read(String path, String tableId, Collector<SeaTunnelRow> output)
            throws IOException, FileConnectorException {
        try (InputStream inputStream = hadoopFileSystemProxy.getInputStream(path)) {
            String relativePath;
            // linux下可以这样判断，windows下不能使用这个方法。
            /*if (hadoopFileSystemProxy.isFile(basePath.getAbsolutePath())) {*/
            if (basePath.isFile()) {
                relativePath = basePath.getName();
            } else {
                /*relativePath =
                path.substring(
                        path.indexOf(basePath.getAbsolutePath())
                                + basePath.getAbsolutePath().length());*/
                relativePath = path.substring(path.indexOf(filePath) + filePath.length());
                if (StringUtils.isBlank(relativePath)) {
                    // 这时候是文件同步
                    relativePath = basePath.getName();
                }
                if (relativePath.startsWith(File.separator)) {
                    relativePath = relativePath.substring(File.separator.length());
                }
            }
            // TODO config this size
            int maxSize = 1024;
            byte[] buffer = new byte[maxSize];
            long partIndex = 0;
            int readSize;
            while ((readSize = inputStream.read(buffer)) != -1) {
                if (readSize != maxSize) {
                    buffer = Arrays.copyOf(buffer, readSize);
                }
                SeaTunnelRow row = new SeaTunnelRow(new Object[] {buffer, relativePath, partIndex});
                buffer = new byte[1024];
                output.collect(row);
                partIndex++;
            }
        }
    }

    /**
     * Returns a fixed SeaTunnelRowType used to store file fragments.
     *
     * <p>`data`: Holds the binary data of the file fragment. When the data is empty, it indicates
     * the end of the file.
     *
     * <p>`relativePath`: Represents the sub-path of the file.
     *
     * <p>`partIndex`: Indicates the order of the file fragment.
     */
    @Override
    public SeaTunnelRowType getSeaTunnelRowTypeInfo(String path) throws FileConnectorException {
        return binaryRowType;
    }
}
