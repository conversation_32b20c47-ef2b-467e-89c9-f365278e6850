/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.hadoop;

import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.common.exception.SeaTunnelRuntimeException;
import org.apache.seatunnel.connectors.seatunnel.file.config.HadoopConf;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.CommonConfigurationKeys;
import org.apache.hadoop.fs.FSDataInputStream;
import org.apache.hadoop.fs.FSDataOutputStream;
import org.apache.hadoop.fs.FileStatus;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hadoop.util.ReflectionUtils;

import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.Serializable;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.PrivilegedExceptionAction;
import java.util.ArrayList;
import java.util.List;

import static org.apache.parquet.avro.AvroReadSupport.READ_INT96_AS_FIXED;
import static org.apache.parquet.avro.AvroSchemaConverter.ADD_LIST_ELEMENT_RECORDS;
import static org.apache.parquet.avro.AvroWriteSupport.WRITE_FIXED_AS_INT96;
import static org.apache.parquet.avro.AvroWriteSupport.WRITE_OLD_LIST_STRUCTURE;

@Slf4j
public class HadoopFileSystemProxy implements Serializable, Closeable {

    private transient UserGroupInformation userGroupInformation;

    private transient Configuration configuration;

    private transient FileSystem fileSystem;

    private final HadoopConf hadoopConf;

    private boolean isAuthTypeKerberos;

    public HadoopFileSystemProxy(@NonNull HadoopConf hadoopConf) {
        this.hadoopConf = hadoopConf;
        initialize();
    }

    public FileSystem getFileSystem() {
        if (fileSystem == null) {
            initialize();
        }
        return fileSystem;
    }

    public boolean fileExist(@NonNull String filePath) throws IOException {
        return execute(() -> getFileSystem().exists(new Path(filePath)));
    }

    public boolean isFile(@NonNull String filePath) throws IOException {
        return execute(() -> getFileSystem().getFileStatus(new Path(filePath)).isFile());
    }

    public void createFile(@NonNull String filePath) throws IOException {
        execute(
                () -> {
                    if (!getFileSystem().createNewFile(new Path(filePath))) {
                        throw CommonError.fileOperationFailed("SeaTunnel", "create", filePath);
                    }
                    return Void.class;
                });
    }

    public void deleteFile(@NonNull String filePath) throws IOException {
        execute(
                () -> {
                    Path path = new Path(filePath);
                    if (getFileSystem().exists(path)) {
                        if (!getFileSystem().delete(new Path(filePath), true)) {
                            throw CommonError.fileOperationFailed("SeaTunnel", "delete", filePath);
                        }
                    }
                    return Void.class;
                });
    }

    public void renameFile(
            @NonNull String oldFilePath,
            @NonNull String newFilePath,
            boolean removeWhenNewFilePathExist)
            throws IOException {
        execute(
                () -> {
                    log.info(
                            "开始重命名文件 old = {}, new={},removeWhenNewFilePathExist={},线程ID={}",
                            oldFilePath,
                            newFilePath,
                            removeWhenNewFilePathExist,
                            Thread.currentThread().getId());
                    Path oldPath = new Path(oldFilePath);
                    Path newPath = new Path(newFilePath);

                    if (!fileExist(oldPath.toString())) {
                        log.warn(
                                "rename file :["
                                        + oldPath
                                        + "] to ["
                                        + newPath
                                        + "] already finished in the last commit, skip");
                        return Void.class;
                    }
                    if (removeWhenNewFilePathExist) {
                        if (fileExist(newFilePath)) {
                            getFileSystem().delete(newPath, true);
                            log.info("Delete already file: {}", newPath);
                        }
                    }
                    if (!fileExist(newPath.getParent().toString())) {
                        createDir(newPath.getParent().toString());
                    }

                    if (getFileSystem().rename(oldPath, newPath)) {
                        log.info("rename file :[" + oldPath + "] to [" + newPath + "] finish");
                    } else {
                        log.info("重命名失败.....");
                        throw CommonError.fileOperationFailed(
                                "SeaTunnel", "rename", oldFilePath + " -> " + newFilePath);
                    }
                    return Void.class;
                });
    }

    public void createDir(@NonNull String filePath) throws IOException {
        execute(
                () -> {
                    if (!getFileSystem().mkdirs(new Path(filePath))) {
                        throw CommonError.fileOperationFailed("SeaTunnel", "create", filePath);
                    }
                    return Void.class;
                });
    }

    public List<LocatedFileStatus> listFile(String path) throws IOException {
        return execute(
                () -> {
                    List<LocatedFileStatus> fileList = new ArrayList<>();
                    if (!fileExist(path)) {
                        return fileList;
                    }
                    Path fileName = new Path(path);
                    RemoteIterator<LocatedFileStatus> locatedFileStatusRemoteIterator =
                            getFileSystem().listFiles(fileName, false);
                    while (locatedFileStatusRemoteIterator.hasNext()) {
                        fileList.add(locatedFileStatusRemoteIterator.next());
                    }
                    return fileList;
                });
    }

    public List<Path> getAllSubFiles(@NonNull String filePath) throws IOException {
        return execute(
                () -> {
                    List<Path> pathList = new ArrayList<>();
                    if (!fileExist(filePath)) {
                        return pathList;
                    }
                    Path fileName = new Path(filePath);
                    FileStatus[] status = getFileSystem().listStatus(fileName);
                    if (status != null) {
                        for (FileStatus fileStatus : status) {
                            if (fileStatus.isDirectory()) {
                                pathList.add(fileStatus.getPath());
                            }
                        }
                    }
                    return pathList;
                });
    }

    public FileStatus[] listStatus(String filePath) throws IOException {
        return execute(() -> getFileSystem().listStatus(new Path(filePath)));
    }

    public FileStatus getFileStatus(String filePath) throws IOException {
        return execute(() -> getFileSystem().getFileStatus(new Path(filePath)));
    }

    public FSDataOutputStream getOutputStream(String filePath) throws IOException {
        return execute(() -> getFileSystem().create(new Path(filePath), true));
    }

    public FSDataInputStream getInputStream(String filePath) throws IOException {
        return execute(() -> getFileSystem().open(new Path(filePath)));
    }

    @SneakyThrows
    public <T> T doWithHadoopAuth(HadoopLoginFactory.LoginFunction<T> loginFunction) {
        if (configuration == null) {
            this.configuration = createConfiguration();
        }
        if (enableKerberos()) {
            configuration.set("hadoop.security.authentication", "kerberos");
            return HadoopLoginFactory.loginWithKerberos(
                    configuration,
                    hadoopConf.getKrb5Path(),
                    hadoopConf.getKerberosPrincipal(),
                    hadoopConf.getKerberosKeytabPath(),
                    loginFunction);
        }
        if (enableRemoteUser()) {
            return HadoopLoginFactory.loginWithRemoteUser(
                    configuration, hadoopConf.getRemoteUser(), loginFunction);
        }
        return loginFunction.run(configuration, UserGroupInformation.getCurrentUser());
    }

    @Override
    public void close() throws IOException {
        try {
            if (userGroupInformation != null && enableKerberos()) {
                userGroupInformation.logoutUserFromKeytab();
            }
        } finally {
            /*log.info("当前关闭线程ID={}", Thread.currentThread().getId());
            if (fileSystem != null) {
                //fileSystem.close();
                fileSystem = null;
            }*/
        }
    }

    private void initialize() {
        try {
            this.configuration = createConfiguration();
            if (StringUtils.isNotBlank(hadoopConf.getTmpPath())) {
                // 原值：hadoop.tmp.dir -> /tmp/hadoop-${user.name}
                configuration.set("hadoop.tmp.dir", hadoopConf.getTmpPath());
            }
            if (enableKerberos()) {
                configuration.set("hadoop.security.authentication", "kerberos");
                initializeWithKerberosLogin();
                isAuthTypeKerberos = true;
                return;
            }
            if (enableRemoteUser()) {
                initializeWithRemoteUserLogin();
                isAuthTypeKerberos = true;
                return;
            }
            if ("100M".equalsIgnoreCase(configuration.get("fs.s3a.multipart.size"))) {
                configuration.set("fs.s3a.multipart.size", "104857600");
            }
            if ("32M".equalsIgnoreCase(configuration.get("fs.s3a.block.size"))) {
                configuration.set("fs.s3a.block.size", "33554432");
            }
            this.fileSystem = FileSystem.get(configuration);
            this.fileSystem.setWriteChecksum(false);
            isAuthTypeKerberos = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Configuration createConfiguration() {
        Configuration configuration = new Configuration();
        configuration.setBoolean(READ_INT96_AS_FIXED, true);
        configuration.setBoolean(WRITE_FIXED_AS_INT96, true);
        configuration.setBoolean(ADD_LIST_ELEMENT_RECORDS, false);
        configuration.setBoolean(WRITE_OLD_LIST_STRUCTURE, true);
        configuration.set(CommonConfigurationKeys.FS_DEFAULT_NAME_KEY, hadoopConf.getHdfsNameKey());
        configuration.setBoolean(
                String.format("fs.%s.impl.disable.cache", hadoopConf.getSchema()), true);
        configuration.set(
                String.format("fs.%s.impl", hadoopConf.getSchema()), hadoopConf.getFsHdfsImpl());
        hadoopConf.setExtraOptionsForConfiguration(configuration);
        return configuration;
    }

    private boolean enableKerberos() {
        boolean kerberosPrincipalEmpty = StringUtils.isBlank(hadoopConf.getKerberosPrincipal());
        boolean kerberosKeytabPathEmpty = StringUtils.isBlank(hadoopConf.getKerberosKeytabPath());
        if (kerberosKeytabPathEmpty && kerberosPrincipalEmpty) {
            return false;
        }
        if (!kerberosPrincipalEmpty && !kerberosKeytabPathEmpty) {
            return true;
        }
        if (kerberosPrincipalEmpty) {
            throw new IllegalArgumentException("Please set kerberosPrincipal");
        }
        throw new IllegalArgumentException("Please set kerberosKeytabPath");
    }

    private void initializeWithKerberosLogin() throws IOException, InterruptedException {
        Pair<UserGroupInformation, FileSystem> pair =
                HadoopLoginFactory.loginWithKerberos(
                        configuration,
                        hadoopConf.getKrb5Path(),
                        hadoopConf.getKerberosPrincipal(),
                        hadoopConf.getKerberosKeytabPath(),
                        (configuration, userGroupInformation) -> {
                            this.userGroupInformation = userGroupInformation;
                            this.fileSystem = FileSystem.get(configuration);
                            return Pair.of(userGroupInformation, fileSystem);
                        });
        // todo: Use a daemon thread to reloginFromTicketCache
        this.userGroupInformation = pair.getKey();
        this.fileSystem = pair.getValue();
        this.fileSystem.setWriteChecksum(false);
        log.info("Create FileSystem success with Kerberos: {}.", hadoopConf.getKerberosPrincipal());
    }

    private boolean enableRemoteUser() {
        return StringUtils.isNotBlank(hadoopConf.getRemoteUser());
    }

    private void initializeWithRemoteUserLogin() throws Exception {
        final Pair<UserGroupInformation, FileSystem> pair =
                HadoopLoginFactory.loginWithRemoteUser(
                        configuration,
                        hadoopConf.getRemoteUser(),
                        (configuration, userGroupInformation) -> {
                            final FileSystem fileSystem = FileSystem.get(configuration);
                            return Pair.of(userGroupInformation, fileSystem);
                        });
        log.info("Create FileSystem success with RemoteUser: {}.", hadoopConf.getRemoteUser());
        this.userGroupInformation = pair.getKey();
        this.fileSystem = pair.getValue();
        this.fileSystem.setWriteChecksum(false);
    }

    private <T> T execute(PrivilegedExceptionAction<T> action) throws IOException {
        if (isAuthTypeKerberos) {
            return doAsPrivileged(action);
        } else {
            try {
                return action.run();
            } catch (IOException | SeaTunnelRuntimeException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private <T> T doAsPrivileged(PrivilegedExceptionAction<T> action) throws IOException {
        if (fileSystem == null || userGroupInformation == null) {
            initialize();
        }

        try {
            return userGroupInformation.doAs(action);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException(e);
        }
    }

    public void writeValidateFile(
            String validateFile,
            String validateContent,
            List<String> filePaths,
            boolean isFile,
            String path,
            String tdsqlPartitionName,
            String fileNameExpression)
            throws IOException {
        OutputStream outputStream;
        if (StringUtils.isNotEmpty(tdsqlPartitionName)) {
            validateFile =
                    validateFile.substring(0, validateFile.lastIndexOf(File.separator) + 1)
                            + "dsg_tdsql_flg"
                            + File.separator
                            + tdsqlPartitionName
                            + "_"
                            + validateFile.substring(validateFile.lastIndexOf(File.separator) + 1);
            outputStream = getOutputStream(validateFile);
        } else {
            outputStream = getOutputStream(validateFile);
        }
        if (!isFile && StringUtils.isNotBlank(path)) { // 目录同步
            FileStatus[] fileStatuses = getFileSystem().listStatus(new Path(path));
            int fileCount = 0;
            int directCount = 0;
            for (FileStatus fs : fileStatuses) {
                if (fs.isFile()) {
                    fileCount++;
                } else {
                    directCount++;
                }
            }
            String content = getResultContent(validateContent, "", 0, "", fileCount, directCount);
            outputStream.write(content.getBytes());
            log.info("写入的内容是{},{},{}", validateContent, fileCount, directCount);
        } else { // 文件同步
            // 源端如果是tdsql分区表，就读数据文件路径下所有文件，因为多读多写在生成flg文件时会有覆盖的问题，要确保最后一个读写线程要获取到所有数据文件
            //            if (StringUtils.isNotEmpty(tdsqlPartitionName) &&
            // StringUtils.isNotEmpty(fileNameExpression)) {
            //                log.info("源端是tdsql分区表，分区名称={}，开始获取数据文件路径",tdsqlPartitionName);
            //                String prefix = fileNameExpression.indexOf("_${transactionId}") > 0 ?
            // fileNameExpression.substring(0, fileNameExpression.indexOf("_${transactionId}")) :
            // fileNameExpression;
            //                String dataFilePath = filePaths.get(0);
            //                dataFilePath = dataFilePath.substring(0,
            // dataFilePath.lastIndexOf(File.separator));
            //                FileStatus[] fileStatuses = listStatus(dataFilePath);
            //                log.info("源端是tdsql分区表，分区名称={}，获取到的数据文件路径是{}，数据文件前缀是{}", dataFilePath,
            // prefix,tdsqlPartitionName);
            //                if (fileStatuses != null && fileStatuses.length > 0) {
            //                    for (int i = 0; i < fileStatuses.length; i++) {
            //                        FileStatus fileStatus = fileStatuses[i];
            //                        String name = fileStatus.getPath().getName();
            //                        if (!name.startsWith(prefix)) {
            //                            continue;
            //                        }
            //                        String filePath = fileStatus.getPath().toString();
            //                        filePath =
            // filePath.substring(filePath.indexOf(File.separator));
            //                        log.info("源端是tdsql分区表，分区名称={}，获取到的数据文件是{}",
            // filePath,tdsqlPartitionName);
            //                        if (fileExist(filePath)//文件存在
            //                                &&
            // fileStatus.getPermission().toString().contains("r")//文件可读
            //                                && (System.currentTimeMillis() -
            // fileStatus.getModificationTime()) > 1000) {//最后修改时间大于1秒，说明文件已经提交（在前面先睡了2秒）
            //                            long length = fileStatus.getLen();
            //                            String content = getResultContent(validateContent, name,
            // length, filePath, 0, 0) + "\n";
            //                            outputStream.write(content.getBytes());
            //                            continue;
            //                        }
            //                        log.info("源端是tdsql分区表，数据文件现在不存在（疑似未提交），跳过{}", filePath);
            //                    }
            //                }
            //            } else {
            for (int i = 0; i < filePaths.size(); i++) {
                String filePath = filePaths.get(i);
                String name = getFileStatus(filePath).getPath().getName();
                long length = getFileStatus(filePath).getLen();
                String content =
                        getResultContent(validateContent, name, length, filePath, 0, 0) + "\n";
                outputStream.write(content.getBytes());
            }
            //            }
        }
        outputStream.close();
    }

    private String getResultContent(
            String validateContent,
            String name,
            long length,
            String filePath,
            int fileCount,
            int folderCount)
            throws IOException {
        if (StringUtils.isNotBlank(validateContent)) {
            if (validateContent.contains("${file_name}")) {
                validateContent = validateContent.replace("${file_name}", name);
            }
            if (validateContent.contains("${file_size}")) {
                validateContent = validateContent.replace("${file_size}", length + "");
            }
            if (validateContent.contains("${file_row}")) {
                validateContent =
                        validateContent.replace("${file_row}", getFileLines(filePath) + "");
            }
            if (validateContent.contains("${file_count}")) {
                validateContent = validateContent.replace("${file_count}", fileCount + "");
            }
            if (validateContent.contains("${folder_count}")) {
                validateContent = validateContent.replace("${folder_count}", folderCount + "");
            }
            return validateContent;
        }
        return "";
    }
    /*
        private String getResultContent(String validateContent, String name,long length,String filePath) throws IOException {
            if(StringUtils.isNotBlank(validateContent)){
                String regex = "\\$\\{([^}]*)\\}";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(validateContent);
                List<String> str = new ArrayList<>();
                while (matcher.find()) {
                    str.add(matcher.group(1));
                }
                if(str.size()==0){
                    return validateContent;
                }
                String value = "";
                for(String s :str){
                    if(s.equalsIgnoreCase("file_name")){
                        value=value+" "+name;
                    }else if(s.equalsIgnoreCase("file_size")){
                        value=value+" "+length;
                    }else if(s.equalsIgnoreCase("file_row")){
                        value = value+" "+getFileLines(filePath);
                    }else if(s.equalsIgnoreCase("file_md5")){

                    }else if(s.equalsIgnoreCase("file_date")){
                        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                        value=value+" "+df.format(new Date());
                    }
                }
                if(value.startsWith(" ")){
                    value=value.substring(1,value.length());
                }
                return value;
            }
            return "";
        }
    */

    private long getFileLines(String filePath) throws IOException {
        long lineCount = 0;
        // 2024-12-05 目录同步 校验文件内容不取文件行
        if (StringUtils.isNotBlank(filePath)) {
            InputStream inputStream = getInputStream(filePath);
            try (BufferedReader reader =
                    new BufferedReader(
                            new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                while (reader.readLine() != null) {
                    lineCount++;
                }
            }
        }
        return lineCount;
    }

    public void writeValidateFiles(
            String validateFile,
            String validateContent,
            List<String> filePaths,
            boolean isFile,
            String path)
            throws IOException {
        if (!isFile && StringUtils.isNotBlank(path)) { // 目录同步
            // TODO 目录同步要做每个文件生成一个flag吗？
            //            FileStatus[] fileStatuses = getFileSystem().listStatus(new Path(path));
            //            int fileCount = 0;
            //            int directCount = 0;
            //            for (FileStatus fs : fileStatuses) {
            //                if (fs.isFile()) {
            //                    fileCount++;
            //                } else {
            //                    directCount++;
            //                }
            //            }
            //            String content = getResultContent(validateContent, "", 0, "", fileCount,
            // directCount);
            //            OutputStream outputStream = getOutputStream(validateFile);
            //            outputStream.write(content.getBytes());
            //            log.info("写入的内容是{},{},{}", validateContent, fileCount, directCount);
            //            outputStream.close();
        } else { // 文件同步
            for (int i = 0; i < filePaths.size(); i++) {
                String filePath = filePaths.get(i);
                if (!validateFile.endsWith(File.separator)) {
                    validateFile = validateFile + File.separator;
                }
                String flagName =
                        validateFile
                                + filePath.substring(
                                        filePath.lastIndexOf(File.separator) + 1,
                                        filePath.lastIndexOf("."))
                                + ".flg";
                OutputStream outputStream = getOutputStream(flagName);
                String name = getFileStatus(filePath).getPath().getName();
                long length = getFileStatus(filePath).getLen();
                String content = getResultContent(validateContent, name, length, filePath, 0, 0);
                outputStream.write(content.getBytes());
                log.info("写入的内容是{},{},{}", validateContent, name, length);
                outputStream.close();
            }
        }
    }
}
