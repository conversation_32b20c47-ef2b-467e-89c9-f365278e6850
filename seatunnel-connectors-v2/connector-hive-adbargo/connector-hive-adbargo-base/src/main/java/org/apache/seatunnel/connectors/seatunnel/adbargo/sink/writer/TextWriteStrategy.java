/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.adbargo.sink.writer;

import org.apache.seatunnel.api.serialization.SerializationSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.connectors.seatunnel.adbargo.config.FileFormat;
import org.apache.seatunnel.connectors.seatunnel.adbargo.exception.FileConnectorException;
import org.apache.seatunnel.connectors.seatunnel.adbargo.sink.config.FileSinkConfig;
import org.apache.seatunnel.connectors.seatunnel.adbargo.sink.util.AdbUtil;
import org.apache.seatunnel.format.text.TextSerializationSchema;

import org.apache.hadoop.fs.FSDataOutputStream;

import groovy.lang.Tuple2;
import lombok.NonNull;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TextWriteStrategy extends AbstractWriteStrategy {
    private final LinkedHashMap<String, FSDataOutputStream> beingWrittenOutputStream;
    private final Map<String, Boolean> isFirstWrite;
    private final String fieldDelimiter;
    private final String rowDelimiter;
    private final DateUtils.Formatter dateFormat;
    private final DateTimeUtils.Formatter dateTimeFormat;
    private final TimeUtils.Formatter timeFormat;
    private final FileFormat fileFormat;
    private final Boolean enableHeaderWriter;
    private SerializationSchema serializationSchema;
    private FileSinkConfig fileSinkConfig;
    private SeaTunnelRowType adbType;

    public TextWriteStrategy(FileSinkConfig fileSinkConfig) {
        super(fileSinkConfig);
        this.beingWrittenOutputStream = new LinkedHashMap<>();
        this.isFirstWrite = new HashMap<>();
        this.fieldDelimiter = fileSinkConfig.getFieldDelimiter();
        this.rowDelimiter = fileSinkConfig.getRowDelimiter();
        this.dateFormat = fileSinkConfig.getDateFormat();
        this.dateTimeFormat = fileSinkConfig.getDatetimeFormat();
        this.timeFormat = fileSinkConfig.getTimeFormat();
        this.fileFormat = fileSinkConfig.getFileFormat();
        this.enableHeaderWriter = fileSinkConfig.getEnableHeaderWriter();
        this.fileSinkConfig = fileSinkConfig;
    }

    @Override
    public void setSeaTunnelRowTypeInfo(SeaTunnelRowType seaTunnelRowType) {
        super.setSeaTunnelRowTypeInfo(seaTunnelRowType);
        this.adbType = seaTunnelRowType;
        this.serializationSchema =
                TextSerializationSchema.builder()
                        .seaTunnelRowType(
                                buildSchemaWithRowType(seaTunnelRowType, sinkColumnsIndexInRow))
                        .delimiter(fieldDelimiter)
                        .dateFormatter(dateFormat)
                        .dateTimeFormatter(dateTimeFormat)
                        .timeFormatter(timeFormat)
                        .build();
    }

    @Override
    public void write(@NonNull SeaTunnelRow seaTunnelRow) {
        super.write(seaTunnelRow);
        String filePath = getOrCreateFilePathBeingWritten(seaTunnelRow);
        FSDataOutputStream fsDataOutputStream = getOrCreateOutputStream(filePath);
        try {
            if (isFirstWrite.get(filePath)) {
                isFirstWrite.put(filePath, false);
            } else {
                fsDataOutputStream.write(rowDelimiter.getBytes());
            }
            fsDataOutputStream.write(
                    serializationSchema.serialize(
                            seaTunnelRow.copy(
                                    sinkColumnsIndexInRow.stream()
                                            .mapToInt(Integer::intValue)
                                            .toArray())));

        } catch (IOException e) {
            throw CommonError.fileOperationFailed("TextFile", "write", filePath, e);
        }
    }

    @Override
    public void close() throws IOException {
        // 1、创建文件csv文件
        String filePath = fileSinkConfig.getPath(); //  得到的路径是/usr/local/gpdata/emp/
        if (!filePath.endsWith("/")) {
            filePath = filePath + "/";
        }
        String adbGpfdistPath = fileSinkConfig.getAdbGpfdistPath(); // /usr/local/gpdata
        if (adbGpfdistPath.endsWith("/")) {
            adbGpfdistPath = adbGpfdistPath.substring(0, adbGpfdistPath.length() - 1);
        }
        String adbTmpFilePath = fileSinkConfig.getAdbTmpFilePath(); // 得到的路径是 /emp/0.csv
        if (!adbTmpFilePath.startsWith("/")) {
            adbTmpFilePath = "/" + adbTmpFilePath;
        }
        AdbUtil.mergeFile(filePath, adbGpfdistPath + adbTmpFilePath);

        // int gpfdistFileLength = filePath.length()-adbTmpFilePath.length();//
        // 移除本身gpfdist的路径长度,前端传过来是filePath = gpfdist地址+adbTmpFilePath
        // 这里是要得到真实的adbTmpFilePath+文件名称的路径
        // AdbUtil.checkFile(filePath);
        String gpfdistAddress = fileSinkConfig.getAdbGpfdistAddress();
        // 必须文件先存在
        // String realFilePaths = AdbUtil.getRealFilePath(filePath,adbTmpFilePath);//得到最终的文件路径
        // log.info("文件创建完毕{}",realFilePaths);
        String tableName = fileSinkConfig.getAdbTable();
        String dbSchema = fileSinkConfig.getAdbDatabase();
        // String code = UUID.randomUUID().toString().replaceAll("-","_");
        // String tmpTableName = tableName.concat("_dsg_"+code);
        String tmpTableName = fileSinkConfig.getAdbExternalTableName();
        log.info("临时表名称是{}", tmpTableName);

        String gpfdistAddr = AdbUtil.getGpfdistAddress(gpfdistAddress, adbTmpFilePath);
        // gpfdistAddress=gpfdistAddress+argoPrefix;
        String tableDelimiter = fileSinkConfig.getFieldDelimiter(); // 列分隔符
        log.info("fpgdist地址是 {},分隔符是{}", gpfdistAddr, tableDelimiter);
        // 2、连接数据库创建表，执行 必须文件存在才可以
        List<String> fields = Arrays.stream(adbType.getFieldNames()).collect(Collectors.toList());
        String ddlSql =
                AdbUtil.getAdbCreateTableSQL(
                        fields, dbSchema, tmpTableName, gpfdistAddr, tableDelimiter);
        log.info("建表语句是 {}", ddlSql);
        String argoUrl = fileSinkConfig.getAdbUrl();
        String argoUser = fileSinkConfig.getAdbUser();
        String argoPassword = fileSinkConfig.getAdbPassword();
        // 删除临时表（防止失败情况下没有删除）
        /*try {
            String dropTableTmp = AdbUtil.getAdbDropTable(dbSchema,tmpTableName);
            AdbUtil.executeSql(argoUrl,argoUser,argoPassword,dropTableTmp);
        }catch (Exception e){
            log.error("启动程序，先删除表，表可能不存在，这里忽略错误");
        }*/
        log.info("开始执行建表");
        // 建表
        AdbUtil.executeSql(argoUrl, argoUser, argoPassword, ddlSql);
        log.info("建表完毕，开始处理插入数据");
        String insertSql = AdbUtil.getAdbInsertSql(dbSchema, tmpTableName, tableName, fields);
        log.info("插入语句是{}", insertSql);
        AdbUtil.executeSql(argoUrl, argoUser, argoPassword, insertSql);
        log.info("插入完毕..开始判断文件是否写入完毕");
        // 3、insert into emp_test1 select * from emp
        // ArgoUtil.checkFile(filePaths);
        // 4、判断文件长度长度大于0
        // ./gpfdist -p 8089 -d /usr/local/gpdata
        // log.info("文件写入完毕，开始删除临时表");
        // String dropTable = AdbUtil.getAdbDropTable(dbSchema,tmpTableName);
        // log.info("删除临时表sql={}",dropTable);
        // AdbUtil.executeSql(argoUrl,argoUser,argoPassword,dropTable);
        // log.info("临时表删除完毕...");
        AdbUtil.deleteFile(adbGpfdistPath + adbTmpFilePath);
        super.close();
    }

    @Override
    public void finishAndCloseFile() {
        beingWrittenOutputStream.forEach(
                (key, value) -> {
                    try {
                        value.flush();
                    } catch (IOException e) {
                        throw new FileConnectorException(
                                CommonErrorCodeDeprecated.FLUSH_DATA_FAILED,
                                String.format("Flush data to this file [%s] failed", key),
                                e);
                    } finally {
                        try {
                            value.close();
                        } catch (IOException e) {
                            log.error("error when close output stream {}", key, e);
                        }
                    }
                    needMoveFiles.put(key, getTargetLocation(key));
                });
        beingWrittenOutputStream.clear();
        isFirstWrite.clear();
    }

    private String getInsertSql(String tableName, String tmpTableName, List<String> columns) {
        String selectColumn = columns.stream().collect(Collectors.joining(", ", "", ""));
        return "insert into " + tableName + " select " + selectColumn + " from " + tmpTableName;
    }

    private Tuple2<String, List<String>> getDdl(
            SeaTunnelRowType rowType, String tmpTable, String tmpFilePath) {
        List<String> columns = new ArrayList<>();
        String sql = "CREATE EXTERNAL TABLE " + tmpTable + " (";
        for (int i = 0; i < rowType.getFieldNames().length; i++) {
            String fieldName = rowType.getFieldNames()[i];
            columns.add(fieldName);
            SeaTunnelDataType<?> fieldType = rowType.getFieldTypes()[i];
            if (i != 0) {
                sql += ",";
            }
            sql += fieldName + " " + fieldType.getSqlType().toString();
        }
        sql += " )ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LOCATION '" + tmpFilePath + "'";
        return new Tuple2<>(sql, columns);
    }

    private FSDataOutputStream getOrCreateOutputStream(@NonNull String filePath) {
        FSDataOutputStream fsDataOutputStream = beingWrittenOutputStream.get(filePath);
        if (fsDataOutputStream == null) {
            try {
                switch (compressFormat) {
                    case NONE:
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                    default:
                        log.warn(
                                "Text file does not support this compress type: {}",
                                compressFormat.getCompressCodec());
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                }
                beingWrittenOutputStream.put(filePath, fsDataOutputStream);
                isFirstWrite.put(filePath, true);
            } catch (IOException e) {
                throw CommonError.fileOperationFailed("TextFile", "open", filePath, e);
            }
        }
        return fsDataOutputStream;
    }

    private void enableWriteHeader(FSDataOutputStream fsDataOutputStream) throws IOException {
        if (enableHeaderWriter) {
            fsDataOutputStream.write(
                    String.join(
                                    FileFormat.CSV.equals(fileFormat) ? "," : fieldDelimiter,
                                    seaTunnelRowType.getFieldNames())
                            .getBytes());
            fsDataOutputStream.write(rowDelimiter.getBytes());
        }
    }
}
