/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.klaviyo.source.config;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.connectors.seatunnel.http.config.HttpConfig;

public class KlaviyoSourceConfig extends HttpConfig {
    public static final String KLAVIYO_API_KEY = "Klaviyo-API-Key";
    public static final String AUTHORIZATION = "Authorization";
    public static final String ACCEPT = "Accept";
    public static final String APPLICATION_JSON = "application/json";

    public static final Option<String> PRIVATE_KEY =
            Options.key("private_key")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("Klaviyo login private key");
    public static final Option<String> REVISION =
            Options.key("revision")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("API endpoint revision (format: YYYY-MM-DD)");
}
