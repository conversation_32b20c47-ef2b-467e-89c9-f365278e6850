package org.apache.seatunnel.connectors.seatunnel.http;

import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.TimeUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

public class TImeUtilTest {

    public static void main(String[] args) {
        String dataFormat = "HH:mm:ss";
        String value = "20:12:12";
        TimeUtils.Formatter formatter = TimeUtils.Formatter.parse(dataFormat);
        LocalTime data = TimeUtils.parse(value, formatter);
        System.out.println(data.toString());

        String formatterStr = TimeUtils.toString(data, formatter);
        System.out.println(formatterStr);

        System.out.println("--------------------------");
        dataFormat = "yyyy/MM/dd";
        value = "2025/12/12";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dataFormat);
        LocalDate localDate = DateUtils.parse(value, dateTimeFormatter);
        System.out.println("------------localDate--------------" + localDate.toString());
    }
}
