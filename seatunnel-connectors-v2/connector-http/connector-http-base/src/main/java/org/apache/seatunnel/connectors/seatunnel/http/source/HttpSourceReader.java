/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.http.source;

import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.seatunnel.shade.com.google.common.base.Strings;

import org.apache.seatunnel.api.serialization.DeserializationSchema;
import org.apache.seatunnel.api.source.Boundedness;
import org.apache.seatunnel.api.source.Collector;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.connectors.seatunnel.common.source.AbstractSingleSplitReader;
import org.apache.seatunnel.connectors.seatunnel.common.source.SingleSplitReaderContext;
import org.apache.seatunnel.connectors.seatunnel.http.client.HttpClientProvider;
import org.apache.seatunnel.connectors.seatunnel.http.client.HttpResponse;
import org.apache.seatunnel.connectors.seatunnel.http.config.HttpParameter;
import org.apache.seatunnel.connectors.seatunnel.http.config.JsonField;
import org.apache.seatunnel.connectors.seatunnel.http.config.PageInfo;
import org.apache.seatunnel.connectors.seatunnel.http.exception.HttpConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.http.exception.HttpConnectorException;

import org.apache.commons.lang3.StringUtils;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.ReadContext;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Setter
public class HttpSourceReader extends AbstractSingleSplitReader<SeaTunnelRow> {
    protected final SingleSplitReaderContext context;
    protected final HttpParameter httpParameter;
    protected HttpClientProvider httpClient;
    private final DeserializationCollector deserializationCollector;
    private static final Option[] DEFAULT_OPTIONS = {
        Option.SUPPRESS_EXCEPTIONS, Option.ALWAYS_RETURN_LIST, Option.DEFAULT_PATH_LEAF_TO_NULL
    };
    private JsonPath[] jsonPaths;
    private final JsonField jsonField;
    private final String contentJson;
    private String tablePath;
    private final Configuration jsonConfiguration =
            Configuration.defaultConfiguration().addOptions(DEFAULT_OPTIONS);
    private boolean noMoreElementFlag = true;
    private Optional<PageInfo> pageInfoOptional = Optional.empty();

    private SeaTunnelRowType seaTunnelRowType;

    public HttpSourceReader(
            HttpParameter httpParameter,
            SingleSplitReaderContext context,
            DeserializationSchema<SeaTunnelRow> deserializationSchema,
            JsonField jsonField,
            String contentJson) {
        this.context = context;
        this.httpParameter = httpParameter;
        this.deserializationCollector = new DeserializationCollector(deserializationSchema);
        this.jsonField = jsonField;
        this.contentJson = contentJson;
    }

    public HttpSourceReader(
            SeaTunnelRowType seaTunnelRowType,
            HttpParameter httpParameter,
            SingleSplitReaderContext context,
            DeserializationSchema<SeaTunnelRow> deserializationSchema,
            JsonField jsonField,
            String contentJson,
            PageInfo pageInfo,
            String tablePath) {
        this.seaTunnelRowType = seaTunnelRowType;
        this.context = context;
        this.httpParameter = httpParameter;
        this.deserializationCollector = new DeserializationCollector(deserializationSchema);
        this.jsonField = jsonField;
        this.contentJson = contentJson;
        this.pageInfoOptional = Optional.ofNullable(pageInfo);
        this.tablePath = tablePath;
    }

    @Override
    public void open() {
        httpClient = new HttpClientProvider(httpParameter);
    }

    @Override
    public void close() throws IOException {
        if (Objects.nonNull(httpClient)) {
            httpClient.close();
        }
    }

    public void pollAndCollectData(PageInfo pageInfo, Collector<SeaTunnelRow> output)
            throws Exception {
        if (pageInfo != null) {
            String pageField = pageInfo.getPageField();
            Long pageIndex = pageInfo.getPageIndex();
            this.httpParameter.getParams().put(pageField, pageIndex.toString()); // 每次查询设置新的分页索引
            String body = this.httpParameter.getBody();
            Map<String, Object> bodyMap =
                    JsonUtils.parseObject(body, new TypeReference<Map<String, Object>>() {});
            bodyMap.put(pageField, pageIndex);
            this.httpParameter.setBody(JsonUtils.toJsonString(bodyMap));
        }

        HttpResponse response =
                httpClient.executeV2(
                        this.httpParameter.getUrl(),
                        this.httpParameter.getMethod().getMethod(),
                        this.httpParameter.getHeaders(),
                        this.httpParameter.getParams(),
                        this.httpParameter.getBody(),
                        this.httpParameter.getBodySendType());

        if (response.getCode() >= 200 && response.getCode() <= 207) {
            String content = response.getContent();
            if (!Strings.isNullOrEmpty(content)) {
                if (this.httpParameter.isEnableMultilines()) {
                    StringReader stringReader = new StringReader(content);
                    BufferedReader bufferedReader = new BufferedReader(stringReader);
                    String lineStr;
                    while ((lineStr = bufferedReader.readLine()) != null) {
                        collect(output, lineStr, tablePath);
                    }
                } else {
                    collect(output, content, tablePath);
                }
            }
            log.debug(
                    "http client execute success request param:[{}], http response status code:[{}], content:[{}]",
                    httpParameter.getParams(),
                    response.getCode(),
                    response.getContent());
        } else {
            String msg =
                    String.format(
                            "http client execute exception, http response status code:[%s], content:[%s]",
                            response.getCode(), response.getContent());
            throw new HttpConnectorException(HttpConnectorErrorCode.REQUEST_FAILED, msg);
        }
    }

    private void updateRequestParam(PageInfo pageInfo) {
        if (this.httpParameter.getParams() == null) {
            httpParameter.setParams(new HashMap<>());
        }
        this.httpParameter
                .getParams()
                .put(pageInfo.getPageField(), pageInfo.getPageIndex().toString());
    }

    @Override
    public void pollNext(Collector<SeaTunnelRow> output) throws Exception {
        synchronized (output.getCheckpointLock()) {
            internalPollNext(output);
        }
    }

    @Override
    public void internalPollNext(Collector<SeaTunnelRow> output) throws Exception {
        try {
            if (pageInfoOptional.isPresent()) {
                noMoreElementFlag = false;
                PageInfo pageInfo = pageInfoOptional.get();
                Long pageIndex = pageInfo.getPageIndex();
                while (!noMoreElementFlag) {
                    // increment page
                    pageInfo.setPageIndex(pageIndex);
                    // set request param
                    updateRequestParam(pageInfo);
                    pollAndCollectData(pageInfo, output);
                    pageIndex += 1;
                    Thread.sleep(10);
                    log.info(
                            noMoreElementFlag
                                    + "----------noMoreElementFlag--------------"
                                    + pageIndex);
                }
            } else {
                pollAndCollectData(null, output);
            }
        } finally {
            if (Boundedness.BOUNDED.equals(context.getBoundedness()) && noMoreElementFlag) {
                // signal to the source that we have reached the end of the data.
                log.info("Closed the bounded http source");
                context.signalNoMoreElement();
            } else {
                if (httpParameter.getPollIntervalMillis() > 0) {
                    Thread.sleep(httpParameter.getPollIntervalMillis());
                }
            }
        }
    }

    private void collect(Collector<SeaTunnelRow> output, String data, String tablePath)
            throws IOException {
        if (contentJson != null) {
            data = JsonUtils.stringToJsonNode(getPartOfJson(data)).toString();
        }
        if (jsonField != null && contentJson == null) {
            this.initJsonPath(jsonField);
            data = JsonUtils.toJsonNode(parseToMap(decodeJSON(data), jsonField)).toString();
        }
        // page increase
        if (pageInfoOptional.isPresent()) {
            // Determine whether the task is completed by specifying the presence of the 'total
            // page' field
            PageInfo pageInfo = pageInfoOptional.get();
            if (pageInfo.getTotalPageSize() > 0) {
                boolean bl = pageInfo.getPageIndex() >= pageInfo.getTotalPageSize();
                int readSize = JsonUtils.stringToJsonNode(data).size();
                boolean b2 = readSize < pageInfo.getBatchSize();
                noMoreElementFlag = bl || b2;
            } else {
                // no 'total page' configured
                int readSize = JsonUtils.stringToJsonNode(data).size();
                // if read size < BatchSize : read finish
                // if read size = BatchSize : read next page.
                noMoreElementFlag = readSize < pageInfo.getBatchSize();
            }
        }
        deserializationCollector.collect(data.getBytes(), output, tablePath);
    }

    private List<Map<String, Object>> parseToMap(List<List<String>> datas, JsonField jsonField) {
        List<Map<String, Object>> decodeDatas = new ArrayList<>(datas.size());
        String[] keys = jsonField.getFields().keySet().toArray(new String[] {});

        for (List<String> data : datas) {
            Map<String, Object> decodeData = new HashMap<>(jsonField.getFields().size());
            final int[] index = {0};
            data.forEach(
                    field -> {
                        String key = keys[index[0]];
                        SeaTunnelDataType<?> seaTunnelDataType =
                                seaTunnelRowType.getFieldType(index[0]);
                        SqlType sqlType = seaTunnelDataType.getSqlType();
                        log.info(
                                key
                                        + "-------------sqlType---------"
                                        + sqlType.name()
                                        + "  DATA: "
                                        + field);
                        Object destData = doTransform(sqlType, sqlType.name(), field, "");
                        decodeData.put(key, destData);
                        index[0]++;
                    });
            decodeDatas.add(decodeData);
        }

        return decodeDatas;
    }

    private Object doTransform(
            SqlType srcSqlType, String desDataType, String value, String dataFormat) {
        if (srcSqlType.equals(SqlType.ARRAY)) {
            List<Object> list = JsonUtils.toList(value, Object.class);
            return list;
        } else if (srcSqlType.equals(SqlType.DECIMAL)) {
            return new BigDecimal(value);
        } else if (srcSqlType.equals(SqlType.INT)) {
            return Integer.valueOf(value);
        } else if (srcSqlType.equals(SqlType.BIGINT)) {
            return new BigInteger(value);
        } else if (srcSqlType.equals(SqlType.FLOAT)) {
            return Float.valueOf(value);
        } else if (srcSqlType.equals(SqlType.DOUBLE)) {
            return Double.valueOf(value);
        } else if (srcSqlType.equals(SqlType.BOOLEAN)) {
            return Boolean.valueOf(value);
        } else if (srcSqlType.equals(SqlType.DATE)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = httpParameter.getDateFormat();
            }
            if (value == null) {
                return null;
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dataFormat);
            LocalDate data = DateUtils.parse(value, dateTimeFormatter);
            return data.toString();
        } else if (srcSqlType.equals(SqlType.TIME)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = httpParameter.getTimeFormat();
            }
            if (value == null) {
                return null;
            }
            TimeUtils.Formatter formatter = TimeUtils.Formatter.parse(dataFormat);
            LocalTime data = TimeUtils.parse(value, formatter);
            return data.toString();
        } else if (srcSqlType.equals(SqlType.TIMESTAMP)
                || srcSqlType.equals(SqlType.TIMESTAMP_TZ)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = httpParameter.getDatetimeFormat();
            }
            //                DateTimeUtils.Formatter formatter =
            // DateTimeUtils.Formatter.parse(dataFormat);
            if (value == null) {
                return null;
            }
            DateTimeFormatter dateTimeFormatter = DateTimeUtils.matchDateTimeFormatter(value);
            LocalDateTime data = DateTimeUtils.parse(value, dateTimeFormatter);
            return data.toString();
        } else {
            return value;
        }
    }

    private List<List<String>> decodeJSON(String data) {
        ReadContext jsonReadContext = JsonPath.using(jsonConfiguration).parse(data);
        List<List<String>> results = new ArrayList<>(jsonPaths.length);
        for (JsonPath path : jsonPaths) {
            List<String> result = jsonReadContext.read(path);
            results.add(result);
        }
        for (int i = 1; i < results.size(); i++) {
            List<?> result0 = results.get(0);
            List<?> result = results.get(i);
            if (result0.size() != result.size()) {
                String format =
                        String.format(
                                "[%s](%d) and [%s](%d) the number of parsing records is inconsistent.",
                                jsonPaths[0].getPath(),
                                result0.size(),
                                jsonPaths[i].getPath(),
                                result.size());
                log.info("根据JsonPath解析数据元素大小不一致：" + format);
                // throw new
                // HttpConnectorException(HttpConnectorErrorCode.FIELD_DATA_IS_INCONSISTENT,
                // format);
            }
        }

        return dataFlip(results);
    }

    private String getPartOfJson(String data) {
        ReadContext jsonReadContext = JsonPath.using(jsonConfiguration).parse(data);
        return JsonUtils.toJsonString(jsonReadContext.read(JsonPath.compile(contentJson)));
    }

    private List<List<String>> dataFlip(List<List<String>> results) {

        List<List<String>> datas = new ArrayList<>();
        for (int i = 0; i < results.size(); i++) {
            List<String> result = results.get(i);
            if (i == 0) {
                for (Object o : result) {
                    String val = o == null ? null : o.toString();
                    List<String> row = new ArrayList<>(jsonPaths.length);
                    row.add(val);
                    datas.add(row);
                }
            } else {
                for (int j = 0; j < result.size(); j++) {
                    Object o = result.get(j);
                    String val = o == null ? null : o.toString();
                    List<String> row = datas.get(j);
                    row.add(val);
                }
            }
        }
        return datas;
    }

    private void initJsonPath(JsonField jsonField) {
        jsonPaths = new JsonPath[jsonField.getFields().size()];
        for (int index = 0; index < jsonField.getFields().keySet().size(); index++) {
            jsonPaths[index] =
                    JsonPath.compile(
                            jsonField.getFields().values().toArray(new String[] {})[index]);
        }
    }
}
