package org.apache.seatunnel.connectors.seatunnel.http.util;

public enum BodyTypeEnum {
    RAW("raw", "The body data is json"),
    FORM_DATA("form-data", "The body data is form-data"),
    X_WWW_FORM_URLENCODED("x-www-form-urlencoded", "The body data is x-www-form-urlencoded ");

    private final String name;
    private final String description;

    BodyTypeEnum(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
}
