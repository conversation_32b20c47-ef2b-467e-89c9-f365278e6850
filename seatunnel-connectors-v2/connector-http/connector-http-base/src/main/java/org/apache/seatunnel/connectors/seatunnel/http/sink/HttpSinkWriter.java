/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.http.sink;

import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import org.apache.seatunnel.api.serialization.SerializationSchema;
import org.apache.seatunnel.api.sink.SupportMultiTableSinkWriter;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.connectors.seatunnel.common.sink.AbstractSinkWriter;
import org.apache.seatunnel.connectors.seatunnel.http.client.HttpClientProvider;
import org.apache.seatunnel.connectors.seatunnel.http.client.HttpResponse;
import org.apache.seatunnel.connectors.seatunnel.http.config.HttpParameter;
import org.apache.seatunnel.connectors.seatunnel.http.config.HttpRequestMethod;
import org.apache.seatunnel.connectors.seatunnel.http.exception.HttpConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.http.exception.HttpConnectorException;
import org.apache.seatunnel.connectors.seatunnel.http.util.BodyTypeEnum;
import org.apache.seatunnel.connectors.seatunnel.http.util.UrlUtils;
import org.apache.seatunnel.format.json.JsonSerializationSchema;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class HttpSinkWriter extends AbstractSinkWriter<SeaTunnelRow, Void>
        implements SupportMultiTableSinkWriter<Void> {
    protected final HttpClientProvider httpClient;
    protected final SeaTunnelRowType seaTunnelRowType;
    protected final HttpParameter httpParameter;
    protected final SerializationSchema serializationSchema;

    public HttpSinkWriter(SeaTunnelRowType seaTunnelRowType, HttpParameter httpParameter) {
        this(seaTunnelRowType, httpParameter, new JsonSerializationSchema(seaTunnelRowType));
    }

    public HttpSinkWriter(
            SeaTunnelRowType seaTunnelRowType,
            HttpParameter httpParameter,
            SerializationSchema serializationSchema) {
        this.seaTunnelRowType = seaTunnelRowType;
        this.httpParameter = httpParameter;
        this.httpClient = new HttpClientProvider(httpParameter);
        this.serializationSchema = serializationSchema;
    }

    @Override
    public void write(SeaTunnelRow element) throws IOException {
        byte[] serialize = serializationSchema.serialize(element);
        int bytesSize = element.getBytesSize();
        boolean flag = Arrays.stream(element.getFields()).allMatch(field -> field == null);
        if (bytesSize == 0 && flag) {
            return;
        }

        String data = new String(serialize);
        // 这里存储的是通过映射组件，转换之后的数据类型--sink目标表字段类型
        Map<String, SqlType> sourceFieldTypesMap = getFieldTypesMap();

        try {
            HttpRequestMethod httpRequestMethod = httpParameter.getMethod();
            String method = httpRequestMethod.getMethod();
            String url = httpParameter.getUrl();
            Map<String, String> headers = httpParameter.getHeaders();

            Map<String, String> paramOutputKey = httpParameter.getParams();

            List<Map<String, Object>> sinkOutputColumns = httpParameter.getSinkOutputColumns();
            String bodySendType = httpParameter.getBodySendType();

            HttpResponse response = null;
            // only support post web hook
            if (method.equalsIgnoreCase("POST")) {
                if (paramOutputKey.size() == 0 && sinkOutputColumns.size() > 0) { // 只有body
                    if (bodySendType.equalsIgnoreCase(BodyTypeEnum.RAW.getName())) {
                        String body = getBodyData(data, sourceFieldTypesMap, sinkOutputColumns);
                        response = httpClient.doPost(url, headers, body);
                    } else if (bodySendType.equalsIgnoreCase(
                            BodyTypeEnum.X_WWW_FORM_URLENCODED.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        // headers.put("Content-Type","application/x-www-form-urlencoded");
                        response = httpClient.doPost(url, headers, bodyMap);
                    } else if (bodySendType.equalsIgnoreCase(BodyTypeEnum.FORM_DATA.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        // headers.put("Content-Type","multipart/form-data");
                        response = httpClient.doPost(url, headers, bodyMap);
                    }
                } else if (paramOutputKey.size() > 0
                        && sinkOutputColumns.size() == 0) { // 没有body 只有params
                    getParamsData(data, paramOutputKey);
                    response = httpClient.doPost(url, headers, paramOutputKey);
                } else if (paramOutputKey.size() > 0
                        && sinkOutputColumns.size() > 0) { // 有body， 有params
                    if (bodySendType.equalsIgnoreCase(BodyTypeEnum.RAW.getName())) {
                        String body = getBodyData(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        url = UrlUtils.urlJoin(url, paramOutputKey);
                        response = httpClient.doPost(url, headers, new HashMap<>(), body);
                    } else if (bodySendType.equalsIgnoreCase(
                            BodyTypeEnum.X_WWW_FORM_URLENCODED.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        response = httpClient.doPost(url, headers, bodyMap);
                    } else if (bodySendType.equalsIgnoreCase(BodyTypeEnum.FORM_DATA.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        response = httpClient.doPost(url, headers, bodyMap);
                    }
                } else if (paramOutputKey.size() == 0 && sinkOutputColumns.size() == 0) {
                    response = httpClient.doPost(url, headers, new HashMap<>());
                }
            } else if (method.equalsIgnoreCase("PUT")) {
                //                Map<String, String> params = JsonUtils.parseObject(data, new
                // TypeReference<Map<String, String>>() {
                //                });
                //                response = httpClient.doPut(url, headers, params);

                if (paramOutputKey.size() == 0 && sinkOutputColumns.size() > 0) { // 只有body
                    if (bodySendType.equalsIgnoreCase(BodyTypeEnum.RAW.getName())) {
                        String body = getBodyData(data, sourceFieldTypesMap, sinkOutputColumns);
                        response = httpClient.doPut(url, headers, new HashMap<>(), body);
                    } else if (bodySendType.equalsIgnoreCase(
                            BodyTypeEnum.X_WWW_FORM_URLENCODED.getName())) {
                        Map<String, String> paramsMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        paramsMap.putAll(paramOutputKey);
                        // headers.put("Content-Type","application/x-www-form-urlencoded");
                        response = httpClient.doPut(url, headers, paramsMap);
                    } else if (bodySendType.equalsIgnoreCase(BodyTypeEnum.FORM_DATA.getName())) {
                        Map<String, String> paramsMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        paramsMap.putAll(paramOutputKey);
                        // headers.put("Content-Type","multipart/form-data");
                        response = httpClient.doPut(url, headers, paramsMap);
                    }
                } else if (paramOutputKey.size() > 0
                        && sinkOutputColumns.size() == 0) { // 没有body 只有params
                    getParamsData(data, paramOutputKey);
                    response = httpClient.doPut(url, headers, paramOutputKey);
                } else if (paramOutputKey.size() > 0
                        && sinkOutputColumns.size() > 0) { // 有body， 有params
                    if (bodySendType.equalsIgnoreCase(BodyTypeEnum.RAW.getName())) {
                        String body = getBodyData(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        url = UrlUtils.urlJoin(url, paramOutputKey);
                        response = httpClient.doPut(url, headers, new HashMap<>(), body);
                    } else if (bodySendType.equalsIgnoreCase(
                            BodyTypeEnum.X_WWW_FORM_URLENCODED.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        response = httpClient.doPut(url, headers, bodyMap);
                    } else if (bodySendType.equalsIgnoreCase(BodyTypeEnum.FORM_DATA.getName())) {
                        Map<String, String> bodyMap =
                                getBodyMap(data, sourceFieldTypesMap, sinkOutputColumns);
                        getParamsData(data, paramOutputKey); // 更新数据
                        bodyMap.putAll(paramOutputKey);
                        response = httpClient.doPut(url, headers, bodyMap);
                    }
                } else if (paramOutputKey.size() == 0 && sinkOutputColumns.size() == 0) {
                    response = httpClient.doPut(url, headers, new HashMap<>());
                }
            }

            int code = response.getCode();
            String content = response.getContent();
            Map<String, Object> params =
                    JsonUtils.parseObject(content, new TypeReference<Map<String, Object>>() {});
            if (params.get("code") != null) {
                log.info(" getContent:  " + response.getContent());
                code = Integer.parseInt(params.get("code").toString());
                if (code == 500) {
                    String msg =
                            "请检查接口配置: 1.Body参数配置(比如RAW或者FORM表单)! 2.请求类型(比如PUT或者POST)! "
                                    + "3.参数字段是否转驼峰！4.确定你调用的接口是否可以使用！content= "
                                    + content;
                    log.error(msg);
                    throw new HttpConnectorException(HttpConnectorErrorCode.REQUEST_FAILED, msg);
                }
            }
            if (HttpResponse.STATUS_OK == code || code == 0) {
                return;
            }
            if (response.getCode() >= 200 && response.getCode() <= 207) {
                log.info(" 接口请求成功！");
            } else {
                log.error("请检查接口服务是否正常！");
                String msg =
                        String.format(
                                "http client execute exception, http response status code:[%s], content:[%s]",
                                response.getCode(), response.getContent());
                log.error(
                        "http client execute exception, http response status code:[{}], content:[{}]",
                        response.getCode(),
                        response.getContent());
                throw new HttpConnectorException(HttpConnectorErrorCode.REQUEST_FAILED, msg);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new HttpConnectorException(HttpConnectorErrorCode.REQUEST_FAILED, e.getMessage());
        }
    }

    private Map<String, SqlType> getFieldTypesMap() {
        String[] fieldNames = seaTunnelRowType.getFieldNames();
        SeaTunnelDataType<?>[] fieldTypes = seaTunnelRowType.getFieldTypes();
        Map<String, SqlType> fieldTypesMap = new HashMap<>();
        for (int i = 0; i < fieldNames.length; i++) {
            String fieldName = fieldNames[i];
            SqlType type = fieldTypes[i].getSqlType();
            fieldTypesMap.put(fieldName, type);
        }
        return fieldTypesMap;
    }

    private Map<String, String> getBodyMap(
            String data,
            Map<String, SqlType> sourceFieldTypesMap,
            List<Map<String, Object>> sinkOutputColumns) {
        Map<String, String> bodyMap = new HashMap<>();
        Map<String, String> params =
                JsonUtils.parseObject(data, new TypeReference<Map<String, String>>() {});
        for (Map<String, Object> outputKeyMap : sinkOutputColumns) {
            String srcFieldName = outputKeyMap.get("srcColumnName").toString();
            SqlType srcSqlType = sourceFieldTypesMap.get(srcFieldName);
            Object object = outputKeyMap.get("srcColumnType");

            String srcColumnType =
                    object == null
                            ? outputKeyMap.get("destFieldType").toString()
                            : object.toString();
            String srcValue =
                    params.get(srcFieldName) == null ? "" : params.get(srcFieldName).toString();

            String destFieldName = outputKeyMap.get("destFieldName").toString();
            String destFieldType = outputKeyMap.get("destFieldType").toString();
            Object destValue = doTransform(srcColumnType, destFieldType, srcValue, "");
            bodyMap.put(destFieldName, destValue.toString());
        }
        return bodyMap;
    }

    private String getBodyData(
            String data,
            Map<String, SqlType> sourceFieldTypesMap,
            List<Map<String, Object>> sinkOutputColumns) {
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> params =
                JsonUtils.parseObject(data, new TypeReference<Map<String, Object>>() {});
        for (Map<String, Object> outputKeyMap : sinkOutputColumns) {
            String srcFieldName = outputKeyMap.get("srcColumnName").toString();
            SqlType srcSqlType = sourceFieldTypesMap.get(srcFieldName);
            Object object = outputKeyMap.get("srcColumnType");
            String srcColumnType =
                    object == null
                            ? outputKeyMap.get("destFieldType").toString()
                            : object.toString();

            String destFieldName = outputKeyMap.get("destFieldName").toString();
            String destFieldType = outputKeyMap.get("destFieldType").toString();

            String[] split = destFieldName.split("\\.");
            if (split.length >= 2) { // 说明包含子字段
                String parentDestFieldName = split[0];
                String childFieldName = split[1];
                if (bodyMap.containsKey(parentDestFieldName)) {
                    Object oldData = bodyMap.get(parentDestFieldName);
                    Map<String, Object> oldMap = (Map<String, Object>) oldData;
                    // 获取上游字段的数据
                    Object childFieldValue =
                            params.get(srcFieldName) == null
                                    ? params.get(destFieldName)
                                    : params.get(srcFieldName);
                    oldMap.put(
                            childFieldName,
                            childFieldValue == null ? "" : childFieldValue.toString());
                    bodyMap.put(parentDestFieldName, oldMap);
                } else { // 首次写入
                    Map<String, Object> firstMap = new HashMap<>();
                    //   获取上游字段的数据
                    Object childFieldValue =
                            params.get(srcFieldName) == null
                                    ? params.get(destFieldName)
                                    : params.get(srcFieldName);
                    firstMap.put(
                            childFieldName,
                            childFieldValue == null ? "" : childFieldValue.toString());
                    bodyMap.put(parentDestFieldName, firstMap);
                }
            } else {
                Object srcValue =
                        params.get(srcFieldName) == null
                                ? params.get(destFieldName)
                                : params.get(srcFieldName);
                Object destValue =
                        doTransform(
                                srcColumnType,
                                destFieldType,
                                srcValue == null ? "" : srcValue.toString(),
                                "");
                if (bodyMap.containsKey(destFieldName)) {
                    Object oldData = bodyMap.get(destFieldName);
                    Map<String, Object> oldMap = (Map<String, Object>) oldData;
                    if (destValue instanceof Map) {
                        Map<String, Object> newMap = (Map<String, Object>) destValue;
                        oldMap.putAll(newMap);
                    }
                    bodyMap.put(destFieldName, oldMap);
                } else {
                    bodyMap.put(destFieldName, destValue);
                }
            }
        }
        String jsonString = JsonUtils.toJsonString(bodyMap);
        return jsonString;
    }

    private Object doTransform(
            String srcColumnType, String desDataType, String value, String dataFormat) {
        if (desDataType.equalsIgnoreCase("array")) {
            List<Object> list = JsonUtils.toList(value, Object.class);
            return list;
        } else if (desDataType.equalsIgnoreCase("number")) {
            if (srcColumnType.equalsIgnoreCase(SqlType.INT.name())
                    || srcColumnType.equalsIgnoreCase(SqlType.INTEGER.name())
                    || srcColumnType.equalsIgnoreCase(SqlType.SMALLINT.name())
                    || srcColumnType.equalsIgnoreCase(SqlType.TINYINT.name())) {
                return Integer.valueOf(value);
            } else if (srcColumnType.equalsIgnoreCase(SqlType.FLOAT.name())) {
                return Float.valueOf(value);
            } else if (srcColumnType.equalsIgnoreCase(SqlType.DOUBLE.name())) {
                return Double.valueOf(value);
            } else if (srcColumnType.equalsIgnoreCase(SqlType.BIGINT.name())
                    || srcColumnType.equalsIgnoreCase(SqlType.DECIMAL.name())) {
                return new BigDecimal(value);
            } else {
                return value;
            }
        } else if (desDataType.equalsIgnoreCase("boolean")) {
            return Boolean.valueOf(value);
        } else if (desDataType.equalsIgnoreCase("object")) {
            if (srcColumnType.equalsIgnoreCase(SqlType.STRING.name())) {
                Map<String, Object> stringObjectMap =
                        JsonUtils.parseObject(value, new TypeReference<Map<String, Object>>() {});
                return stringObjectMap;
            } else {
                return value;
            }
        } else if (desDataType.equalsIgnoreCase("string")) {
            if (srcColumnType.equalsIgnoreCase(SqlType.DATE.name())) {
                if (StringUtils.isEmpty(dataFormat)) {
                    dataFormat = httpParameter.getDateFormat();
                }
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dataFormat);
                LocalDate data = DateUtils.parse(value, dateTimeFormatter);
                return data.toString();
            } else if (srcColumnType.equalsIgnoreCase(SqlType.TIME.name())) {
                if (StringUtils.isEmpty(dataFormat)) {
                    dataFormat = httpParameter.getTimeFormat();
                }
                TimeUtils.Formatter formatter = TimeUtils.Formatter.parse(dataFormat);
                LocalTime data = TimeUtils.parse(value, formatter);
                String formatterStr = TimeUtils.toString(data, formatter);
                return formatterStr;
            } else if (srcColumnType.equalsIgnoreCase(SqlType.TIMESTAMP.name())
                    || srcColumnType.equalsIgnoreCase(SqlType.TIMESTAMP_TZ.name())
                    || srcColumnType.equalsIgnoreCase("DATETIME")) {
                if (StringUtils.isEmpty(dataFormat)) {
                    dataFormat = httpParameter.getDatetimeFormat();
                }

                DateTimeFormatter dateTimeFormatter = DateTimeUtils.matchDateTimeFormatter(value);
                LocalDateTime data = DateTimeUtils.parse(value, dateTimeFormatter);

                DateTimeUtils.Formatter formatter = DateTimeUtils.Formatter.parse(dataFormat);
                String formatterStr = DateTimeUtils.toString(data, formatter);
                return formatterStr;
            } else {
                return value;
            }
        }
        return value;
    }

    private void getParamsData(String data, Map<String, String> paramOutputKey) {
        Map<String, String> params =
                JsonUtils.parseObject(data, new TypeReference<Map<String, String>>() {});
        // 填充数据，以paramKeys 设置默认值为准
        for (String key : paramOutputKey.keySet()) {
            String value = paramOutputKey.get(key);
            if (StringUtils.isEmpty(value)) {
                paramOutputKey.put(key, params.get(key));
            }
        }
    }

    @Override
    public void close() throws IOException {
        if (Objects.nonNull(httpClient)) {
            httpClient.close();
        }
    }
}
