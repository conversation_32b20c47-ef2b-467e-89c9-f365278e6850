package org.apache.seatunnel.connectors.seatunnel.http.client;

import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;

import java.net.URI;

public class HttpGetWithBody extends HttpEntityEnclosingRequestBase {

    public HttpGetWithBody(String uri) {
        super();
        setURI(URI.create(uri));
    }

    @Override
    public String getMethod() {
        return HttpGet.METHOD_NAME;
    }

    public HttpGetWithBody() {
        super();
    }

    public HttpGetWithBody(final URI uri) {
        super();
        setURI(uri);
    }
}
