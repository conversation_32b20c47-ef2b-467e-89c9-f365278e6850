/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.tdengine;

import org.junit.jupiter.api.Assertions;

import com.taosdata.jdbc.TSDBDriver;
import lombok.SneakyThrows;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Properties;

public class TDengineTest {

    public void testQueryUrl(String jdbcUrl) {
        Assertions.assertDoesNotThrow(
                () -> {
                    try (Connection conn = getConnection(jdbcUrl)) {
                        try (Statement stmt = conn.createStatement()) {
                            ResultSet rs =
                                    stmt.executeQuery(
                                            "SELECT location,AVG(voltage) FROM meters GROUP BY location;");
                        }
                    }
                });
    }

    @SneakyThrows
    private Connection getConnection(String jdbcUrl) {
        Properties connProps = new Properties();
        connProps.setProperty(TSDBDriver.PROPERTY_KEY_BATCH_LOAD, "true");
        return DriverManager.getConnection(jdbcUrl, connProps);
    }
}
