package org.apache.seatunnel.format.json;

import com.jayway.jsonpath.JsonPath;

/**
 * <AUTHOR>
 * @date 2024/12/2
 */
public class JsonPathsUtils {
    public static void main(String[] args) {
        String jsonString =
                "{\"agent_send_timestamp\":1729824765709,\"collector_recv_timestamp\":1729824131727,\"k8s\":{\"k8s_namespace\":\"default\",\"runtime\":\"docker\",\"k8s_labels\":{\"app\":\"tn-controller-manager-container-app-status\",\"tianniu-app-type\":\"controller-manager\"},\"image_id\":\"97a4d2dbaf24fa39c8c5a375f59f29ee3fded05a178b9cd0417c379aabffc957\"},\"ip\":\"**********\",\"source\":\"stdout\",\"hostname\":\"tn-controller-manager\",\"appname\":\"default\",\"tag\":[\"tn_controller_manager\",\"stdout\"],\"timestamp\":1729824762939}";
        String path = "$.k8s.k8s_labels.app";
        Object result = JsonPath.read(jsonString, path);
        System.out.println(result);
    }
}
