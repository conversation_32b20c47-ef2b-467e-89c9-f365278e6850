/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.format.compatible.kafka.connect.json;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

import java.util.Map;

public class KafkaConnectJsonFormatOptions {

    public static final Option<Boolean> KEY_CONVERTER_SCHEMA_ENABLED =
            Options.key("key_converter_schema_enabled")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("kafka connect key converter schema enabled.");

    public static final Option<Boolean> VALUE_CONVERTER_SCHEMA_ENABLED =
            Options.key("value_converter_schema_enabled")
                    .booleanType()
                    .defaultValue(true)
                    .withDescription("kafka connect value converter schema enabled.");

    public static boolean getKeyConverterSchemaEnabled(Map<String, String> options) {
        return Boolean.parseBoolean(
                options.getOrDefault(KEY_CONVERTER_SCHEMA_ENABLED.key(), "true"));
    }

    public static boolean getValueConverterSchemaEnabled(Map<String, String> options) {
        return Boolean.parseBoolean(
                options.getOrDefault(VALUE_CONVERTER_SCHEMA_ENABLED.key(), "true"));
    }
}
