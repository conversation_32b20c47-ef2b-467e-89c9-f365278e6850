mvn spotless:apply
本地构建
mvn clean install -Dmaven.test.skip=true -T 4
打包
mvn clean package install -pl seatunnel-dist -am -Dmaven.test.skip=true

mvn clean install -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -DskipTests  -pl  seatunnel-core/seatunnel-starter  -am

seatunnel.yaml
seatunnel:
engine:
history-job-expire-minutes: 1440
backup-count: 1
queue-type: blockingqueue
print-execution-info-interval: 10
print-job-metrics-info-interval: 10
slot-service:
dynamic-slot: true
checkpoint:
interval: 30000
timeout: 600000
storage:
type: hdfs
max-retained: 3
plugin-config:
namespace: /tmp/seatunnel/checkpoint_snapshot
storage.type: hdfs
fs.defaultFS: file:/// 为本地路径存储

验证
启动zeta引擎
./seatunnel-cluster.sh -d
启动同步任务
./seatunnel.sh --config ./../config/001.conf -e cluster
暂停同步任务
./seatunnel.sh -s 779223181985579009
恢复运行同步任务
./seatunnel.sh --config ./../config/001.conf -r 779223181985579009
取消任务
./seatunnel.sh -can 779223181985579009

使用multi_source_to_multi_sink1.conf 运行的时候，一个jdbc就是一个连接。读写是分开的。
使用multi_source_to_multi_sink2.conf 运行的时候

cdc同步目前要求源端必须有主键

./seatunnel.sh --config ./../config/lihj.conf -e cluster

写到hive，基于cdh
参考https://blog.csdn.net/qq_43224174/article/details/131430223
从cdh机器上/opt/cloudera/parcels/CDH/lib/hive/lib 路径copy文件
-rw-r--r--. 1 <USER> <GROUP> 438744 2月 3 00:49 hive-common-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 438744 2月 3 00:49 hive-common.jar
-rw-r--r--. 1 <USER> <GROUP> 35803898 2月 3 00:16 hive-exec-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 35803898 2月 3 00:50 hive-exec.jar
-rw-r--r--. 1 <USER> <GROUP> 8251305 2月 3 00:37 hive-metastore-2.1.1-cdh6.3.2.jar
-rw-r--r--. 1 <USER> <GROUP> 8251305 2月 3 00:48 hive-metastore.jar
-rw-r--r--. 1 <USER> <GROUP> 313702 2月 3 00:45 libfb303-0.9.3.jar
-rw-r--r--. 1 <USER> <GROUP> 234121 2月 3 00:49 libthrift-0.9.3-1.jar
copy到seatunnel的lib下，然后重启zeta引擎

说明
D:\git_seatunnel\dev_seatunnel\seatunnel 跟随社区进度2.3.4做测试验证
D:\open_git\open_st\seatunnel 社区贡献

postgresqlcdc开启

```shell
postgresql cdc 需要在配置文件postgresql.conf 修改

#------------------------------------------------------------------------------
# WRITE AHEAD LOG
#------------------------------------------------------------------------------

# - Settings -

wal_level = logical                     # minimal, replica, or logical  主要改这里，改为logical pg就可以读取到cdc内容了
                                        # (change requires restart)
#fsync = on                             # flush data to disk for crash safety
                                        # (turning this off can cause
                                        # unrecoverable data corruption)
#synchronous_commit = on                # synchronization level;
                                        # off, local, remote_write, remote_apply, or on
#wal_sync_method = fsync                # the default is the first option

```

oracle cdc

```shell
查看表空间文件位置
select t1.name,t2.name  from v$tablespace t1,v$datafile t2 where t1.ts# = t2.ts#


CREATE TABLESPACE logminer_tbs DATAFILE '/opt/oracle/oradata/ORCLCDB/ORCLPDB1/logminer_tbs.dbf' SIZE 25M REUSE AUTOEXTEND ON MAXSIZE UNLIMITED;
CREATE USER joyadata IDENTIFIED BY joyadata DEFAULT TABLESPACE LOGMINER_TBS QUOTA UNLIMITED ON LOGMINER_TBS;
-- 允许"joyadata"用户创建会话，即允许该用户连接到数据库。
GRANT CREATE SESSION TO joyadata;
-- （不支持Oracle 11g）允许"joyadata"用户在多租户数据库（CDB）中设置容器。
-- GRANT SET CONTAINER TO joyadata;
-- 允许"joyadata"用户查询V_$DATABASE视图，该视图包含有关数据库实例的信息。
GRANT SELECT ON V_$DATABASE TO joyadata;
-- 允许"joyadata"用户执行任何表的闪回操作。
GRANT FLASHBACK ANY TABLE TO joyadata;
-- 允许"joyadata"用户查询任何表的数据。
GRANT SELECT ANY TABLE TO joyadata;
-- 允许"joyadata"用户拥有SELECT_CATALOG_ROLE角色，该角色允许查询数据字典和元数据。
GRANT SELECT_CATALOG_ROLE TO joyadata;
-- 允许"joyadata"用户拥有EXECUTE_CATALOG_ROLE角色，该角色允许执行一些数据字典中的过程和函数。
GRANT EXECUTE_CATALOG_ROLE TO joyadata;
-- 允许"joyadata"用户查询任何事务。
GRANT SELECT ANY TRANSACTION TO joyadata;
-- （不支持Oracle 11g）允许"joyadata"用户进行数据变更追踪（LogMiner）。
-- GRANT LOGMINING TO joyadata;
-- 允许"joyadata"用户创建表。
GRANT CREATE TABLE TO joyadata;
-- 允许"joyadata"用户锁定任何表。
GRANT LOCK ANY TABLE TO joyadata;
-- 允许"joyadata"用户修改任何表。
GRANT ALTER ANY TABLE TO joyadata;
-- 允许"joyadata"用户创建序列。
GRANT CREATE SEQUENCE TO joyadata;
-- 允许"joyadata"用户执行DBMS_LOGMNR包中的过程。
GRANT EXECUTE ON DBMS_LOGMNR TO joyadata;
-- 允许"joyadata"用户执行DBMS_LOGMNR_D包中的过程。
GRANT EXECUTE ON DBMS_LOGMNR_D TO joyadata;
-- 允许"joyadata"用户查询V_$LOG视图，该视图包含有关数据库日志文件的信息。
GRANT SELECT ON V_$LOG TO joyadata;
-- 允许"joyadata"用户查询V_$LOG_HISTORY视图，该视图包含有关数据库历史日志文件的信息。
GRANT SELECT ON V_$LOG_HISTORY TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_LOGS视图，该视图包含有关LogMiner日志文件的信息。
GRANT SELECT ON V_$LOGMNR_LOGS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_CONTENTS视图，该视图包含LogMiner日志文件的内容。
GRANT SELECT ON V_$LOGMNR_CONTENTS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGMNR_PARAMETERS视图，该视图包含有关LogMiner的参数信息。
GRANT SELECT ON V_$LOGMNR_PARAMETERS TO joyadata;
-- 允许"joyadata"用户查询V_$LOGFILE视图，该视图包含有关数据库日志文件的信息。
GRANT SELECT ON V_$LOGFILE TO joyadata;
-- 允许"joyadata"用户查询V_$ARCHIVED_LOG视图，该视图包含已归档的数据库日志文件的信息。
GRANT SELECT ON V_$ARCHIVED_LOG TO joyadata;
-- 允许"joyadata"用户查询V_$ARCHIVE_DEST_STATUS视图，该视图包含有关归档目标状态的信息。
GRANT SELECT ON V_$ARCHIVE_DEST_STATUS TO joyadata;
ALTER DATABASE ADD SUPPLEMENTAL LOG DATA;
ALTER TABLE JOYADATA.EMP_104 ADD SUPPLEMENTAL LOG DATA (ALL) COLUMNS;
```

sqlserver cdc

./seatunnel.sh --config ./../config/mysql2dm.conf -e cluster
./seatunnel.sh --config ./../config/mysql2mysql.conf -e cluster
./seatunnel.sh --config ./../config/mysql2oracle.conf -e cluster
./seatunnel.sh --config ./../config/mysql2postgresql.conf -e cluster
./seatunnel.sh --config ./../config/mysql2sqlserver.conf -e cluster

./seatunnel.sh --config ./../config/cdc/mysql/mysqlcdc.conf -e cluster
./seatunnel.sh --config ./../config/cdc/oracle/00001.conf -e cluster xx
./seatunnel.sh --config ./../config/cdc/postgresql/postgres_cdc_to_mysql.conf -e cluster xx
./seatunnel.sh --config ./../config/cdc/sqlserver/sqlservercdc_to_mysql.conf -e cluster
./seatunnel.sh --config ./../config/es2hive.conf -e cluster

./seatunnel.sh --config 这里是你的conf路径 -e cluster

./seatunnel.sh --config ./../config/mysql2es.conf -e cluster

./seatunnel.sh --config ./../config/m2o.conf -e cluster

sql函数可以写select * from xxx
但是表名称需要写虚拟表（result_table_name）

./seatunnel-cluster.sh -d

argo 建表
SET transaction.type=inceptor;
CREATE TABLE emp_user (id INT,name STRING,address STRING)
CLUSTERED BY (id) INTO 2 BUCKETS STORED AS ORC
TBLPROPERTIES ("transactional"="true");

hazelcast集群配置
https://mp.weixin.qq.com/s/HtK1zUp_KHuJ79KP8rCQ0A

hive 建表
CREATE TABLE `default.lihj_test1`(
`vin` varchar(100),
`brand` varchar(100),
`ym` varchar(100),
`mileage` varchar(100),
`mn` varchar(100))
ROW FORMAT SERDE
'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
'field.delim'='\t',
'serialization.format'='\t')
STORED AS INPUTFORMAT
'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
'hdfs://foton1.cdh.com:8020/user/hive/warehouse/lihj_test1'
TBLPROPERTIES (
'transient_lastDdlTime'='**********')

星环tdh
http://************:8180/#/login  admin/admin
beeline -u ************************ -n hive -p 123456
root/DsgData@321

arogofile

CREATE EXTERNAL TABLE emp_lihj ( emp_id STRING, emp_name STRING, gender STRING,account STRING,org_id STRING,birth_date
STRING,age STRING,nationality STRING,province STRING,email STRING,phone STRING,begin_date STRING,remark
STRING,create_time STRING,update_time STRING)
ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LOCATION '/tmp/lihj';

CREATE EXTERNAL TABLE emp_20240606 (emp_id INT,
emp_name STRING,
gender STRING,
account STRING,
org_id STRING,
birth_date STRING,
age INT,
nationality STRING,
province STRING,
city STRING,
email STRING,
phone STRING,
begin_date DATE,
remark STRING,
create_time TIMESTAMP,
update_time TIMESTAMP )

create table holo_a(id int,name string) stored as holodesk;

Doris 建表语句
CREATE TABLE customer ( c_custkey INT,
c_name VARCHAR ( 26 ),
c_city VARCHAR ( 11 ) )
DUPLICATE KEY ( c_custkey )
DISTRIBUTED BY HASH ( c_custkey ) BUCKETS 10 PROPERTIES ( "replication_num" = "1" );

工银瑞信json
{"agent_send_timestamp":**********250,"collector_recv_timestamp":**********658,"raw_message":"2024-05-30-14:14:
27.**********,,*************,,switch,,huawei,,*******.********,,ARP表3)","ip":"***********","index":"
ops-yotta-********","logical_index":"yotta","logtype":"other","hostname":"SPLUNK230606K00T","appname":"snmp","domain":"
ops","context_id":**********,"raw_message_length":148,"timestamp":**********250}
{"_index":"yotta","raw_message":"<188>Jun 03 2024 10:56:26 JSZX-Huiju %%01SNMP/4/SNMP_IPLOCK(s)[449]:The source IP was
locked because of the failure of login through SNMP.(SourceIuawei.log","switch.sw_name":"JSZX-Huiju","hostname":"
VM_16_9_centos","logtype":"switch","appname":"switch","switch.count":449,"switch.pri":"188","host":"***********","
switch.kvmsgp":1717383386000,"agent_send_timestamp":1717383392400,"switch.severity":4,"collector_recv_timestamp":
1717383393098,"ip":"***********","switch.describe":"警告可能存在某种差错","swiule":"SNMP","context_id":
1717383392400319046,"_id":"AY_cBuyQKQXXgHkGUehZ","switch.logtag":"s","ip_addr":"***********","event_time":1717383386000}


sqlserver建库时需要指定排序规则，否则varchar类型字段中有中文时会乱码，示例：
CREATE DATABASE zsp  
COLLATE Chinese_PRC_CI_AS;

impala 建表语句
CREATE TABLE IF NOT EXISTS default.emp_02 (
emp_id INT,                     
emp_name STRING
)      

-Xms42g -Xmx42g -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/seatunnel/dump/zeta-server -XX:MaxMetaspaceSize=8g -XX:+UseG1GC -XX:+PrintGCDetails -Xloggc:/alidata1/za-seatunnel/logs/gc.log -XX:+PrintGCDateStamps -XX:MaxGCPauseMillis=5000 -XX:InitiatingHeapOccupancyPercent=50 -XX:+UseStringDeduplication -XX:GCTimeRatio=4 -XX:G1ReservePercent=15 -XX:ConcGCThreads=12 -XX:G1HeapRegionSize=32M
16c/64G

doris 建表语句

```sql
CREATE TABLE IF NOT EXISTS test.table_partition_range (
     id BIGINT COMMENT 'id'
    ,name STRING COMMENT '姓名'
    ,gender STRING COMMENT '性别'
    ,age INT COMMENT '年龄'
    ,hero STRING COMMENT '英雄角色'
    ,hero_skill_damage_score DOUBLE COMMENT '英雄技能伤害评分'
    ,partition_value DATE COMMENT '分区值(指定列)'
)
    -- ENGINE=OLAP -- 不指定(默认OLAP)
/* DUPLICATE KEY(`id`) */ -- 默认为明细模型(排序列系统自动选定了前 3 列)
    COMMENT '分区表_range分区'
    PARTITION BY RANGE(partition_value) () /* 本次演示不预建分区 */
    DISTRIBUTED BY HASH(id) BUCKETS AUTO /* 自动分桶 */
    PROPERTIES (
                   "replication_num" = "1",
    -- 设置: 无排序列的默认明细模型
                   "enable_duplicate_without_keys_by_default" = "true"
    -- 使用自动分桶推算的创建语法(该参数可选, 如果不设置默认 "estimate_partition_size" = "10G")
    ,"estimate_partition_size" = "2G"
               )
查看有哪些分区
SHOW PARTITIONS FROM sales_records;
```

oracle类型的数据库 报错这个:	
Caused by: org.apache.seatunnel.engine.common.exception.SeaTunnelEngineException: java.lang.SecurityException: sealing violation: package oracle.jdbc.driver is sealed
在seatunnel-cluster.sh 配置文件 nohup java 后加参数-Dcom.sun.jndi.rmi.securirty.sealing=false

使用oracle9i时需要将lib中的ali-phoenix-shaded-thin-client-5.2.5-HBase-2.x.jar删掉
需要用oracle-10.2.0.4.jar包

hive分区内容不能是中文
CREATE TABLE `zsp_test0828_name2`(
`id` tinyint COMMENT 'null',
`name` varchar(100) COMMENT 'null',
`data_time` timestamp COMMENT 'null')
PARTITIONED BY (`date_1` string COMMENT 'null')
select * from zsp_test0828_name2;
SHOW PARTITIONS zsp_test0828_name2;
truncate table zsp_test0828_name2
ALTER TABLE zsp_test0828_name2 ADD IF NOT EXISTS PARTITION (name = 'zhangsi1')


gbase的jdbc驱动默认useCursorFetch是false，jdbcurl上需要加useCursorFetch=true，否则不分片读取大数据量的表，会内存溢出

农信问题记录
1、hbase认证的server_principal，这个值需要到mrs客户端的hbase/conf/client.env中找
2、hbase的zookeeper_quorum，这个值需要到core-site中找
3、hbase需要加的lib依赖
    commons-validator-1.7.jar
    caffeine-2.8.1.jar
    opentelemetry-api-1.16.0.jar
    opentelemetry-context-1.16.0.jar
    opentelemetry-semconv-1.16.0-alpha.jar
4、直接底层执行st任务可以执行，但是页面非root租户执行时报找不到依赖或连接器，是jar包权限问题
5、hbase的bulkload，需要将mrs客户端的/${mrsClientPath}/HDFS/hadoop/lib/native下的文件放到执行服务器上，
并添加到系统环境变量中，否则会报错：secure io is not possible without native code extensions

亚马逊S3登陆地址（黄哥账号密码****************）Joyadata2024
https://signin.aws.amazon.com/signin?client_id=arn%3Aaws%3Asignin%3A%3A%3Aconsole%2Fcanvas&redirect_uri=https%3A%2F%2Fconsole.aws.amazon.com%2Fconsole%2Fhome%3FhashArgs%3D%2523%26isauthcode%3Dtrue%26state%3DhashArgsFromTB_eu-north-1_4d06b8c755c9ec47&page=resolve&code_challenge=4cWqO6RVVOMj5w7B6YZ8uqBOUBxmA_85B3v3bepelIQ&code_challenge_method=SHA-256

s3写入时jar包组合：
    组合1(普通hadoop-aws包)：hadoop-aws.3.1.4.jar,aws-java-sdk-bundle.1.11.330.jar
    组合2(华为mrs的hadoop-aws包)：hadoop-aws.3.3.1-h0.cbu.mrs.330.r9.jar,aws-java-sdk-bundle.1.11.901.jar

58 /dsg/app/public/ossutil
./ossutil  ls  oss://joyadata   后面是文件路径

写sftp时，tmp_path和path必须在同一个文件系统，否则rename会报错

${file_name}
${file_size}
${file_row}
${file_md5}
${file_date}
文件夹同步有以下参数
${file_count}
${folder_count}


https://www.ibm.com/docs/en/informix-servers
informix连接方式 192.168.5.127  itest itest
连接数据库命令  dbaccess lr -
查看informix版本  onstat -
常看当前连接  onstat -g ses
KILL掉指定会话 onmode -z <sid>
onstat --help  （ 有一大堆命令）
onmode --help  （ 有一大堆命令）


metastore_uri 从hive-site.xml中获取hive.metastore.uris的值



informix  建表，查询不需要处理关键字

CREATE TABLE informix.all0920_lihj13 (
int integer NOT NULL,
select smallint,
from float,
c4 smallfloat,
c5 decimal(5,2),
c6 decimal(4,2),
c7 char(5),
c8 varchar(100),
c9 nchar(5),
c10 nvarchar(30),
c11 date,
c12 datetime year to second,
c13 datetime year to fraction(3),
c14 datetime year to fraction(5),
c17 serial NOT NULL,
c18 money,
c19 text,
c20 byte,
c21 clob,
c22 boolean,
c23 json(4096)
)
select  int,select,from from all0920_lihj13

postgresql、greenplum
建表双引号小写，查询的时候大小写都可以
建表双引号大写，查询的时候必须使用双引号大写

集成传过来 始终带双引号，数据库不支持带双引号的使用单独连接器处理

| 路径                                       | 作用                                  | 日期       |
| ------------------------------------------ |-------------------------------------| ---------- |
| lib/gdb-mysql-connector-java-5.1.46.28.jar | godlendb 驱动，与mysql的time类型会导致精度丢失，移除 | 2025-01-06 |


|                                            |               |            |
|                                            |               |            |



db2编码问题解决以及说明
出现错误
[INFO] 2025-02-12 19:10:57.848 +0800 - 	at org.apache.seatunnel.connectors.seatunnel.jdbc.internal.JdbcInputFormat.nextRecord(JdbcInputFormat.java:218)
[INFO] 2025-02-12 19:10:57.849 +0800 - 	at org.apache.seatunnel.connectors.seatunnel.jdbc.source.JdbcSourceReader.pollNext(JdbcSourceReader.java:68)
[INFO] 2025-02-12 19:10:57.850 +0800 - 	at org.apache.seatunnel.engine.server.task.flow.SourceFlowLifeCycle.collect(SourceFlowLifeCycle.java:150)
[INFO] 2025-02-12 19:10:57.851 +0800 - 	at org.apache.seatunnel.engine.server.task.SourceSeaTunnelTask.collect(SourceSeaTunnelTask.java:127)
[INFO] 2025-02-12 19:10:57.852 +0800 - 	at org.apache.seatunnel.engine.server.task.SeaTunnelTask.stateProcess(SeaTunnelTask.java:168)
[INFO] 2025-02-12 19:10:57.853 +0800 - 	at org.apache.seatunnel.engine.server.task.SourceSeaTunnelTask.call(SourceSeaTunnelTask.java:132)
[INFO] 2025-02-12 19:10:57.854 +0800 - 	at org.apache.seatunnel.engine.server.TaskExecutionService$BlockingWorker.run(TaskExecutionService.java:631)
[INFO] 2025-02-12 19:10:57.855 +0800 - 	at org.apache.seatunnel.engine.server.TaskExecutionService$NamedTaskWrapper.run(TaskExecutionService.java:939)
[INFO] 2025-02-12 19:10:57.857 +0800 - 	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
解决方式
在property中设置属性 db2.jcc.charsetDecoderEncoder=3
属性值分析
值含义
0 默认模式：使用 JDBC 驱动的默认字符集处理方式。通常会自动处理字符集转换，但在某些情况下可能会导致字符集问题。
1 禁用字符集转换：不进行任何字符集转换，直接将字节数据传递给数据库或从数据库读取。适用于需要完全控制字符集处理的场景。
2 启用字符集转换：强制使用 Java 默认字符集（由 file.encoding 系统属性指定）进行字符集转换。适用于简单的字符集处理场景。
3 智能字符集转换：根据数据库的字符集设置和 Java 环境的字符集设置，自动选择合适的字符集进行转换。推荐用于处理多字节字符集（如 UTF-8）的场景。
在 IBM DB2 的 JDBC 驱动中，`db2.jcc.charsetDecoderEncoder` 属性用于控制字符编码和解码的行为。除了设置为 `"3"`（使用 ICU 库），还有其他可选值，具体如下：

### `db2.jcc.charsetDecoderEncoder` 的可选值
1. **`0`**（默认值）：
    - 使用 Java 默认的字符编码和解码机制。
    - 适用于大多数简单场景，但对复杂的字符集（如某些 Unicode 转换）支持可能不够完善。
2. **`1`**：
    - 使用 DB2 JDBC 驱动自带的字符编码和解码逻辑。
    - 提供比 Java 默认机制更好的兼容性，但可能不如 ICU 库强大。
3. **`2`**：
    - 使用 ICU 库进行字符编码和解码，但仅在必要时（例如，当 Java 默认机制无法处理时）。
    - 这是一个折中方案，兼顾性能和兼容性。
4. **`3`**：
    - 强制使用 ICU 库进行所有字符编码和解码。
    - 提供最全面的字符集支持，适合需要处理多语言或复杂字符集的场景。

hive jdbc 插入动态分区。
SET hive.exec.dynamic.partition.mode=nonstrict
在 INSERT INTO ... PARTITION 语句中，分区字段必须放在 VALUES 的最后。例如，如果表有两个分区字段 (year STRING, month STRING)，则 VALUES 的顺序应为 (id, name, salary, year, month)。

https://github.com/apache/doris/pull/38215
doris streamload 读如果版本低于2.1.8 时间类型如果是datetime,会读出来是1970年，使用jdbc读没问题。


星环tdh的hive(inceptor)
1、inceptor使用inceptor8驱动时，要将st的lib中的seatunnel-hadoop3-3.1.4-uber.jar删掉，因为有一个org.apache.hadoop.net.NetUtils类有冲突
2、core-site文件中有个key为oauth2的参数，要改为false

镇江医院doris读数不准(由于doris快照引起的，数据频繁在写入),目前解决方案是先写临时表(select into )然后查询临时表

map {key:value,key2:value2}
array [value1,value2]
seatunnel 实现逻辑
会将,转换为  \u0002
会将:转换为  \u0003
会将数组和对应的[]和{}移除
原始数据
"48656c6c6f576f726c64",["apple","banana","cherry"],{"name":"John Doe","email":"<EMAIL>"}
修正后的数据应该是
"48656c6c6f576f726c64","apple""banana""cherry","name""John Doe""email""<EMAIL>"

```
mergesftpfile情况下，batchSize是不生效的，因为每次获取到的都是同一个文件名称,所以文件内容一致是追加的，finalName字段值是文件名称，不包含路径即可。
为什么在mergefile-sftp、mergefile-s3等的resources/META-INF.services
META-INF/services/org.apache.hadoop.fs.FileSystem 文件的作用：注册自定义的文件系统实现类，供 Hadoop/SeaTunnel 自动发现和加载。
local 不需要，是因为本地文件系统实现已经在 Hadoop 里注册好了，不需要你再重复注册。
如果你要扩展自己的文件系统（比如 SFTP），一定要有这个 SPI 文件，否则 Hadoop/SeaTunnel 找不到你的实现类，就会报 ClassNotFoundException。
```