#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# This mapping is used to resolve the Jar package name without version (or call artifactId)
# corresponding to the module in the user Config, helping SeaTunnel to load the correct Jar package.

## *** WARNING **** : `seatunnel.source.XXX`, the `XXX` should be string which SeaTunnelSource::getPluginName and TableSinkFactory::factoryIdentifier returned value##

# SeaTunnel Connector-V2

seatunnel.source.FakeSource = connector-fake
seatunnel.sink.Console = connector-console

