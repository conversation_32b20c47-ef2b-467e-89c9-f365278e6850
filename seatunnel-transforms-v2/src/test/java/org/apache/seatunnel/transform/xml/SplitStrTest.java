package org.apache.seatunnel.transform.xml;

public class SplitStrTest {

    public static void main(String[] args) {
        String data = "1$$$$";
        data = "1级$False$否$是$$是$$无$否$无$无;$否$无$是$否$无$无;$$无;$$不适用$$$否$否$$无".replace("$", "@");
        //        String data ="1$@$@";
        //        String  data ="1@@@@";
        //        String  data ="1####";
        String lineSplit = getRealLineSplint("@#$$");
        System.out.println(lineSplit);
        String[] split = data.split(lineSplit, -1);
        System.out.println(split.length);
    }

    private static String getRealLineSplint(String split) {
        String lineSplit = split;
        if (lineSplit.contains("$") || lineSplit.indexOf("$") > 0) {
            lineSplit = lineSplit.replace("$", "@");
        }
        return lineSplit;
    }
}
