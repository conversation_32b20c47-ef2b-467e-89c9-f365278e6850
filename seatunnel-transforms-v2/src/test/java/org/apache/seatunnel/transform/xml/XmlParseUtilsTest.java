package org.apache.seatunnel.transform.xml;

import org.apache.seatunnel.transform.xpath.util.XmlParseUtils;

import org.dom4j.Document;
import org.dom4j.io.SAXReader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XmlParseUtilsTest {

    public static void main(String[] args) {
        Map<String, String> elements = new HashMap<String, String>();
        elements.put("name", "张三");
        elements.put("sex", "男");
        elements.put("age", "20");
        Document doc = XmlParseUtils.createDocumentFromXmlString("<students></students>");
        doc.getRootElement().addAttribute("year", "2000");

        XmlParseUtils.addNodeElement(doc.getRootElement(), "student", "20090313001", elements);

        elements.clear();
        elements.put("name", "李四");
        elements.put("sex", "男");
        elements.put("age", "21");
        XmlParseUtils.addNodeElement(doc.getRootElement(), "student", "20090313002", elements);

        //        String filePath = "D:/test.xml";
        //        String filePath = "E:\\Cindasc\\DI-Code\\data\\test.xml";
        String filePath = "E:\\Cindasc\\DI-Code\\data\\test01.xml";
        String xmlData = XmlParseUtils.readXMLAsString(filePath);
        //        System.out.println(xmlData);
        //        XmlParseUtils.createXMLFile(doc, filePath, "UTF-8");
        //        System.out.println(XmlParseUtils.readXMLAsString(filePath));

        //        XmlParseUtils.printElementsByXPath(filePath,
        // "/students/student[@id=\"20090313001\"]/name");
        // XMLUtils.printAllChildNode(doc.getRootElement());

        //        List<String> elementsByXPath =
        // XmlParseUtils.getElementsByXmlContextAndXPathV2(xmlData, "/data/values/value[@id]",
        // "xxx");
        //        for (String name : elementsByXPath) {
        //            System.out.println("name : " + name);
        //        }

        //        String filePath1 =
        // "E:\\SeaTunnel\\SeaTunnel-2.3.9\\seatunnel-transforms-v2\\src\\main\\java\\org\\apache\\seatunnel\\transform\\xmlpath\\xml\\test01.xml";
        //        String filePath2 =
        // "E:\\SeaTunnel\\SeaTunnel-2.3.9\\seatunnel-transforms-v2\\src\\main\\java\\org\\apache\\seatunnel\\transform\\xmlpath\\xml\\test02.xml";
        //        List<String> elementsByXPath = XmlParseUtils.getElementsByXPath(filePath2,
        // "/conf/grades/grade/studentName");
        //        List<String> elementsByXPath = XmlParseUtils.getElementsByXPath(filePath2,
        // "/conf/grades/grade[@class!=\"003\"]/studentName");
        //        List<String> elementsByXPath = XmlParseUtils.getElementsByXPath(filePath2,
        // "/conf/grades/grade[@class=\"003\"]/studentName");

        //        List<String> elementsByXPath = XmlParseUtils.getElementsByXPath(filePath2,
        // "/conf/user/name");
        //        for (String name : elementsByXPath) {
        //            System.out.println("name : " + name);
        //        }

        //        String xmlData = " <user>\n" +
        //                "\t     <id>1</id>\n" +
        //                "\t    <name>张三</name>\n" +
        //                "\t    <age>20</age>\n" +
        //                "\t    <addr>北京</addr>\n" +
        //                "</user>  ";
        //
        //        String xmlData2 = "<conf> <user>\n" +
        //                "\t     <id>1</id>\n" +
        //                "\t    <name>张三</name>\n" +
        //                "\t    <age>20</age>\n" +
        //                "\t    <addr>北京</addr>\n" +
        //                "</user> </conf> ";
        //
        //
        String xmlData3 =
                "<?xml version=\"1.0\" encoding=\"utf-16\"?>  <conf> <users> <user>\n"
                        + "\t     <id>1</id>\n"
                        + "\t    <name>张三</name>\n"
                        + "\t    <age>20</age>\n"
                        + "\t    <addr>北京</addr>\n"
                        + "</user> </users> </conf> ";

        String xmlData4 = "";
        //
        ////        StringReader stringReader = new StringReader(xmlData);
        ////        BufferedReader bufferedReader = new BufferedReader(stringReader);
        ////        List<String> elementsByXPath1 = XmlParseUtils.getElementsByXPath(bufferedReader,
        // "/user/name");
        ////        for (String name : elementsByXPath1) {
        ////            System.out.println("name : " + name);
        ////        }
        ////        stringReader.close();
        //
        ////        List<String> elementsByXPath1 =
        // XmlParseUtils.getElementsByXmlContextAndXPath(xmlData, "/user/name");
        ////        List<String> elementsByXPath1 =
        // XmlParseUtils.getElementsByXmlContextAndXPath(xmlData2, "/conf/user/name");
        SAXReader saxReader = new SAXReader();
        long t1 = System.currentTimeMillis();
        int num = 1024;
        for (int n = 0; n < num; n++) {
            for (int i = 0; i < 100; i++) {
                List<String> elementsByXPath1 =
                        XmlParseUtils.getElementsByXmlContextAndXPathV3(
                                saxReader,
                                xmlData,
                                "/EmrCoverPage/PatientBasicInfo/CurrentAddress",
                                "11");
                for (String name : elementsByXPath1) {
                    //                    System.out.println("name : " + name);
                }
            }
        }
        long t2 = System.currentTimeMillis();

        System.out.println("查询 " + num + " 条数据，花费时间：" + (t2 - t1) + " 毫秒");
    }
}
