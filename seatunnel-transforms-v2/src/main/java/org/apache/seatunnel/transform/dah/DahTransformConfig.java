package org.apache.seatunnel.transform.dah;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

/**
 * 资产中心脱敏
 *
 * <AUTHOR>
 * @date 2024/9/20
 */
public class DahTransformConfig {
    public static Option<String> DAH_IDENTIFIER =
            Options.key("dah_identifier")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("资产中心脱敏组件名称");
    public static Option<String> DAH_COLUMN =
            Options.key("dah_column").stringType().noDefaultValue().withDescription("脱敏字段名称");
    public static Option<String> DAH_SALT =
            Options.key("dah_salt").stringType().noDefaultValue().withDescription("盐");
    public static Option<String> DAH_ALGOITHM_REGEX =
            Options.key("dah_algorithmRegex")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("正则表达式");
    public static Option<String> DAH_ALGOITHM_PARAMETER =
            Options.key("dah_algorithmParameter")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("参数字符串");
}
