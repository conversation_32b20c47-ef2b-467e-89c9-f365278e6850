package org.apache.seatunnel.transform.replaceall;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

import com.google.auto.service.AutoService;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@AutoService(Factory.class)
public class ReplaceAllTransformFactory implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return "ReplaceAll";
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        CatalogTable catalogTable = context.getCatalogTables().get(0);
        return () -> new ReplaceAllTransform(context.getOptions(), catalogTable);
    }
}
