package org.apache.seatunnel.transform.replaceall;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
public class ReplaceAllTransformConfig implements Serializable {
    public static final Option<String> KEY_REPLACE_FIELD =
            Options.key("replace_field")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("The field you want to replace");

    public static final Option<List<Map<String, List<String>>>> REPLACE_FIELD_PATTERNS =
            Options.key("replace_field_patterns")
                    .listMapListType()
                    .noDefaultValue()
                    .withDescription("");

    public static final Option<List<String>> KEY_PATTERN =
            Options.key("pattern")
                    .listType()
                    .noDefaultValue()
                    .withDescription("The old string that will be replaced");

    public static final Option<List<String>> KEY_REPLACEMENT =
            Options.key("replacement")
                    .listType()
                    .noDefaultValue()
                    .withDescription("The new string for replace");

    public static final Option<Boolean> KEY_IS_REGEX =
            Options.key("is_regex")
                    .booleanType()
                    .defaultValue(false)
                    .withDescription("Use regex for string match");

    public static final Option<Boolean> KEY_REPLACE_FIRST =
            Options.key("replace_first")
                    .booleanType()
                    .noDefaultValue()
                    .withDescription("Replace the first match string");
}
