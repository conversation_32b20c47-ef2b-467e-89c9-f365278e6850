package org.apache.seatunnel.transform.dynamiccolumn;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

import com.google.auto.service.AutoService;

/**
 * <AUTHOR>
 * @date 2024/4/14
 */
@AutoService(Factory.class)
public class DynamicColumnTransformFactory implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return "DynamicColumn";
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().required(DynamicColumnTransformConfig.DYNAMIC_FIELD).build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        DynamicColumnTransformConfig dynamicColumnTransformConfig =
                DynamicColumnTransformConfig.of(context.getOptions());
        CatalogTable catalogTable = context.getCatalogTables().get(0);
        return () -> new DynamicColumnTransform(dynamicColumnTransformConfig, catalogTable);
    }
}
