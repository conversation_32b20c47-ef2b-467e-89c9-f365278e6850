package org.apache.seatunnel.transform.xpath;

import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;

import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;

@ToString
public class XmlColumnConfig implements Serializable {

    private final String xpath;

    private final String srcField;

    private final String destField;

    private final String dataFormat;

    private final String defaultValue;

    @Getter private final Column destColumn;

    public XmlColumnConfig(
            String xpath,
            String srcField,
            String destField,
            String dataFormat,
            String defaultValue,
            Column destColumn) {
        this.xpath = xpath;
        this.srcField = srcField;
        this.destField = destField;
        this.dataFormat = dataFormat;
        this.defaultValue = defaultValue;
        this.destColumn = destColumn;
    }

    public String getXpath() {
        return xpath;
    }

    public String getSrcField() {
        return srcField;
    }

    public String getDestField() {
        return destField;
    }

    public SeaTunnelDataType<?> getDestType() {
        return destColumn.getDataType();
    }

    public String getDataFormat() {
        return dataFormat;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public Column getDestColumn() {
        return destColumn;
    }
}
