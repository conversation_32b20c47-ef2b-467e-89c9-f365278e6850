package org.apache.seatunnel.transform.dynamiccolumn;

import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.type.BasicType;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.MultipleFieldOutputTransform;
import org.apache.seatunnel.transform.common.SeaTunnelRowAccessor;
import org.apache.seatunnel.transform.exception.TransformCommonError;

import lombok.NonNull;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/4/14
 */
public class DynamicColumnTransform extends MultipleFieldOutputTransform {
    private final DynamicColumnTransformConfig dynamicColumnTransformConfig;
    private final int splitFieldIndex;

    public DynamicColumnTransform(
            @NonNull DynamicColumnTransformConfig dynamicColumnTransformConfig,
            @NonNull CatalogTable catalogTable) {
        super(catalogTable);
        this.dynamicColumnTransformConfig = dynamicColumnTransformConfig;
        SeaTunnelRowType seaTunnelRowType = catalogTable.getTableSchema().toPhysicalRowDataType();
        try {
            splitFieldIndex = 0;
        } catch (IllegalArgumentException e) {
            throw TransformCommonError.cannotFindInputFieldError(
                    getPluginName(), dynamicColumnTransformConfig.getDynamicField().toString());
        }
        this.outputCatalogTable = getProducedCatalogTable();
    }

    @Override
    public String getPluginName() {
        return "Split";
    }

    @Override
    protected Object[] getOutputFieldValues(SeaTunnelRowAccessor inputRow) {
        Object splitFieldValue = inputRow.getField(splitFieldIndex);
        if (splitFieldValue == null) {
            return null;
        }

        String[] splitFieldValues = getDynamicValue(dynamicColumnTransformConfig.getDynamicField());
        String[] tmp = splitFieldValues;
        splitFieldValues = new String[dynamicColumnTransformConfig.getDynamicField().size()];
        System.arraycopy(tmp, 0, splitFieldValues, 0, tmp.length);
        return splitFieldValues;
    }

    private String[] getDynamicValue(Map<String, String> dynamicField) {
        List<String> list = new ArrayList<>();
        dynamicField.keySet().stream()
                .forEach(
                        key -> {
                            String method = dynamicField.get(key);
                            if (method.equalsIgnoreCase("UUID")) {
                                list.add(UUID.randomUUID().toString().replaceAll("-", ""));
                            } else if (method.equalsIgnoreCase("hash")) {
                                list.add(String.valueOf("AAA".hashCode()));
                            }
                        });
        return list.toArray(new String[0]);
    }

    @Override
    protected Column[] getOutputColumns() {
        return Arrays.stream(dynamicColumnTransformConfig.getDynamicField().keySet().toArray())
                .map(
                        fieldName ->
                                PhysicalColumn.of(
                                        (String) fieldName,
                                        BasicType.STRING_TYPE,
                                        200,
                                        true,
                                        "",
                                        ""))
                .toArray(Column[]::new);
    }
}
