package org.apache.seatunnel.transform.dynamiccolumn;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/14
 */
@Getter
@Setter
public class DynamicColumnTransformConfig implements Serializable {

    public static final Option<Map<String, String>> DYNAMIC_FIELD =
            Options.key("dynamic_field")
                    .mapType()
                    .noDefaultValue()
                    .withDescription(
                            "Specify the field mapping relationship between input and output");

    private Map<String, String> dynamicField;

    public static DynamicColumnTransformConfig of(ReadonlyConfig config) {
        DynamicColumnTransformConfig dynamicColumnTransformConfig =
                new DynamicColumnTransformConfig();
        dynamicColumnTransformConfig.setDynamicField(config.get(DYNAMIC_FIELD));
        return dynamicColumnTransformConfig;
    }
}
