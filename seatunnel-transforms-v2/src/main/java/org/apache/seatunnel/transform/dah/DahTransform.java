package org.apache.seatunnel.transform.dah;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.SeaTunnelRowAccessor;
import org.apache.seatunnel.transform.common.SingleFieldOutputTransform;
import org.apache.seatunnel.transform.exception.TransformCommonError;

import org.apache.commons.collections4.CollectionUtils;

import com.joyadata.algorithm.util.AlgorithmConcealUtil;
import com.joyadata.algorithm.util.AlgorithmHashUtil;
import lombok.NonNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资产中心脱敏
 *
 * <AUTHOR>
 * @date 2024/9/20
 */
public class DahTransform extends SingleFieldOutputTransform {
    private final ReadonlyConfig config;
    private int inputFieldIndex;

    public DahTransform(@NonNull ReadonlyConfig config, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = config;
        initOutputFields(
                inputCatalogTable.getTableSchema().toPhysicalRowDataType(),
                this.config.get(DahTransformConfig.DAH_COLUMN));
    }

    private void initOutputFields(SeaTunnelRowType inputRowType, String replaceField) {
        try {
            inputFieldIndex = inputRowType.indexOf(replaceField);
        } catch (IllegalArgumentException e) {
            throw TransformCommonError.cannotFindInputFieldError(getPluginName(), replaceField);
        }
    }

    @Override
    public String getPluginName() {
        return "Dah";
    }

    @Override
    protected Object getOutputFieldValue(SeaTunnelRowAccessor inputRow) {
        Object inputFieldValue = inputRow.getField(inputFieldIndex);
        if (null == inputFieldValue) {
            return null;
        }
        String name = config.get(DahTransformConfig.DAH_IDENTIFIER);
        Map<String, Function<String, Object>> transformations = new HashMap<>();

        transformations.put("emptyString", AlgorithmConcealUtil::emptyString);
        transformations.put("nullValue", AlgorithmConcealUtil::nullValue);
        transformations.put("starValue", AlgorithmConcealUtil::starValue);
        transformations.put(
                "wordReplacement",
                value ->
                        AlgorithmConcealUtil.wordReplacement(
                                value,
                                config.get(DahTransformConfig.DAH_ALGOITHM_REGEX),
                                config.get(DahTransformConfig.DAH_ALGOITHM_PARAMETER)));
        transformations.put(
                "selfMaskDefault",
                value ->
                        AlgorithmConcealUtil.selfMaskDefault(
                                value, config.get(DahTransformConfig.DAH_ALGOITHM_PARAMETER)));
        transformations.put(
                "selfMaskCustom",
                value ->
                        AlgorithmConcealUtil.selfMaskCustom(
                                value, config.get(DahTransformConfig.DAH_ALGOITHM_PARAMETER)));
        transformations.put("retainName", AlgorithmConcealUtil::retainName);
        transformations.put("maskPhoneNumber", AlgorithmConcealUtil::maskPhoneNumber);
        transformations.put("maskPhoneNumberVarying", AlgorithmConcealUtil::maskPhoneNumberVarying);
        transformations.put("maskBankCardNumber", AlgorithmConcealUtil::maskBankCardNumber);
        transformations.put(
                "maskBankCardNumberVarying", AlgorithmConcealUtil::maskBankCardNumberVarying);
        transformations.put("idNumber", AlgorithmConcealUtil::idNumber);
        transformations.put("idNumberVarying", AlgorithmConcealUtil::idNumberVarying);
        transformations.put("anonymizeName", AlgorithmConcealUtil::anonymizeName);
        transformations.put("anonymizePhoneNumber", AlgorithmConcealUtil::anonymizePhoneNumber);
        transformations.put(
                "anonymizePhoneNumberVarying", AlgorithmConcealUtil::anonymizePhoneNumberVarying);
        transformations.put("email", AlgorithmConcealUtil::email);
        transformations.put("desensitizeAddress", AlgorithmConcealUtil::desensitizeAddress);
        transformations.put("sha256", AlgorithmHashUtil::sha256);
        transformations.put(
                "sha256WithSalt",
                value ->
                        AlgorithmHashUtil.sha256WithSalt(
                                value, config.get(DahTransformConfig.DAH_SALT)));
        transformations.put("sha384", AlgorithmHashUtil::sha384);
        transformations.put(
                "sha384WithSalt",
                value ->
                        AlgorithmHashUtil.sha384WithSalt(
                                value, config.get(DahTransformConfig.DAH_SALT)));
        transformations.put("sha512", AlgorithmHashUtil::sha512);
        transformations.put(
                "sha512WithSalt",
                value ->
                        AlgorithmHashUtil.sha512WithSalt(
                                value, config.get(DahTransformConfig.DAH_SALT)));
        transformations.put("md5", AlgorithmHashUtil::md5);
        transformations.put(
                "md5WithSalt",
                value ->
                        AlgorithmHashUtil.md5WithSalt(
                                value, config.get(DahTransformConfig.DAH_SALT)));
        transformations.put("base64Encode", AlgorithmHashUtil::base64Encode);
        // 将输入值转换为字符串
        String inputValueString = inputFieldValue.toString();
        // 查找对应的转换函数
        Function<String, Object> transformationFunction = transformations.get(name);
        // 如果没有找到，则使用默认函数
        if (transformationFunction == null) {
            transformationFunction = v -> v; // 默认返回输入值本身
        }
        // 应用转换函数
        Object outputValue = transformationFunction.apply(inputValueString);
        // 返回最终结果
        return outputValue;

        // if("sha384WithSalt".equals(name)){
        //    String content =
        // AlgorithmHashUtil.sha384WithSalt(inputFieldValue.toString(),config.get(DahTransformConfig.DAH_SALT));
        //    System.out.println(content);
        //    return content;
        // }
        // return inputFieldValue;
    }

    @Override
    protected Column getOutputColumn() {
        List<Column> columns = inputCatalogTable.getTableSchema().getColumns();
        List<Column> collect =
                columns.stream()
                        .filter(
                                column ->
                                        column.getName()
                                                .equals(config.get(DahTransformConfig.DAH_COLUMN)))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw TransformCommonError.cannotFindInputFieldError(
                    getPluginName(), config.get(DahTransformConfig.DAH_COLUMN));
        }
        return collect.get(0).copy();
    }
}
