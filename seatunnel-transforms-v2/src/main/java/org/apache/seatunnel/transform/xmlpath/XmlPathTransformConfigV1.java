package org.apache.seatunnel.transform.xmlpath;

import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.SeaTunnelDataTypeConvertorUtil;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.transform.exception.TransformCommonError;
import org.apache.seatunnel.transform.exception.TransformException;
import org.apache.seatunnel.transform.xpath.XmlColumnConfig;
import org.apache.seatunnel.transform.xpath.util.XmlPathConstants;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.apache.seatunnel.transform.exception.JsonPathTransformErrorCode.DEST_FIELD_MUST_NOT_EMPTY;
import static org.apache.seatunnel.transform.exception.JsonPathTransformErrorCode.PATH_MUST_NOT_EMPTY;
import static org.apache.seatunnel.transform.exception.JsonPathTransformErrorCode.SRC_FIELD_MUST_NOT_EMPTY;

@Getter
@Setter
public class XmlPathTransformConfigV1 implements Serializable {

    public static final Option<String> XPATH =
            Options.key("xpath")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("JSONPath for Selecting Field from JSON.");

    public static final Option<String> SRC_FIELD =
            Options.key("srcField")
                    .stringType()
                    .noDefaultValue()
                    .withDescription("JSON source field.");

    public static final Option<String> DEST_FIELD =
            Options.key("destField").stringType().noDefaultValue().withDescription("output field.");

    public static final Option<String> DEST_TYPE =
            Options.key("destType")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<String> DATA_FORMAT =
            Options.key("dataFormat")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<String> DEFAULT_VALUE =
            Options.key("defaultValue")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<List<Map<String, String>>> MAPPING_KEYS =
            Options.key("mapping_keys")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("mapping_keys");

    public static final Option<List<Map<String, String>>> KEY_OUTPUT_FIELDS =
            Options.key("output_fields")
                    .type(new TypeReference<List<Map<String, String>>>() {})
                    .noDefaultValue()
                    .withDescription("The result output fields ");

    // 全局时间格式

    public static final Option<String> TIME_FORMAT =
            Options.key("timeFormat")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<String> DATE_FORMAT =
            Options.key("dateFormat")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<String> DATETIME_FORMAT =
            Options.key("datetimeFormat")
                    .stringType()
                    .defaultValue("string")
                    .withDescription("output field type,default string");

    public static final Option<String> LINE_SPLIT =
            Options.key("lineSplit").stringType().defaultValue("#").withDescription(" line split ");

    private final List<XmlColumnConfig> columnConfigs;
    private List<Map<String, String>> mappingKeys;

    //    private Map<String, String> inputColumns;
    private List<Map<String, String>> outputFields;

    /** 日期类型格式（默认值:yyyy-MM-dd） */
    private String dateFormat;
    /** 日期时间类型格式（默认值:yyyy-MM-dd HH:mm:ss） */
    private String datetimeFormat;
    /** 时间类型格式(默认值:HH:mm:ss） */
    private String timeFormat;

    private String lineSplit = XmlPathConstants.SPLIT;

    public XmlPathTransformConfigV1(List<XmlColumnConfig> columnConfigs) {
        this.columnConfigs = columnConfigs;
    }

    public List<XmlColumnConfig> getColumnConfigs() {
        return columnConfigs;
    }

    public static XmlPathTransformConfigV1 of(ReadonlyConfig config, CatalogTable table) {

        //        if (!config.toConfig().hasPath(INPUT_COLUMNS.key())) {
        //            throw new TransformException(
        //                    COLUMNS_MUST_NOT_EMPTY, COLUMNS_MUST_NOT_EMPTY.getErrorMessage());
        //        }
        //        List<String> columns = config.get(INPUT_COLUMNS);

        List<Map<String, String>> outputFields = config.get(KEY_OUTPUT_FIELDS);

        List<Map<String, String>> mappingKeys = config.get(MAPPING_KEYS);

        List<XmlColumnConfig> configs = new ArrayList<>(mappingKeys.size());
        for (Map<String, String> map : mappingKeys) {
            checkColumnConfig(map);

            String xpath = map.get(XPATH.key());
            String srcField = map.get(SRC_FIELD.key());
            String destField = map.get(DEST_FIELD.key());
            String type = map.getOrDefault(DEST_TYPE.key(), DEST_TYPE.defaultValue());
            String dataFormat = map.get(DATA_FORMAT.key());
            String defaultValue = map.get(DEFAULT_VALUE.key());
            SeaTunnelDataType<?> srcFieldDataType =
                    SeaTunnelDataTypeConvertorUtil.deserializeSeaTunnelDataType(srcField, type);

            List<Column> columns = table.getTableSchema().getColumns();
            List<String> columnNames =
                    columns.stream().map(Column::getName).collect(Collectors.toList());

            if (!columnNames.contains(srcField)) {
                throw TransformCommonError.cannotFindInputFieldError("XmlPath", srcField);
            }
            int i = columnNames.indexOf(srcField);

            Column srcFieldColumn = columns.get(i);
            Column destFieldColumn =
                    PhysicalColumn.of(
                            destField,
                            srcFieldDataType,
                            srcFieldColumn.getColumnLength(),
                            true,
                            null,
                            null);

            XmlColumnConfig columnConfig =
                    new XmlColumnConfig(
                            xpath, srcField, destField, dataFormat, defaultValue, destFieldColumn);
            configs.add(columnConfig);
        }

        // 配置类
        XmlPathTransformConfigV1 xmlPathTransformConfigV2 = new XmlPathTransformConfigV1(configs);
        xmlPathTransformConfigV2.setMappingKeys(mappingKeys);
        xmlPathTransformConfigV2.setOutputFields(outputFields);

        xmlPathTransformConfigV2.setTimeFormat(config.get(TIME_FORMAT));
        xmlPathTransformConfigV2.setDateFormat(config.get(DATE_FORMAT));
        xmlPathTransformConfigV2.setDatetimeFormat(config.get(DATETIME_FORMAT));
        xmlPathTransformConfigV2.setLineSplit(config.get(LINE_SPLIT));

        return xmlPathTransformConfigV2;
    }

    private static void checkColumnConfig(Map<String, String> map) {
        String path = map.get(XPATH.key());
        if (StringUtils.isBlank(path)) {
            throw new TransformException(
                    PATH_MUST_NOT_EMPTY, PATH_MUST_NOT_EMPTY.getErrorMessage());
        }
        String srcField = map.get(SRC_FIELD.key());
        if (StringUtils.isBlank(srcField)) {
            throw new TransformException(
                    SRC_FIELD_MUST_NOT_EMPTY, SRC_FIELD_MUST_NOT_EMPTY.getErrorMessage());
        }
        String destField = map.get(DEST_FIELD.key());
        if (StringUtils.isBlank(destField)) {
            throw new TransformException(
                    DEST_FIELD_MUST_NOT_EMPTY, DEST_FIELD_MUST_NOT_EMPTY.getErrorMessage());
        }
    }
}
