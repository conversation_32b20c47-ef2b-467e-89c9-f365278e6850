package org.apache.seatunnel.transform.xpath;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;
import org.apache.seatunnel.transform.xpath.util.XmlPathConstants;

import com.google.auto.service.AutoService;

import java.util.List;

@AutoService(Factory.class)
public class XmlPathTransformFactoryV2 implements TableTransformFactory {

    @Override
    public String factoryIdentifier() {
        return XmlPathConstants.PLUGIN_NAME_V2;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().optional(XmlPathTransformConfigV2.MAPPING_KEYS).build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        List<CatalogTable> catalogTables = context.getCatalogTables();
        CatalogTable catalogTable = catalogTables.get(0);
        ReadonlyConfig options = context.getOptions();
        XmlPathTransformConfigV2 xmlPathTransformConfigV2 =
                XmlPathTransformConfigV2.of(options, catalogTable);
        return () -> new XmlPathCatalogTransformV2(xmlPathTransformConfigV2, catalogTable);
    }
}
