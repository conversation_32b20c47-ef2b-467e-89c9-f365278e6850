package org.apache.seatunnel.transform.replaceall;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.type.ArrayType;
import org.apache.seatunnel.api.table.type.MapType;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.transform.common.MultipleFieldOutputTransform;
import org.apache.seatunnel.transform.common.SeaTunnelRowAccessor;

import org.apache.commons.lang3.StringEscapeUtils;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONObject;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@Slf4j
public class ReplaceAllTransform extends MultipleFieldOutputTransform {
    private final ReadonlyConfig config;

    private List<String> fieldNames;
    private List<Integer> fieldOriginalIndexes;
    private List<SeaTunnelDataType<?>> fieldTypes;

    private Map<String, Map<String, List<String>>> replaceFieldPatterns;
    private Map<String, List<String>> listKeysMap = new HashMap<>();
    private Map<String, List<String>> listReplacementMap = new HashMap<>();

    public ReplaceAllTransform(
            @NonNull ReadonlyConfig config, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = config;
        SeaTunnelRowType seaTunnelRowType =
                inputCatalogTable.getTableSchema().toPhysicalRowDataType();
        initOutputFields(seaTunnelRowType);
    }

    private void initOutputFields(SeaTunnelRowType inputRowType) {
        List<String> fieldNames = new ArrayList<>();
        List<Integer> fieldOriginalIndexes = new ArrayList<>();
        List<SeaTunnelDataType<?>> fieldsType = new ArrayList<>();
        for (int i = 0; i < inputRowType.getFieldNames().length; i++) {
            String fieldName = inputRowType.getFieldName(i);
            fieldNames.add(fieldName);
            fieldOriginalIndexes.add(inputRowType.indexOf(fieldName));
            fieldsType.add(inputRowType.getFieldType(inputRowType.indexOf(fieldName)));
        }
        this.fieldNames = fieldNames;
        this.fieldOriginalIndexes = fieldOriginalIndexes;
        this.fieldTypes = fieldsType;
        this.replaceFieldPatterns =
                getReplaceFieldPatterns(
                        config.get(ReplaceAllTransformConfig.REPLACE_FIELD_PATTERNS));
        for (int i = 0; i < fieldOriginalIndexes.size(); i++) {
            if (null != replaceFieldPatterns
                    && replaceFieldPatterns.containsKey(fieldNames.get(i))) {
                Map<String, List<String>> stringListMap =
                        replaceFieldPatterns.get(fieldNames.get(i));
                List<String> listKeys = stringListMap.get("pattern");
                List<String> listReplacement = stringListMap.get("replacement");
                if (null == listKeys || listKeys.size() == 0) {
                    continue;
                }
                List<String> unescapeListKeys = new ArrayList<>();
                for (int j = 0; j < listKeys.size(); j++) {
                    String key = listKeys.get(j);
                    // 20250303 zsp所有要转换的值都反转义一次，可以把字符\n转为真正的换行符，\\n转为字符\n
                    // 如果要把字符\n替换调，页面需要设置\\n
                    String actualChar = StringEscapeUtils.unescapeJava(key);
                    unescapeListKeys.add(j, actualChar);
                }
                listKeysMap.put(fieldNames.get(i), unescapeListKeys);
                listReplacementMap.put(fieldNames.get(i), listReplacement);
            }
        }
        log.info(
                "ReplaceAllTransform----listKeysMap--> {},listReplacement-->{}",
                JSONObject.toJSONString(listKeysMap),
                JSONObject.toJSONString(listReplacementMap));
    }

    private Map<String, Map<String, List<String>>> getReplaceFieldPatterns(
            List<Map<String, List<String>>> maps) {
        Map<String, Map<String, List<String>>> result = new HashMap<>();
        if (null == maps || maps.isEmpty()) return result;
        for (Map<String, List<String>> map : maps) {
            String fieldName = map.get("field_name").get(0);
            Map<String, List<String>> stringListMap = new HashMap<>();
            stringListMap.put("pattern", map.get("pattern"));
            stringListMap.put("replacement", map.get("replacement"));
            result.put(fieldName, stringListMap);
        }
        return result;
    }

    @Override
    public String getPluginName() {
        return "ReplaceAll";
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow inputRow) {
        return super.transformRow(inputRow);
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    protected Object[] getOutputFieldValues(SeaTunnelRowAccessor inputRow) {
        Object[] fieldValues = new Object[fieldNames.size()];
        for (int i = 0; i < fieldOriginalIndexes.size(); i++) {
            Object value = inputRow.getField(fieldOriginalIndexes.get(i));
            value =
                    replaceAll1(
                            value,
                            listKeysMap.get(fieldNames.get(i)),
                            listReplacementMap.get(fieldNames.get(i)));
            //                for (int j = 0; j < listKeys.size(); j++) {
            //                    String replaceKey =listKeys.get(j);
            //                    String replaceValue = listReplacement.get(j);
            //                    if(null != value && null!=replaceKey){
            //                        if(null == replaceValue){
            //                            value = value.toString().replaceAll(listKeys.get(j),"");
            //                            value = value.toString().replace(listKeys.get(j),"");
            //                        }else {
            //                            value =
            // value.toString().replaceAll(listKeys.get(j),replaceValue);
            //                            value =
            // value.toString().replace(listKeys.get(j),replaceValue);
            //                        }
            //                    }else if(null == value && null == replaceKey){
            //                        value = replaceValue;
            //                    }
            //                }
            fieldValues[i] = clone(fieldNames.get(i), fieldTypes.get(i), value);
        }
        return fieldValues;
    }

    public static Object replaceAll1(Object str, List<String> targets, List<String> replacements) {
        if (null == str
                || targets == null
                || replacements == null
                || targets.size() != replacements.size()) {
            return str;
        }
        StringBuilder build = new StringBuilder(str.toString());
        for (int i = 0; i < targets.size(); i++) {
            String target = targets.get(i);
            String replacement = replacements.get(i);
            if (null == replacement) {
                replacement = "";
            }
            int index = 0;
            while ((index = build.indexOf(target, index)) != -1) {
                build.replace(index, index + target.length(), replacement);
                index += replacement.length(); // Move index forward to avoid infinite loops
            }
        }
        return build.toString();
    }

    private Object clone(String field, SeaTunnelDataType<?> dataType, Object value) {
        if (value == null) {
            return null;
        }
        switch (dataType.getSqlType()) {
            case BOOLEAN:
            case STRING:
            case TINYINT:
            case SMALLINT:
            case INT:
            case BIGINT:
            case FLOAT:
            case DOUBLE:
            case DECIMAL:
            case DATE:
            case TIME:
            case TIMESTAMP:
                return value;
            case BYTES:
                byte[] bytes = (byte[]) value;
                byte[] newBytes = new byte[bytes.length];
                System.arraycopy(bytes, 0, newBytes, 0, bytes.length);
                return newBytes;
            case ARRAY:
                ArrayType arrayType = (ArrayType) dataType;
                Object[] array = (Object[]) value;
                Object newArray =
                        Array.newInstance(arrayType.getElementType().getTypeClass(), array.length);
                for (int i = 0; i < array.length; i++) {
                    Array.set(newArray, i, clone(field, arrayType.getElementType(), array[i]));
                }
                return newArray;
            case MAP:
                MapType mapType = (MapType) dataType;
                Map map = (Map) value;
                Map<Object, Object> newMap = new HashMap<>();
                for (Object key : map.keySet()) {
                    newMap.put(
                            clone(field, mapType.getKeyType(), key),
                            clone(field, mapType.getValueType(), map.get(key)));
                }
                return newMap;
            case ROW:
                SeaTunnelRowType rowType = (SeaTunnelRowType) dataType;
                SeaTunnelRow row = (SeaTunnelRow) value;

                Object[] newFields = new Object[rowType.getTotalFields()];
                for (int i = 0; i < rowType.getTotalFields(); i++) {
                    newFields[i] =
                            clone(
                                    rowType.getFieldName(i),
                                    rowType.getFieldType(i),
                                    row.getField(i));
                }
                SeaTunnelRow newRow = new SeaTunnelRow(newFields);
                newRow.setRowKind(row.getRowKind());
                newRow.setTableId(row.getTableId());
                return newRow;
            case NULL:
                return null;
            default:
                throw CommonError.unsupportedDataType(
                        getPluginName(), dataType.getSqlType().toString(), field);
        }
    }

    @Override
    protected Column[] getOutputColumns() {
        List<Column> columns = inputCatalogTable.getTableSchema().getColumns();
        return columns.toArray(new Column[0]);
    }
}
