package org.apache.seatunnel.transform.common;

import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.transform.SeaTunnelFlatMapTransform;

import java.util.List;

public abstract class AbstractSeaTunnelFlatMapTransform
        implements SeaTunnelFlatMapTransform<SeaTunnelRow> {

    protected String inputTableName;
    protected SeaTunnelRowType inputRowType;

    protected SeaTunnelRowType outputRowType;

    @Override
    public SeaTunnelDataType<SeaTunnelRow> getProducedType() {
        return outputRowType;
    }

    @Override
    public List<SeaTunnelRow> flatMap(SeaTunnelRow row) {
        return transformRow(row);
    }

    /**
     * Outputs transformed row data.
     *
     * @param inputRow upstream input row data
     */
    protected abstract List<SeaTunnelRow> transformRow(SeaTunnelRow inputRow);

    @Override
    public CatalogTable getProducedCatalogTable() {
        throw new UnsupportedOperationException(
                String.format(
                        "Connector %s must implement TableTransformFactory.createTransform method",
                        getPluginName()));
    }
}
