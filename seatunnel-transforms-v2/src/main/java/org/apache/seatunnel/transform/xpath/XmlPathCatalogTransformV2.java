package org.apache.seatunnel.transform.xpath;

import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.ConstraintKey;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.PrimaryKey;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportTransform;
import org.apache.seatunnel.transform.exception.TransformCommonError;
import org.apache.seatunnel.transform.xpath.util.XmlParseUtils;
import org.apache.seatunnel.transform.xpath.util.XmlPathConstants;

import org.apache.commons.lang3.StringUtils;

import lombok.NonNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class XmlPathCatalogTransformV2 extends AbstractCatalogSupportTransform {

    private XmlPathTransformConfigV2 config;

    private final SeaTunnelRowType seaTunnelRowType;

    private int[] srcFieldIndexArr;

    private Column[] outputColumns;

    private CatalogTable inputCatalogTable;

    public XmlPathCatalogTransformV2(
            @NonNull XmlPathTransformConfigV2 config, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = config;
        this.inputCatalogTable = inputCatalogTable;
        this.seaTunnelRowType = inputCatalogTable.getTableSchema().toPhysicalRowDataType();
        init();
    }

    private void init() {
        initSrcFieldIndexArr();
        initOutputSeaTunnelRowType();
    }

    private void initOutputSeaTunnelRowType() {
        this.outputColumns =
                this.config.getColumnConfigs().stream()
                        .map(XmlColumnConfig::getDestColumn)
                        .toArray(Column[]::new);
    }

    private void initSrcFieldIndexArr() {
        List<XmlColumnConfig> columnConfigs = this.config.getColumnConfigs();
        Set<String> fieldNameSet = new HashSet<>(Arrays.asList(seaTunnelRowType.getFieldNames()));
        this.srcFieldIndexArr = new int[columnConfigs.size()];

        for (int i = 0; i < columnConfigs.size(); i++) {
            XmlColumnConfig columnConfig = columnConfigs.get(i);
            String srcField = columnConfig.getSrcField();
            if (!fieldNameSet.contains(srcField)) {
                throw TransformCommonError.cannotFindInputFieldError(getPluginName(), srcField);
            }
            this.srcFieldIndexArr[i] = this.seaTunnelRowType.indexOf(srcField);
        }
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow inputRow) {

        List<XmlColumnConfig> configs = this.config.getColumnConfigs();
        List<Map<String, String>> outputFields = this.config.getOutputFields();

        Object[] outputDataArray = new Object[configs.size() + outputFields.size()];
        int size = configs.size();
        for (int i = 0; i < size; i++) {
            int pos = this.srcFieldIndexArr[i];
            XmlColumnConfig fieldConfig = configs.get(i);
            Object fieldValue = inputRow.getField(pos);
            String xpath = fieldConfig.getXpath();
            String defaultValue = fieldConfig.getDefaultValue();
            List<String> data =
                    XmlParseUtils.getElementsByXmlContextAndXPathV2(
                            fieldValue.toString(), xpath, defaultValue);
            // System.out.println(xpath + "---xpath--->" + StringUtils.join(data, ","));
            // SeaTunnelDataType<?> inputDataType = seaTunnelRowType.getFieldType(pos);
            SeaTunnelDataType<?> desDataType = fieldConfig.getDestColumn().getDataType();
            String value = StringUtils.join(data, this.config.getLineSplit());
            outputDataArray[i] = doTransform(desDataType, value, fieldConfig.getDataFormat());
        }

        int index = size;
        for (Map<String, String> map : outputFields) {
            String name = map.get("fieldName");
            String fieldType = map.get("fieldType");
            String dataFormat = map.get("dataFormat");
            int pos = this.seaTunnelRowType.indexOf(name);
            Object value = inputRow.getField(pos);
            // SeaTunnelDataType<?> srcFieldDataType =
            // SeaTunnelDataTypeConvertorUtil.deserializeSeaTunnelDataType(name, fieldType);
            if (null == value) {
                outputDataArray[index] = value;
            } else {
                outputDataArray[index] = value;
            }
            index++;
        }

        SeaTunnelRow outputRow = new SeaTunnelRow(outputDataArray);
        outputRow.setRowKind(inputRow.getRowKind());
        outputRow.setTableId(inputRow.getTableId());
        return outputRow;
    }

    private Object doTransform(SeaTunnelDataType<?> desDataType, String value, String dataFormat) {
        SqlType sqlType = desDataType.getSqlType();
        if (sqlType.equals(SqlType.STRING)) {
            return value;
        } else if (sqlType.equals(SqlType.DOUBLE)) {
            return Double.valueOf(value);
        } else if (sqlType.equals(SqlType.INT)) {
            return Integer.valueOf(value);
        } else if (sqlType.equals(SqlType.FLOAT)) {
            return Float.valueOf(value);
        } else if (sqlType.equals(SqlType.DATE)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getDateFormat();
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dataFormat);
            LocalDate data = DateUtils.parse(value, dateTimeFormatter);
            return data;
        } else if (sqlType.equals(SqlType.TIME)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getTimeFormat();
            }
            TimeUtils.Formatter formatter = TimeUtils.Formatter.parse(dataFormat);
            LocalTime data = TimeUtils.parse(value, formatter);
            return data;
        } else if (sqlType.equals(SqlType.TIMESTAMP)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getDatetimeFormat();
            }
            DateTimeUtils.Formatter formatter = DateTimeUtils.Formatter.parse(dataFormat);
            LocalDateTime data = DateTimeUtils.parse(value, formatter);
            return data;
        }
        return value;
    }

    @Override
    protected TableSchema transformTableSchema() {

        List<ConstraintKey> outputConstraintKeys =
                inputCatalogTable.getTableSchema().getConstraintKeys().stream()
                        .map(ConstraintKey::copy)
                        .collect(Collectors.toList());

        PrimaryKey copiedPrimaryKey =
                inputCatalogTable.getTableSchema().getPrimaryKey() == null
                        ? null
                        : inputCatalogTable.getTableSchema().getPrimaryKey().copy();

        TableSchema tableSchema = inputCatalogTable.getTableSchema();

        List<Column> transformColumns = new ArrayList<>();
        List<XmlColumnConfig> columnConfigs = this.config.getColumnConfigs();

        for (XmlColumnConfig config : columnConfigs) {
            String name = config.getDestField();
            SeaTunnelDataType<?> dataType = config.getDestColumn().getDataType();
            transformColumns.add(PhysicalColumn.of(name, dataType, 0, true, null, null));
        }

        // 这里从配置中获取
        List<Map<String, String>> outputFields = this.config.getOutputFields();
        Map<String, Map<String, String>> outputFieldNameMaps = new HashMap<>();
        for (Map<String, String> map : outputFields) {
            String name = map.get("fieldName");
            outputFieldNameMaps.put(name, map);
        }
        // 这里从source表获取schema信息
        for (Column column : tableSchema.getColumns()) {
            SeaTunnelDataType<?> dataType = column.getDataType();
            String name = column.getName();
            if (outputFieldNameMaps.containsKey(name)) {
                transformColumns.add(PhysicalColumn.of(name, dataType, 0, true, null, null));
            }
        }

        return TableSchema.builder()
                .primaryKey(copiedPrimaryKey)
                .columns(transformColumns)
                .constraintKey(outputConstraintKeys)
                .build();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return XmlPathConstants.PLUGIN_NAME_V2;
    }
}
