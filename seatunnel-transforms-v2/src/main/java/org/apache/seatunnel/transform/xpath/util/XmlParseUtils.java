package org.apache.seatunnel.transform.xpath.util;

import org.apache.seatunnel.common.utils.JsonUtils;

import org.apache.commons.lang3.StringUtils;

import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class XmlParseUtils {

    /**
     * 生成xml文件
     *
     * @param doc
     * @param fileName
     * @param encoding
     */
    public static void createXMLFile(Document doc, String fileName, String encoding) {
        XMLWriter writer = null;
        try {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(encoding)) {
                OutputFormat format = OutputFormat.createPrettyPrint();
                format.setEncoding(encoding); // 指定XML编码
                writer = new XMLWriter(new FileWriter(fileName), format);
            } else {
                writer = new XMLWriter(new FileWriter(fileName));
            }
            writer.write(doc);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建Document并返回根结点
     *
     * @param root_label 根标签
     * @return
     */
    public static Document createDocument(String root_label) {
        Document doc = DocumentHelper.createDocument();
        doc.setRootElement(doc.addElement(root_label));
        return doc;
    }

    /**
     * 将字符串转化为XML
     *
     * @param xml_string
     * @return
     */
    public static Document createDocumentFromXmlString(String xml_string) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(xml_string)) {
            xml_string = "<students> <class>test</class> </students>";
        }
        Document document = null;
        try {
            document = DocumentHelper.parseText(xml_string);
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return document;
    }

    /**
     * 给结点加子结点
     *
     * @param root 相对根
     * @param newNode 新结点
     * @param nodeId 结点Id
     * @param elements 结点元素
     */
    public static void addNodeElement(Element root, String newNode, String nodeId, Map elements) {
        Element _newNode = root.addElement(newNode);
        _newNode.addAttribute("id", nodeId).addAttribute("no", nodeId); // 设置属性

        Iterator it = elements.entrySet().iterator();
        while (it.hasNext()) { // 结点下的元素与值
            Map.Entry entry = (Map.Entry) it.next();
            Object key = entry.getKey();
            Object value = entry.getValue();
            _newNode.addElement((String) key).setText((String) value);
        }
    }

    /**
     * 将文档或节点的XML转化为字符串
     *
     * @param filePath
     * @return
     */
    public static String readXMLAsString(String filePath) {
        SAXReader reader = new SAXReader();
        Document document = null;
        try {
            document = reader.read(new File(filePath));
            // Element root=document.getRootElement();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return document.asXML();
    }

    /**
     * 打印节点的所有子节点
     *
     * @param element
     */
    public static void printAllChildNode(Element element) {
        // 循环当前节点属性
        Iterator attrs = element.attributeIterator();
        while (attrs.hasNext()) {
            Attribute attr = (Attribute) attrs.next();
            System.out.println(attr.getName() + "=" + attr.getText());
        }

        // 循环其孩子元素节点
        Iterator elements = element.elementIterator();
        while (elements.hasNext()) {
            Element ele = (Element) elements.next();
            if (ele.attribute("id") == null) {
                System.out.println(ele.getName() + "=" + ele.getText());
            }

            // 递归调用
            printAllChildNode(ele);
        }
    }

    /**
     * * 以xpath读取数据
     *
     * @param filePath
     * @param xpath
     */
    public static void printElementsByXPath(String filePath, String xpath) {
        SAXReader reader = new SAXReader();
        try {
            Document doc = reader.read(new File(filePath));
            List properties = doc.selectNodes(xpath);
            Iterator it = properties.iterator();
            while (it.hasNext()) {
                Element elm = (Element) it.next();
                System.out.println(elm.getText());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static List<String> getElementsByXPath(String filePath, String xpath) {
        SAXReader reader = new SAXReader();
        List<String> list = new ArrayList<>();
        try {
            Document doc = reader.read(new File(filePath));
            List<Node> properties = doc.selectNodes(xpath);

            Iterator<Node> it = properties.iterator();

            while (it.hasNext()) {
                Element elm = (Element) it.next();
                //                System.out.println(elm.getText());
                list.add(elm.getText());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return list;
    }

    public static List<String> getElementsByXPath(InputStream inputStream, String xpath) {
        SAXReader reader = new SAXReader();
        List<String> list = new ArrayList<>();
        try {
            Document doc = reader.read(inputStream);
            List properties = doc.selectNodes(xpath);
            Iterator it = properties.iterator();
            while (it.hasNext()) {
                Element elm = (Element) it.next();
                //                System.out.println(elm.getText());
                list.add(elm.getText());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return list;
    }

    public static List<String> getElementsByXPath(Reader reader, String xpath) {
        SAXReader saxReader = new SAXReader();
        List<String> list = new ArrayList<>();
        try {
            Document doc = saxReader.read(reader);
            List properties = doc.selectNodes(xpath);
            Iterator it = properties.iterator();
            while (it.hasNext()) {
                Element elm = (Element) it.next();
                //                System.out.println(elm.getText());
                list.add(elm.getText());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return list;
    }

    public static List<String> getElementsByXmlContextAndXPath(String xmlContext, String xpath) {

        StringReader stringReader = new StringReader(xmlContext);
        BufferedReader bufferedReader = new BufferedReader(stringReader);
        SAXReader saxReader = new SAXReader();
        List<String> list = new ArrayList<>();
        try {
            saxReader.setEncoding("UTF-8");
            Document doc = saxReader.read(bufferedReader);
            List properties = doc.selectNodes(xpath);
            Iterator it = properties.iterator();
            while (it.hasNext()) {
                Element elm = (Element) it.next();
                list.add(elm.getText());
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        } finally {
            try {
                bufferedReader.close();
                stringReader.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return list;
    }

    public static List<String> getElementsByXmlContextAndXPathV2(
            String xmlContext, String xpath, String defaultValue) {
        return getElementsByXmlContextAndXPathV2(new SAXReader(), xmlContext, xpath, defaultValue);
    }

    public static List<String> getElementsByXmlContextAndXPathV2(
            SAXReader saxReader, String xmlContext, String xpath, String defaultValue) {
        List<String> list = new ArrayList<>();
        if (xmlContext.equals("")) {
            log.info("原始XML数据：[" + xmlContext + "]");
            return list;
        }
        StringReader stringReader = new StringReader(xmlContext.replace("\n", ""));
        BufferedReader bufferedReader = new BufferedReader(stringReader);
        try {
            saxReader.setEncoding("UTF-8");
            Document doc = saxReader.read(bufferedReader);

            // List properties0 = doc.selectNodes(xpath);
            xpath = xpath.replace("[@", "/").replace("]", "");

            int i = xpath.lastIndexOf("/");
            String preXpath = xpath.substring(0, i);

            String lastXpath = xpath.substring(i + 1, xpath.length());
            List<Node> nodesByXpath = doc.selectNodes(preXpath);
            if (nodesByXpath.size() == 0) { // 当获取不到，设置默认值
                list.add(defaultValue);
            }
            for (Node node : nodesByXpath) {
                List<Node> nodes = node.selectNodes(lastXpath);

                if (nodes.size() == 0) {
                    Element element = (Element) node;
                    Attribute attribute = element.attribute(lastXpath);
                    if (attribute != null) {
                        String value = attribute.getValue();
                        if (StringUtils.isNotEmpty(value)) {
                            list.add(value);
                        } else {
                            list.add(defaultValue);
                        }
                    } else {
                        list.add(defaultValue);
                    }
                } else if (nodes.size() > 0) {
                    List<String> tmpList = new ArrayList<>();
                    for (Node dataNode : nodes) {
                        String text = dataNode.getText();
                        if (text.equals("")) {
                            tmpList.add(defaultValue);
                        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(text.trim())) {
                            tmpList.add(text);
                        }
                    }
                    if (tmpList.size() > 0) {
                        // list.add(StringUtils.join(tmpList, "|"));
                        list.addAll(tmpList);
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage() + ", 原始XML数据：[" + xmlContext + "]");
        } finally {
            try {
                bufferedReader.close();
                stringReader.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return list;
    }

    public static List<String> getElementsByXmlContextAndXPathV3(
            SAXReader saxReader, String xmlContext, String xpath, String defaultValue) {
        List<String> list = new ArrayList<>();
        if (xmlContext.equals("")) {
            log.info("原始XML数据：[" + xmlContext + "]");
            return list;
        }
        InputStream inputStream = new ByteArrayInputStream(xmlContext.replace("\n", "").getBytes());
        try {
            saxReader.setEncoding("UTF-8");
            Document doc = saxReader.read(inputStream);
            // List properties0 = doc.selectNodes(xpath);
            xpath = xpath.replace("[@", "/").replace("]", "");

            int i = xpath.lastIndexOf("/");
            String preXpath = xpath.substring(0, i);

            String lastXpath = xpath.substring(i + 1, xpath.length());
            List<Node> nodesByXpath = doc.selectNodes(preXpath);
            if (nodesByXpath.size() == 0) { // 当获取不到，设置默认值
                list.add(defaultValue);
            }
            for (Node node : nodesByXpath) {
                List<Node> nodes = node.selectNodes(lastXpath);

                if (nodes.size() == 0) {
                    Element element = (Element) node;
                    Attribute attribute = element.attribute(lastXpath);
                    if (attribute != null) {
                        String value = attribute.getValue();
                        if (StringUtils.isNotEmpty(value)) {
                            list.add(value);
                        } else {
                            list.add(defaultValue);
                        }
                    } else {
                        list.add(defaultValue);
                    }
                } else if (nodes.size() > 0) {
                    List<String> tmpList = new ArrayList<>();
                    for (Node dataNode : nodes) {
                        String text = dataNode.getText();
                        if (text.equals("")) {
                            tmpList.add(defaultValue);
                        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(text.trim())) {
                            tmpList.add(text);
                        }
                    }
                    if (tmpList.size() > 0) {
                        // list.add(StringUtils.join(tmpList, "|"));
                        list.addAll(tmpList);
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage() + ", 原始XML数据：[" + xmlContext + "]");
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return list;
    }

    // 递归解析XML节点生成树
    //    递归解析XML节点生成树public TreeNode parseNode( String filePath) {
    //        // 使用JDOM库加载XML文件
    //        SAXBuilder builder = new SAXBuilder();
    //        Document document = builder.build(new File(filePath));
    //        // 获取XML文件根节点
    //        Element root = document.getRootElement();
    //
    //        TreeNode node = new TreeNode(root.getName());
    //        List<Element> children = element.getChildren();
    //        for (Element child : children) {
    //            node.addChild(parseNode(child));
    //        }
    //        return node;
    //    }
    //
    //    // 输出树结构
    //    public void printTree(TreeNode node, int level) {
    //        for (int i = 0; i < level; i++) {
    //            System.out.print("--");
    //        }
    //        System.out.println(node.getName());
    //        List<TreeNode> children = node.getChildren();
    //        for (TreeNode child : children) {
    //            printTree(child, level + 1);
    //        }
    //    }

    public static String getXmlToJsonTree(String xmlData) {
        Map<String, Object> data = new HashMap<>();
        try {
            // 解析 XML
            SAXReader saxBuilder = new SAXReader();
            String replaceData = xmlData.replace("utf-16", "utf-8").replace("UTF-16", "UTF-8");
            Document document = saxBuilder.read(new ByteArrayInputStream(replaceData.getBytes()));
            // 获取根元素
            Element rootElement = document.getRootElement();
            String rootName = rootElement.getName();

            String path = "/" + rootName;
            // 转换为树形结构
            Set<String> xpathSet = new HashSet<>(); // 去掉重复的xpath
            xpathSet.add(path);
            Map<String, Object> nodeTree = convertElementToMap(path, rootElement, xpathSet);
            // 打印结果
            // System.out.println(nodeTree);
            data.put(rootName, nodeTree);

            xpathSet.clear();
            String jsonString = JsonUtils.toJsonString(data);
            return jsonString;
        } catch (DocumentException e) {
            e.printStackTrace();
            return JsonUtils.toJsonString(data);
        }
    }

    /** 将 DOM4j 的 Element 转换为树形结构（Map 和 List） */
    private static Map<String, Object> convertElementToMap(
            String path, Element element, Set<String> xpathSet) {
        Map<String, Object> result = new HashMap<>();
        // 遍历属性
        if (element.attributes().size() > 0) {
            List<Attribute> attributes = element.attributes();
            if (attributes.size() > 0) {
                for (Attribute attribute : attributes) {
                    String attributeName = attribute.getName();
                    Map<String, Object> tmp0 = new HashMap<>();
                    String tmpXpath = path + "[@" + attributeName + "]";
                    tmp0.put("xpath", tmpXpath);
                    tmp0.put("name", attributeName);
                    tmp0.put("value", attribute.getValue());
                    tmp0.put("type", "p");
                    if (!xpathSet.contains(tmpXpath)) {
                        result.put(attributeName, tmp0);
                        xpathSet.add(tmpXpath);
                    }
                }
            }
        }

        // 遍历子节点元素
        for (Element child : element.elements()) {
            String childName = child.getName();

            // 如果子元素还有子元素，递归处理
            if (child.elements().size() > 0) {
                if (result.containsKey(childName)) {
                    // 如果已经存在同名节点，将其转换为列表
                    Object existingValue = result.get(childName);
                    List<Object> list;
                    if (existingValue instanceof List) {
                        list = (List<Object>) existingValue;
                    } else {
                        list = new ArrayList<>();
                        list.add(existingValue);
                    }
                    Map<String, Object> stringObjectMap =
                            convertElementToMap(path + "/" + childName, child, xpathSet);
                    if (stringObjectMap.size() > 0) {
                        list.add(stringObjectMap);
                    }
                    if (list.size() > 0) {
                        result.put(childName, list);
                    }
                } else {
                    Map<String, Object> stringObjectMap =
                            convertElementToMap(path + "/" + childName, child, xpathSet);
                    if (stringObjectMap.size() > 0) {
                        result.put(childName, stringObjectMap);
                    }
                }
            } else {
                // 如果是叶子节点，直接存储文本内容
                String xpath = path + "/" + childName;
                String textTrim = child.getTextTrim();
                Map<String, Object> tmp = new HashMap<>();
                tmp.put("xpath", xpath);
                tmp.put("name", childName);
                tmp.put("value", textTrim);
                tmp.put("type", "n");

                if (child.attributes().size() > 0) {
                    List<Map<String, Object>> list = new ArrayList<>();
                    List<Attribute> attributes = child.attributes();
                    for (Attribute attribute : attributes) {
                        String attributeName = attribute.getName();
                        Map<String, Object> tmp0 = new HashMap<>();
                        String tmpPath = xpath + "[@" + attributeName + "]";
                        tmp0.put("xpath", tmpPath);
                        tmp0.put("name", attributeName);
                        tmp0.put("value", attribute.getValue());
                        tmp0.put("type", "p");
                        if (!xpathSet.contains(tmpPath)) {
                            list.add(tmp0);
                            xpathSet.add(tmpPath);
                        }
                    }
                    list.add(tmp);
                    result.put(childName, list);
                } else {
                    if (!xpathSet.contains(xpath)) {
                        result.put(childName, tmp);
                        xpathSet.add(xpath);
                    }
                }
            }
        }

        return result;
    }

    private static Map<String, Object> convertElementToMapNoXPath(Element element) {
        Map<String, Object> result = new HashMap<>();
        // 遍历子元素
        for (Element child : element.elements()) {
            String childName = child.getName();

            // 如果子元素还有子元素，递归处理
            if (child.elements().size() > 0) {
                if (result.containsKey(childName)) {
                    // 如果已经存在同名节点，将其转换为列表
                    Object existingValue = result.get(childName);
                    List<Object> list;
                    if (existingValue instanceof List) {
                        list = (List<Object>) existingValue;
                    } else {
                        list = new ArrayList<>();
                        list.add(existingValue);
                    }
                    list.add(convertElementToMapNoXPath(child));
                    result.put(childName, list);
                } else {
                    result.put(childName, convertElementToMapNoXPath(child));
                }
            } else {
                // 如果是叶子节点，直接存储文本内容
                result.put(childName, child.getTextTrim());
            }
        }

        return result;
    }
}
