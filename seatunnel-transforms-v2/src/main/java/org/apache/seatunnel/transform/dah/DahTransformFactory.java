package org.apache.seatunnel.transform.dah;

import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

import com.google.auto.service.AutoService;

/**
 * 资产中心脱敏
 *
 * <AUTHOR>
 * @date 2024/9/20
 */
@AutoService(Factory.class)
public class DahTransformFactory implements TableTransformFactory {

    @Override
    public String factoryIdentifier() {
        return "Dah";
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder()
                .required(DahTransformConfig.DAH_IDENTIFIER, DahTransformConfig.DAH_COLUMN)
                .optional(DahTransformConfig.DAH_SALT)
                .optional(DahTransformConfig.DAH_ALGOITHM_REGEX)
                .optional(DahTransformConfig.DAH_ALGOITHM_PARAMETER)
                .build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        CatalogTable catalogTable = context.getCatalogTables().get(0);
        return () -> new DahTransform(context.getOptions(), catalogTable);
    }
}
