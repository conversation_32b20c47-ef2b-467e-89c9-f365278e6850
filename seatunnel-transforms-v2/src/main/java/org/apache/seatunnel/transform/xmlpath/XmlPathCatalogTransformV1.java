package org.apache.seatunnel.transform.xmlpath;

import org.apache.seatunnel.shade.com.google.common.collect.Lists;

import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.Column;
import org.apache.seatunnel.api.table.catalog.ConstraintKey;
import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
import org.apache.seatunnel.api.table.catalog.PrimaryKey;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.api.table.type.SqlType;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportFlatMapTransform;
import org.apache.seatunnel.transform.exception.TransformCommonError;
import org.apache.seatunnel.transform.xpath.XmlColumnConfig;
import org.apache.seatunnel.transform.xpath.util.XmlParseUtils;
import org.apache.seatunnel.transform.xpath.util.XmlPathConstants;

import org.apache.commons.lang3.StringUtils;

import org.dom4j.io.SAXReader;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
public class XmlPathCatalogTransformV1 extends AbstractCatalogSupportFlatMapTransform {

    private XmlPathTransformConfigV1 config;

    private final SeaTunnelRowType seaTunnelRowType;

    private int[] srcFieldIndexArr;

    private Column[] outputColumns;

    private CatalogTable inputCatalogTable;

    public XmlPathCatalogTransformV1(
            @NonNull XmlPathTransformConfigV1 config, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = config;
        this.inputCatalogTable = inputCatalogTable;
        this.seaTunnelRowType = inputCatalogTable.getTableSchema().toPhysicalRowDataType();
        init();
    }

    private void init() {
        initSrcFieldIndexArr();
        initOutputSeaTunnelRowType();
    }

    private void initOutputSeaTunnelRowType() {
        this.outputColumns =
                this.config.getColumnConfigs().stream()
                        .map(XmlColumnConfig::getDestColumn)
                        .toArray(Column[]::new);
    }

    private void initSrcFieldIndexArr() {
        List<XmlColumnConfig> columnConfigs = this.config.getColumnConfigs();
        Set<String> fieldNameSet = new HashSet<>(Arrays.asList(seaTunnelRowType.getFieldNames()));
        this.srcFieldIndexArr = new int[columnConfigs.size()];

        for (int i = 0; i < columnConfigs.size(); i++) {
            XmlColumnConfig columnConfig = columnConfigs.get(i);
            String srcField = columnConfig.getSrcField();
            if (!fieldNameSet.contains(srcField)) {
                throw TransformCommonError.cannotFindInputFieldError(getPluginName(), srcField);
            }
            this.srcFieldIndexArr[i] = this.seaTunnelRowType.indexOf(srcField);
        }
    }

    @Override
    protected List<SeaTunnelRow> transformRow(SeaTunnelRow inputRow) {

        List<XmlColumnConfig> configs = this.config.getColumnConfigs();
        List<Map<String, String>> outputFields = this.config.getOutputFields();

        Map<String, String> dataSplit = new HashMap<>();
        Map<String, Integer> dataIndex = new HashMap<>();
        Map<Integer, List<String>> sortedMap =
                new TreeMap<>(
                        new Comparator<Integer>() {
                            @Override
                            public int compare(Integer k1, Integer k2) {
                                // 若需要降序排序，则可以改为
                                return k2.compareTo(k1);
                            }
                        });
        SAXReader saxReader = new SAXReader();
        Object[] outputDataArray = new Object[configs.size() + outputFields.size()];
        int size = configs.size();
        for (int i = 0; i < size; i++) {
            int pos = this.srcFieldIndexArr[i];
            XmlColumnConfig fieldConfig = configs.get(i);
            Object fieldValue = inputRow.getField(pos);

            String xpath = fieldConfig.getXpath();
            String defaultValue = fieldConfig.getDefaultValue();
            List<String> data = new ArrayList<>();
            if (fieldValue != null) {
                // System.out.println("i = " + i + ", pos=" + pos + ", fieldValue------------" +
                // fieldValue);
                data =
                        XmlParseUtils.getElementsByXmlContextAndXPathV3(
                                saxReader, fieldValue.toString().trim(), xpath, defaultValue);
            } else {
                log.info("原始XML数据：fieldValue = [" + fieldValue + "]");
            }
            // System.out.println(xpath + "---xpath--->" + StringUtils.join(data, ","));
            // SeaTunnelDataType<?> inputDataType = seaTunnelRowType.getFieldType(pos);
            // SeaTunnelDataType<?> desDataType = fieldConfig.getDestColumn().getDataType();

            String lineSplit = getRealLineSplint();
            String value = StringUtils.join(data, lineSplit);
            outputDataArray[i] = value;

            bySizePutSortedMap(sortedMap, data, fieldConfig);
            dataSplit.put(fieldConfig.getDestField(), value);
            dataIndex.put(fieldConfig.getDestField(), i);
        }

        int index = size;
        for (Map<String, String> map : outputFields) {
            String name = map.get("fieldName");
            String fieldType = map.get("fieldType");
            String dataFormat = map.get("dataFormat");
            int pos = this.seaTunnelRowType.indexOf(name);
            Object value = inputRow.getField(pos);
            // SeaTunnelDataType<?> srcFieldDataType =
            // SeaTunnelDataTypeConvertorUtil.deserializeSeaTunnelDataType(name, fieldType);
            if (null == value) {
                outputDataArray[index] = value;
            } else {
                outputDataArray[index] = value;
            }
            index++;
        }

        List<SeaTunnelRow> list = new ArrayList<>();

        if (sortedMap.size() > 0) {
            Integer key = sortedMap.entrySet().stream().findFirst().get().getKey();
            List<String> destFieldList = sortedMap.entrySet().stream().findFirst().get().getValue();
            List<Object[]> listObj = new ArrayList<>();
            for (int i = 0; i < key; i++) { // 初始化每行数据
                listObj.add(outputDataArray);
            }
            for (int n = 0; n < key; n++) { // 遍历每一行数据
                Object[] obj = listObj.get(n);
                for (String destField : destFieldList) {
                    String value = dataSplit.get(destField);
                    String lineSplit = getRealLineSplint();
                    String[] split = value.split(lineSplit, -1);
                    Integer i = dataIndex.get(destField);
                    if (split.length - 1 < n) {
                        log.error(
                                "字段："
                                        + destField
                                        + " , 数据："
                                        + value
                                        + ", 按照【"
                                        + this.config.getLineSplit()
                                        + "】进行分割, n="
                                        + n
                                        + ",建议设置默认值！");
                    }
                    String data = split[n];
                    XmlColumnConfig fieldConfig = configs.get(i);
                    // 进行数据转换
                    obj[i] =
                            doTransform(
                                    fieldConfig.getDestColumn().getDataType(),
                                    data,
                                    fieldConfig.getDataFormat());
                }
                Object[] objectRow = obj.clone();
                SeaTunnelRow outputRow = new SeaTunnelRow(objectRow);
                outputRow.setRowKind(inputRow.getRowKind());
                outputRow.setTableId(inputRow.getTableId());
                list.add(outputRow);
            }
        }
        return list;
    }

    private String getRealLineSplint() {
        String lineSplit = this.config.getLineSplit();
        if (lineSplit.contains("$") || lineSplit.indexOf("$") > 0) {
            lineSplit = lineSplit.replace("$", "@");
        }
        return lineSplit;
    }

    private void bySizePutSortedMap(
            Map<Integer, List<String>> sortedMap, List<String> data, XmlColumnConfig fieldConfig) {
        String destField = fieldConfig.getDestField();
        Integer size = data.size();
        if (sortedMap.get(size) == null) {
            List<String> list = Lists.newArrayList(destField);
            sortedMap.put(size, list);
        } else {
            List<String> list = sortedMap.get(size);
            list.add(destField);
            sortedMap.put(size, list);
        }
    }

    private Object doTransform(SeaTunnelDataType<?> desDataType, String value, String dataFormat) {
        SqlType sqlType = desDataType.getSqlType();
        if (sqlType.equals(SqlType.STRING)) {
            return value;
        } else if (sqlType.equals(SqlType.DOUBLE)) {
            if ("".equals(value)) {
                return 0;
            }
            return Double.valueOf(value);
        } else if (sqlType.equals(SqlType.INT)) {
            if ("".equals(value)) {
                return 0;
            }
            return Integer.valueOf(value);
        } else if (sqlType.equals(SqlType.FLOAT)) {
            if ("".equals(value)) {
                return 0;
            }
            return Float.valueOf(value);
        } else if (sqlType.equals(SqlType.DATE)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getDateFormat();
            }
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(dataFormat);
            LocalDate data = DateUtils.parse(value, dateTimeFormatter);
            return data;
        } else if (sqlType.equals(SqlType.TIME)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getTimeFormat();
            }
            TimeUtils.Formatter formatter = TimeUtils.Formatter.parse(dataFormat);
            LocalTime data = TimeUtils.parse(value, formatter);
            return data;
        } else if (sqlType.equals(SqlType.TIMESTAMP)) {
            if (StringUtils.isEmpty(dataFormat)) {
                dataFormat = config.getDatetimeFormat();
            }
            DateTimeUtils.Formatter formatter = DateTimeUtils.Formatter.parse(dataFormat);
            LocalDateTime data = DateTimeUtils.parse(value, formatter);
            return data;
        }
        return value;
    }

    @Override
    protected TableSchema transformTableSchema() {

        List<ConstraintKey> outputConstraintKeys =
                inputCatalogTable.getTableSchema().getConstraintKeys().stream()
                        .map(ConstraintKey::copy)
                        .collect(Collectors.toList());

        PrimaryKey copiedPrimaryKey =
                inputCatalogTable.getTableSchema().getPrimaryKey() == null
                        ? null
                        : inputCatalogTable.getTableSchema().getPrimaryKey().copy();

        TableSchema tableSchema = inputCatalogTable.getTableSchema();

        List<Column> transformColumns = new ArrayList<>();
        List<XmlColumnConfig> columnConfigs = this.config.getColumnConfigs();

        for (XmlColumnConfig config : columnConfigs) {
            String name = config.getDestField();
            SeaTunnelDataType<?> dataType = config.getDestColumn().getDataType();
            transformColumns.add(PhysicalColumn.of(name, dataType, 0, true, null, null));
        }

        // 这里从配置中获取
        List<Map<String, String>> outputFields = this.config.getOutputFields();

        for (Map<String, String> map : outputFields) {
            String fieldName = map.get("fieldName");
            // 这里从source表获取schema信息
            for (Column column : tableSchema.getColumns()) {
                SeaTunnelDataType<?> dataType = column.getDataType();
                String name = column.getName();
                if (fieldName.equalsIgnoreCase(name)) {
                    transformColumns.add(PhysicalColumn.of(name, dataType, 0, true, null, null));
                }
            }
        }

        return TableSchema.builder()
                .primaryKey(copiedPrimaryKey)
                .columns(transformColumns)
                .constraintKey(outputConstraintKeys)
                .build();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return XmlPathConstants.PLUGIN_NAME;
    }
}
