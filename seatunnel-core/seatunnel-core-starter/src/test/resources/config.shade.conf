#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  shade.identifier = "base64"
}

source {
  MySQL-CDC {
    schema {
      fields {
        name = string
        age = int
        sex = boolean
      }
    }
    result_table_name = "fake"
    parallelism = 1
    server-id = 5656
    port = 56725
    hostname = "127.0.0.1"
    username = "c2VhdHVubmVs"
    password = "c2VhdHVubmVsX3Bhc3N3b3Jk"
    database-name = "inventory_vwyw0n"
    table-name = "products"
    base-url = "****************************"
  }
}

transform {
}

sink {
  # choose stdout output plugin to output data to console
  Clickhouse {
    host = "localhost:8123"
    database = "default"
    table = "fake_all"
    username = "c2VhdHVubmVs"
    password = "c2VhdHVubmVsX3Bhc3N3b3Jk"

    # cdc options
    primary_key = "id"
    support_upsert = true
  }
}
