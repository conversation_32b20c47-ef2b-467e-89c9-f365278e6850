# Introduction

This module is the seatunnel job entrypoint. SeaTunnel jobs are started by the below process.
![seatunnel-workflow.svg](../docs/en/images/seatunnel_starter.png)

- seatunnel-core-flink: The flink job starter.
- seatunnel-core-flink-sql: The flink sql job starter.
- seatunnel-core-spark: The spark job starter.
- seatunnel-spark-starter: The spark job starter for connector-v2.
- seatunnel-flink-starter: The flink job starter for connector-v2.
- seatunnel-starter: The seatunnel engine job starter for connector-v2.
