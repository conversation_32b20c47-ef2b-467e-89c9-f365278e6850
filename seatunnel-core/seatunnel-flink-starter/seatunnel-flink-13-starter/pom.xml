<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-flink-starter</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-flink-13-starter</artifactId>
    <name>SeaTunnel : Core : Flink Starter : 1.3</name>

    <dependencies>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-flink-starter-common</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- flink-translation -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-translation-flink-13</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- flink 1.13.6 java api -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-java</artifactId>
            <version>${flink.1.13.6.version}</version>
            <scope>${flink.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${flink.1.13.6.version}</version>
            <scope>${flink.scope}</scope>
        </dependency>

        <!-- flink planner api -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
            <version>${flink.1.13.6.version}</version>
            <scope>${flink.scope}</scope>
        </dependency>

        <!-- flink state backend rocksdb api -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-statebackend-rocksdb_${scala.binary.version}</artifactId>
            <version>${flink.1.13.6.version}</version>
            <scope>${flink.scope}</scope>
        </dependency>

    </dependencies>

</project>
