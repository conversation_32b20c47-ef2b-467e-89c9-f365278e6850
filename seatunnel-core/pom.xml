<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-core</artifactId>
    <packaging>pom</packaging>
    <name>SeaTunnel : Core :</name>

    <modules>
        <module>seatunnel-core-starter</module>
        <module>seatunnel-flink-starter</module>
        <module>seatunnel-spark-starter</module>
        <module>seatunnel-starter</module>
    </modules>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>copy-starter-logging-package-for-e2e</id>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <phase>package</phase>
                            <configuration>
                                <excludeTransitive>false</excludeTransitive>
                                <includeGroupIds>org.slf4j,org.apache.logging.log4j</includeGroupIds>
                                <includeArtifactIds>slf4j-api,jcl-over-slf4j,log4j-slf4j-impl,log4j-api,log4j-core</includeArtifactIds>
                                <outputDirectory>${project.build.directory}/logging-e2e</outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
