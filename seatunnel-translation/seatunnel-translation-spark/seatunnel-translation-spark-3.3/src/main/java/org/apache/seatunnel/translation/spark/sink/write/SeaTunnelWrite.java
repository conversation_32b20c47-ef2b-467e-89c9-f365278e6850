/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.translation.spark.sink.write;

import org.apache.seatunnel.api.sink.SeaTunnelSink;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.translation.spark.sink.SeaTunnelBatchWrite;

import org.apache.spark.sql.connector.write.BatchWrite;
import org.apache.spark.sql.connector.write.Write;
import org.apache.spark.sql.connector.write.streaming.StreamingWrite;

import java.io.IOException;

public class SeaTunnelWrite<AggregatedCommitInfoT, CommitInfoT, StateT> implements Write {

    private final SeaTunnelSink<SeaTunnelRow, StateT, CommitInfoT, AggregatedCommitInfoT> sink;
    private final CatalogTable catalogTable;

    public SeaTunnelWrite(
            SeaTunnelSink<SeaTunnelRow, StateT, CommitInfoT, AggregatedCommitInfoT> sink,
            CatalogTable catalogTable) {
        this.sink = sink;
        this.catalogTable = catalogTable;
    }

    @Override
    public BatchWrite toBatch() {
        try {
            return new SeaTunnelBatchWrite<>(sink, catalogTable);
        } catch (IOException e) {
            throw new RuntimeException("SeaTunnel Spark sink create batch failed", e);
        }
    }

    @Override
    public StreamingWrite toStreaming() {
        try {
            return new SeaTunnelBatchWrite<>(sink, catalogTable);
        } catch (IOException e) {
            throw new RuntimeException("SeaTunnel Spark sink create batch failed", e);
        }
    }
}
