/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.translation.flink.sink;

import java.io.Serializable;

/**
 * The writer state wrapper of {@link StateT}, used to unify the different implementations of {@link
 * StateT}
 *
 * @param <StateT> The generic type of the writer state
 */
public class FlinkWriterState<StateT> implements Serializable {

    private long checkpointId = 0;

    private StateT state;

    public FlinkWriterState(long checkpointId, StateT state) {
        this.checkpointId = checkpointId;
        this.state = state;
    }

    public long getCheckpointId() {
        return checkpointId;
    }

    public void setCheckpointId(long checkpointId) {
        this.checkpointId = checkpointId;
    }

    public StateT getState() {
        return state;
    }

    public void setState(StateT state) {
        this.state = state;
    }
}
