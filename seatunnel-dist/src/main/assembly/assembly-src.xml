<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>src</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <baseDirectory>${project.build.finalName}-src</baseDirectory>

    <fileSets>
        <fileSet>
            <directory>../</directory>
            <useDefaultExcludes>true</useDefaultExcludes>
            <includes>
                <include>**/*</include>
            </includes>
            <excludes>
                <!-- github ignore -->
                <exclude>**/.github/**</exclude>

                <!-- maven ignore -->
                <exclude>**/target/**</exclude>
                <exclude>**/*.class</exclude>
                <exclude>**/*.jar</exclude>
                <exclude>**/*.war</exclude>
                <exclude>**/*.zip</exclude>
                <exclude>**/*.tar</exclude>
                <exclude>**/*.tar.gz</exclude>

                <!-- maven plugin ignore -->
                <exclude>release.properties</exclude>
                <exclude>**/pom.xml.releaseBackup</exclude>
                <exclude>*.gpg</exclude>
                <!--github ignore-->
                <exclude>**/.github/**</exclude>
                <exclude>**/.dlc.json</exclude>
                <!-- eclipse ignore -->
                <exclude>**/.settings/**</exclude>
                <exclude>**/.project</exclude>
                <exclude>**/.classpath</exclude>

                <!-- idea ignore -->
                <exclude>**/.idea/**</exclude>
                <exclude>**/*.ipr</exclude>
                <exclude>**/*.iml</exclude>
                <exclude>**/*.iws</exclude>

                <!-- temp ignore -->
                <exclude>**/logs/**</exclude>
                <exclude>**/*.log</exclude>
                <exclude>**/*.doc</exclude>
                <exclude>**/*.cache</exclude>
                <exclude>**/*.diff</exclude>
                <exclude>**/*.patch</exclude>
                <exclude>**/*.tmp</exclude>
                <exclude>**/all-dependencies.txt</exclude>
                <exclude>**/self-modules.txt</exclude>
                <exclude>**/third-party-dependencies.txt</exclude>

                <!-- system ignore -->
                <exclude>**/.DS_Store</exclude>
                <exclude>**/Thumbs.db</exclude>
            </excludes>
        </fileSet>
    </fileSets>
</assembly>
