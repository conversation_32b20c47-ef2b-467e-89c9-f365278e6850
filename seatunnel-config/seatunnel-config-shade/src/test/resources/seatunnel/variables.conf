# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

spark {
  spark.stream.batchDuration = 5

  spark.app.name = "SeaTunnel"
  spark.executor.instances = 2
  spark.executor.cores = 1
  spark.executor.memory = "1g"
}

source {
  fakestream {
    content = [
      "20190318, beijing, first message",
      "20190319, shanghai, second message",
      "20190318, shanghai, third message"
    ]
    rate = 1
  }
}

transform {
  split {
    fields = ["dt", "city", "msg"]
    delimiter = ","
  }

  sql {
    table_name = "user_view"
    sql = "select * from user_view where city = '"${city2}"'"
    result_table_name = "result1"
  }

  sql {
    table_name = "user_view"
    sql = "select * from user_view where dt = '"${dt}"'"
    result_table_name = "result2"
  }
}

sink {
  stdout {
    source_table_name="result1"
  }

  stdout {
  }
}
