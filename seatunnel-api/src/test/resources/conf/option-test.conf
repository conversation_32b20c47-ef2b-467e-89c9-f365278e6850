#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
    parallelism = 1
    job.mode = "STREAMING"
    checkpoint.interval = 5000
}

source {
    FakeSource {
        option {
            bool = true
            bool-str = "false"
            int = **********
            int-str = "100"
            float = 3.3333
            float-str = "3.1415"
            double = 3.1415926535897932384626433832795028841971
            double-str = "3.1415926535897932384626433832795028841971"
            map {
                inner {
                    path = "mac"
                    name = "ashulin"
                    # The nested Map(Map<Map<?,?>>) type supports only JSON
                    map = """{"fantasy":"final"}"""
                }
                type = "source"
                patch.note = "hollow"
                name = "saitou"
            }
        }
        option.long = **********0
        option.long-str = "**********0"
        option.string = "Hello, Apache SeaTunnel"
        option.enum = "LATEST"
        option.numeric-list = [
            1,
            2
        ]
        option.enum-list = [
            "EARLIEST",
            "LATEST"
        ]
        option.list-json = """["Hello", "Apache SeaTunnel"]"""
        option.list = ["final", "fantasy", "VII"]
        option.list-str = "Silk,Song"
        option.complex-type = [{
            inner {
                list = [{
                    inner {
                        path = "mac"
                        name = "ashulin"
                        map = """{"fantasy":"final"}"""
                    }
                    type = "source"
                    patch.note = "hollow"
                    name = "saitou"
                },
                {
                    inner {
                        path = "mac"
                        name = "ashulin"
                        map = """{"fantasy":"final"}"""
                    }
                    type = "source"
                    patch.note = "hollow"
                    name = "saitou"
                }]
                list-2 = [{
                inner {
                    path = "mac"
                    name = "ashulin"
                    map = """{"fantasy":"final"}"""
                }
                type = "source"
                patch.note = "hollow"
                name = "saitou"
                }]
            }
        }]
    }
}

transform {
    sql {
        sql = "select name,age from fake"
    }
}

sink {
    File {
        path = "file:///tmp/hive/warehouse/test2"
        field_delimiter = "\t"
        row_delimiter = "\n"
        partition_by = ["age"]
        partition_dir_expression = "${k0}=${v0}"
        is_partition_field_write_in_file = true
        file_name_expression = "${transactionId}_${now}"
        file_format_type = "text"
        sink_columns = ["name","age"]
        extendsSQL = """insert into sink (c_bit_1, c_bit_8, c_bit_16, c_bit_32, c_bit_64, c_boolean, c_tinyint, c_tinyint_unsigned, c_smallint, c_smallint_unsigned,
                                                c_mediumint, c_mediumint_unsigned, c_int, c_integer, c_bigint, c_bigint_unsigned,
                                                c_decimal, c_decimal_unsigned, c_float, c_float_unsigned, c_double, c_double_unsigned,
                                                c_char, c_tinytext, c_mediumtext, c_text, c_varchar, c_json, c_longtext, c_date,
                                                c_datetime, c_timestamp, c_tinyblob, c_mediumblob, c_blob, c_longblob, c_varbinary,
                                                c_binary, c_year, c_int_unsigned, c_integer_unsigned,c_bigint_30,c_decimal_unsigned_30,c_decimal_30)
                   values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);"""
    }
}