#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

schema = {
   columns = [
       {
          name = id
          type = bigint
          nullable = false
          defaultValue = 0
          comment = "primary key id"
       },
       {
          name = map
          type = "map<string, map<string, string>>"
          nullable = true
          comment = "map value"
       },
       {
          name = map_array
          type = "map<string, map<string, array<int>>>"
          nullable = true
          comment = "map_array value"
       },
       {
         name = array
         type = "array<tinyint>"
         nullable = true
         comment = "array value"
       },
       {
        name = string
        type = "string"
        nullable = true
        defaultValue = "I'm default value"
        comment = "string value"
      },
      {
        name = boolean
        type = "boolean"
        nullable = true
        defaultValue = false
        comment = "boolean value"
      },
      {
        name = tinyint
        type = "tinyint"
        nullable = true
        comment = "tinyint value"
      },
      {
        name = smallint
        type = "smallint"
        nullable = true
        comment = "smallint value"
      },
      {
        name = int
        type = "int"
        nullable = true
        comment = "int value"
      },
      {
        name = bigint
        type = "bigint"
        nullable = true
        comment = "bigint value"
      },
      {
        name = float
        type = "float"
        nullable = true
        defaultValue = 1.1
        comment = "float value"
      },
      {
        name = double
        type = "double"
        nullable = true
        comment = "double value"
      },
      {
        name = decimal
        type = "decimal(30, 8)"
        nullable = true
        comment = "decimal value"
      },
      {
        name = "null"
        type = "null"
        nullable = true
        comment = "null value"
      },
      {
        name = bytes
        type = "bytes"
        nullable = true
        comment = "bytes value"
      },
      {
        name = date
        type = "date"
        nullable = true
        defaultValue = "2020-01-01"
        comment = "date value"
      },
      {
        name = time
        type = "time"
        nullable = true
        comment = "time value"
      },
      {
        name = timestamp
        type = "timestamp"
        nullable = true
        comment = "timestamp value"
      },
      {
        name = row
        type = {
         map = "map<string, map<string, string>>"
         map_array = "map<string, map<string, array<int>>>"
         array = "array<tinyint>"
         string = string
         boolean = boolean
         tinyint = tinyint
         smallint = smallint
         int = int
         bigint = bigint
         float = float
         double = double
         decimal = "decimal(30, 8)"
         null = "null"
         bytes = bytes
         date = date
         time = time
         timestamp = timestamp
         row = {
           map = "map<string, map<string, string>>"
           map_array = "map<string, map<string, array<int>>>"
           array = "array<tinyint>"
           string = string
           boolean = boolean
           tinyint = tinyint
           smallint = smallint
           int = int
           bigint = bigint
           float = float
           double = double
           decimal = "decimal(30, 8)"
           null = "null"
           bytes = bytes
           date = date
           time = time
           timestamp = timestamp
           }
       }
        nullable = true
        comment = "row value"
      }
   ]
   primaryKey {
      name = "id"
      columnNames = [id]
   }
   constraintKeys = [
      {
         constraintName = "id_index"
         constraintType = INDEX_KEY
         constraintColumns = [
            {
                columnName = "id"
                sortType = ASC
            }
         ]
      },
   ]
}