/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.common.metrics;

public final class MetricTags {

    private MetricTags() {}

    public static final String MEMBER = "member";

    public static final String ADDRESS = "address";

    public static final String JOB_ID = "jobId";

    public static final String PIPELINE_ID = "pipelineId";

    public static final String TASK_GROUP_ID = "taskGroupId";

    public static final String TASK_ID = "taskID";

    public static final String UNIT = "unit";

    public static final String TASK_NAME = "taskName";

    public static final String SERVICE = "service";

    public static final String TASK_GROUP_LOCATION = "taskGroupLocation";
}
