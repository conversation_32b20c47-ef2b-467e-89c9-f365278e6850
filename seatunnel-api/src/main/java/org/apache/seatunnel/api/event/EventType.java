/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.event;

public enum EventType {
    SCHEMA_CHANGE_ADD_COLUMN,
    SCHEMA_CHANGE_DROP_COLUMN,
    SCHEMA_CHANGE_MODIFY_COLUMN,
    SCHEMA_CHANGE_CHANGE_COLUMN,
    SCHEMA_CHANGE_UPDATE_COLUMNS,
    SCHEMA_CHANGE_RENAME_TABLE,
    LIFECYCLE_ENUMERATOR_OPEN,
    LIFECYCLE_ENUMERATOR_CLOSE,
    LIFECYCLE_READER_OPEN,
    LIFECYCLE_READER_CLOSE,
    LIFECYCLE_WRITER_CLOSE,
    READER_MESSAGE_DELAYED,
}
