/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.table.converter;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.io.Serializable;

@Data
@Builder
public class BasicTypeDefine<T> implements Serializable {
    protected String name;
    // e.g. `varchar(10)` for MySQL
    protected String columnType;
    // e.g. `varchar` for MySQL
    protected String dataType;
    protected T nativeType;
    // e.g. `varchar` length is 10
    protected Long length;
    // e.g. `decimal(10, 2)` precision is 10
    protected Long precision;
    // e.g. `decimal(10, 2)` scale is 2 or timestamp(6) scale is 6
    protected Integer scale;
    // e.g. `tinyint unsigned` is true
    protected boolean unsigned;
    @Builder.Default protected boolean nullable = true;
    protected Object defaultValue;
    protected String comment;

    @Tolerate
    public BasicTypeDefine() {}
}
