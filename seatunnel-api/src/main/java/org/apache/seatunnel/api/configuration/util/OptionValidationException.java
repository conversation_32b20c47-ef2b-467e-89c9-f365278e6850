/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.configuration.util;

import org.apache.seatunnel.api.common.SeaTunnelAPIErrorCode;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.common.exception.SeaTunnelRuntimeException;

/** Exception for all errors occurring during option validation phase. */
public class OptionValidationException extends SeaTunnelRuntimeException {

    public OptionValidationException(String message, Throwable cause) {
        super(SeaTunnelAPIErrorCode.OPTION_VALIDATION_FAILED, message, cause);
    }

    public OptionValidationException(String message) {
        super(SeaTunnelAPIErrorCode.OPTION_VALIDATION_FAILED, message);
    }

    public OptionValidationException(String formatMessage, Object... args) {
        super(SeaTunnelAPIErrorCode.OPTION_VALIDATION_FAILED, String.format(formatMessage, args));
    }

    public OptionValidationException(Option<?> option) {
        super(
                SeaTunnelAPIErrorCode.OPTION_VALIDATION_FAILED,
                String.format(
                        "The option(\"%s\")  is incorrectly configured, please refer to the doc: %s",
                        option.key(), option.getDescription()));
    }
}
