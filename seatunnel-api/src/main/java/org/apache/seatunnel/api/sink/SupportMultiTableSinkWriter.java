/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.sink;

import java.util.Optional;

/** The Sink Connector Writer which support multi table should implement this interface */
public interface SupportMultiTableSinkWriter<T> extends SupportResourceShare<T> {

    /**
     * The primary key index of the table in SeaTunnelRow, use it to make sure the same key value
     * will be written to the same sink writer
     */
    default Optional<Integer> primaryKey() {
        return Optional.empty();
    }
}
