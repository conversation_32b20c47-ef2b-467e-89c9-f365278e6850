/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.table.catalog;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Getter
@EqualsAndHashCode
@RequiredArgsConstructor
public final class TablePath implements Serializable {
    private static final long serialVersionUID = 1L;
    private final String databaseName;
    private final String schemaName;
    private final String tableName;

    public static final TablePath DEFAULT = TablePath.of("default", "default", "default");

    public static TablePath of(String fullName) {
        return of(fullName, false);
    }

    public static TablePath of(String fullName, boolean schemaFirst) {
        String[] paths = fullName.split("\\.");

        if (paths.length == 1) {
            return of(null, paths[0]);
        }
        if (paths.length == 2) {
            if (schemaFirst) {
                return of(null, paths[0], paths[1]);
            }
            return of(paths[0], null, paths[1]);
        }
        if (paths.length == 3) {
            return of(paths[0], paths[1], paths[2]);
        }
        // 2025-02-14  oracle 使用servername连接，servername使用的 als_stgxxx.hk.ha.dev 格式
        if (paths.length > 3) {
            // 处理更长的路径（例如 als_stgxxx.hk.ha.dev.C##ZSP.A_2025_02_14）
            // 假设最后一部分是表名，倒数第二部分是 schema，其余部分是数据库名
            int length = paths.length;
            String tableName = paths[length - 1]; // 表名是最后一部分
            String schemaName = paths[length - 2]; // schema 是倒数第二部分
            String databaseName =
                    String.join(".", Arrays.copyOfRange(paths, 0, length - 2)); // 数据库名是剩余部分
            TablePath tablePath = of(databaseName, schemaName, tableName);
            log.info("使用非典型的dbname格式fullName:{},解析后的TablePath:{}", fullName, tablePath);
            return tablePath;
        }
        throw new IllegalArgumentException(
                String.format("Cannot get split '%s' to get databaseName and tableName", fullName));
    }

    public static TablePath of(String databaseName, String tableName) {
        return of(databaseName, null, tableName);
    }

    public static TablePath of(String databaseName, String schemaName, String tableName) {
        return new TablePath(databaseName, schemaName, tableName);
    }

    public String getSchemaAndTableName() {
        return getNameCommon(null, schemaName, tableName, null, null);
    }

    public String getSchemaAndTableName(String quote) {
        return getNameCommon(null, schemaName, tableName, quote, quote);
    }

    public String getFullName() {
        return getNameCommon(databaseName, schemaName, tableName, null, null);
    }

    public String getFullNameWithQuoted() {
        return getFullNameWithQuoted("`");
    }

    public String getFullNameWithQuoted(String quote) {
        return getNameCommon(databaseName, schemaName, tableName, quote, quote);
    }

    public String getFullNameWithQuoted(String quoteLeft, String quoteRight) {
        return getNameCommon(databaseName, schemaName, tableName, quoteLeft, quoteRight);
    }

    private String getNameCommon(
            String databaseName,
            String schemaName,
            String tableName,
            String quoteLeft,
            String quoteRight) {
        List<String> joinList = new ArrayList<>();
        quoteLeft = quoteLeft == null ? "" : quoteLeft;
        quoteRight = quoteRight == null ? "" : quoteRight;

        if (databaseName != null) {
            joinList.add(quoteLeft + databaseName + quoteRight);
        }

        if (schemaName != null) {
            joinList.add(quoteLeft + schemaName + quoteRight);
        }

        if (tableName != null) {
            joinList.add(quoteLeft + tableName + quoteRight);
        }

        return String.join(".", joinList);
    }

    @Override
    public String toString() {
        return getFullName();
    }
}
