/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.sink;

import java.util.Optional;

/** The Sink Connectors which support schema and data SaveMode should implement this interface */
public interface SupportSaveMode {

    String DATA_SAVE_MODE_KEY = "data_save_mode";

    String SCHEMA_SAVE_MODE_KEY = "schema_save_mode";

    // This method defines the return of a specific save_mode handler
    <PERSON><PERSON><SaveModeHandler> getSaveModeHandler();
}
