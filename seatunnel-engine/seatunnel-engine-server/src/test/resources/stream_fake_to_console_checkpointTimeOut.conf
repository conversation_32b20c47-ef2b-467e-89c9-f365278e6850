#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 1000
  checkpoint.timeout = 100
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
    FakeSource {
      result_table_name = "fake1"
       row.num = 1000
       split.num = 100
       split.read-interval = 3000
       parallelism = 1
      schema = {
        fields {
          name = "string"
          age = "int"
        }
      }
      parallelism = 1
    }
}

transform {
}

sink {
  console {
  log.print.delay.ms=5000
  }
}