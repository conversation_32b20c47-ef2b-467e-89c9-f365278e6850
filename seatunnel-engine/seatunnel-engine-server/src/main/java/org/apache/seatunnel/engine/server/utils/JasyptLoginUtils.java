package org.apache.seatunnel.engine.server.utils;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.iv.RandomIvGenerator;
import org.jasypt.salt.RandomSaltGenerator;

/** <AUTHOR> @Date 2025/7/14 18:09 */
public class JasyptLoginUtils {
    private static final String JASYPT_PASSWORD = "NkVCQUQxMjBFQTI4QjY5NzVFQkYxRUNBRjEzMjc1Nzc=";
    private static final String JASYPT_ALGORITHM = "PBEWITHHMACSHA512ANDAES_256";
    public static final String JASYPT_RPD = "Cdyanfa_123456";
    // cMU+KY4HfzCJHRKCopKBFXppHTBtBDjNwC+bcOAlURV0Tqt2jLzzjrS4r8dok78w

    public static String decryptToken(String encryptedToken) {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(JASYPT_PASSWORD);
        encryptor.setAlgorithm(JASYPT_ALGORITHM);
        encryptor.setSaltGenerator(new RandomSaltGenerator());
        encryptor.setIvGenerator(new RandomIvGenerator());
        return encryptor.decrypt(encryptedToken);
    }

    public static boolean checkAuth(String uri) {
        String encryptedToken = getTokenFromUri(uri);
        if (encryptedToken == null) {
            return false;
        }
        try {
            String decrypted = JasyptLoginUtils.decryptToken(encryptedToken);
            return JASYPT_RPD.equals(decrypted);
        } catch (Exception e) {
            e.printStackTrace();
            // 解密失败
            return false;
        }
    }

    public static String getTokenFromUri(String uri) {
        if (uri == null) {
            return null;
        }
        int questionMarkIndex = uri.indexOf('?');
        if (questionMarkIndex == -1) {
            return null;
        }
        String queryString = uri.substring(questionMarkIndex + 1);
        String[] params = queryString.split("&");
        for (String param : params) {
            String[] keyValue = param.split("=", 2);
            if (keyValue.length == 2 && "token".equals(keyValue[0])) {
                return keyValue[1];
            }
        }
        return null;
    }
}
