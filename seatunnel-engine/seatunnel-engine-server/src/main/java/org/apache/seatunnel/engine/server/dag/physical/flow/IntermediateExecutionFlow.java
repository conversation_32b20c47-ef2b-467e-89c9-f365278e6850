/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.engine.server.dag.physical.flow;

import org.apache.seatunnel.engine.core.dag.internal.IntermediateQueue;
import org.apache.seatunnel.engine.server.dag.physical.config.FlowConfig;

import java.util.ArrayList;
import java.util.List;

public class IntermediateExecutionFlow<F extends FlowConfig> extends Flow {

    private final IntermediateQueue queue;

    private F config;

    public IntermediateExecutionFlow(IntermediateQueue queue) {
        super(new ArrayList<>());
        this.queue = queue;
    }

    public IntermediateExecutionFlow(IntermediateQueue queue, List<Flow> next) {
        super(next);
        this.queue = queue;
    }

    public F getConfig() {
        return config;
    }

    public void setConfig(F config) {
        this.config = config;
    }

    public IntermediateQueue getQueue() {
        return queue;
    }

    @Override
    public long getFlowID() {
        return queue.getId();
    }
}
