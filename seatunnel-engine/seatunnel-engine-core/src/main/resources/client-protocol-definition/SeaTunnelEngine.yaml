#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# The schema of this file can find from this link: https://github.com/hazelcast/hazelcast-client-protocol
id: 222
name: SeaTunnel
methods:
  - id: 1
    name: printMessage
    since: 2.0
    doc: ''
    request:
      retryable: false
      partitionIdentifier: -1
      params:
        - name: message
          type: String
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''

  - id: 2
    name: submitJob
    since: 2.0
    doc: ''
    request:
      retryable: false
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
        - name: jobImmutableInformation
          type: Data
          nullable: false
          since: 2.0
          doc: ''
    response: {}

  - id: 3
    name: waitForJobComplete
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: jobResult
          type: Data
          nullable: false
          since: 2.0
          doc: ''

  - id: 4
    name: cancelJob
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response: {}

  - id: 5
    name: getJobStatus
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: jobStatus
          type: int
          nullable: false
          since: 2.0
          doc: ''

  - id: 6
    name: getJobDetailStatus
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''

  - id: 7
    name: listJobStatus
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params: []
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''

  - id: 8
    name: getJobMetrics
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''
  - id: 9
    name: getJobInfo
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: response
          type: Data
          nullable: false
          since: 2.0
          doc: ''
  - id: 10
    name: savePointJob
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
    response: {}

  - id: 11
    name: getClusterHealthMetrics
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params: []
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''

  - id: 12
    name: getRunningJobMetrics
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params: [ ]
    response:
      params:
        - name: response
          type: String
          nullable: false
          since: 2.0
          doc: ''

  - id: 13
    name: uploadConnectorJar
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: jobId
          type: long
          nullable: false
          since: 2.0
          doc: ''
        - name: connectorJar
          type: Data
          nullable: false
          since: 2.0
          doc: ''
    response:
      params:
        - name: response
          type: Data
          nullable: false
          since: 2.0
          doc: ''

  - id: 14
    name: sendConnectorJarToMemberNode
    since: 2.0
    doc: ''
    request:
      retryable: true
      partitionIdentifier: -1
      params:
        - name: connectorJar
          type: Data
          nullable: false
          since: 2.0
          doc: ''
        - name: connectorJarIdentifier
          type: Data
          nullable: false
          since: 2.0
          doc: ''
    response: {}