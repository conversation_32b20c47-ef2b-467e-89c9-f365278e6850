package org.apache.seatunnel.engine.core.classloader;

import java.net.URL;
import java.util.Collection;

/** <AUTHOR> @Date 2025/7/31 10:07 */
public interface ClassLoaderService {
    /**
     * Get the classloader of the connector plugin.
     *
     * @param jobId the job id
     * @param jars the jars of the connector plugin
     * @return the classloader of the connector plugin
     */
    ClassLoader getClassLoader(long jobId, Collection<URL> jars);

    /**
     * Release the classloader of the connector plugin.
     *
     * @param jobId the job id
     * @param jars the jars of the connector plugin
     */
    void releaseClassLoader(long jobId, Collection<URL> jars);

    /** Close the classloader service. */
    void close();
}
