/*
 * Copyright (c) 2008-2021, Hazelcast, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.engine.common.config;

/** Configuration sections for Hazelcast SeaTunnel shared by YAML based configurations */
enum SeaTunnelConfigSections {
    SEATUNNEL("seatunnel", false),
    ENGINE("engine", false);

    final String name;
    final boolean multipleOccurrence;

    SeaTunnelConfigSections(String name, boolean multipleOccurrence) {
        this.name = name;
        this.multipleOccurrence = multipleOccurrence;
    }

    static boolean canOccurMultipleTimes(String name) {
        for (SeaTunnelConfigSections element : values()) {
            if (name.equals(element.name)) {
                return element.multipleOccurrence;
            }
        }
        return false;
    }

    boolean isEqual(String name) {
        return this.name.equals(name);
    }
}
