<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~   http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied.  See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  ~
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>imap-storage-plugins</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>imap-storage-file</artifactId>
    <name>SeaTunnel : Engine : Storage : IMap Storage Plugins : File</name>

    <properties>
        <!-- Imap storage dependency package  -->
        <hadoop-aliyun.version>3.0.0</hadoop-aliyun.version>
        <json-smart.version>2.4.7</json-smart.version>
        <hadoop-aws.version>3.1.4</hadoop-aws.version>
        <netty-buffer.version>4.1.60.Final</netty-buffer.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>serializer-protobuf</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- hadoop jar -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
            <version>${project.version}</version>
            <classifier>optional</classifier>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.lmax/disruptor -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
        </dependency>

        <!-- Imap storage dependency package  -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aliyun</artifactId>
            <version>${hadoop-aliyun.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>${json-smart.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aws</artifactId>
            <version>${hadoop-aws.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-buffer</artifactId>
            <version>${netty-buffer.version}</version>
            <scope>provided</scope>
        </dependency>

    </dependencies>

</project>
