1、仅删除
only_delete_conflicting_rows
目标端删除远程存在的数据

2、仅更新
only_update_conflicting_rows
更新目标端在源端存在的，不存在，不处理。
3、先删除后插入
delete_conflicting_before_inserting_rows
先目标端删除源端存在的，然后目标端插入源端数据
例如目标端有20个字段，此次同步至同步15个字段，其余5个字段需要为空，而不是保留原来值，所以要删除后插入
如果目标端存在数据，源端不存在，这种数据不处理

仅删除：
-----------------------------------------------
源端：1、2、3、4、5、6、7、8、9、10 、15

目标端        
1、3、5、11、12、13、14、15

---------------------------------->  delete from xx where id（1、2、3、4、5、6、7、8、9、10 ，15）
期望结果： 11、12、13、14



仅更新：
---------------------------------------
源端：
1:{name:11,age:20}
2:{name:22,age:20}
目标端
1:{name:10,age:10}
3:{name:33,age:30}
期望结果
1:{name:11age:20}
3:{name:33,age:30}

按PK/UK进行Delete&Insert：(UPSET)
---------------------------------------                
源端：                
1:{name:11,age:20}
2:{name:22,age:20}        
目标端
1:{name:10,age:10,create_time:20240508}
3:{name:33,age:30}
期望结果
1:{name:11age:20,create_time:20240509}------------------时间字段自动生成更新了
2:{name:22,age:20}
3:{name:33,age:30}

按PK/UK进行Update&Insert： (UPSET)
---------------------------------------                
源端：                
1:{name:11,age:20}
2:{name:22,age:20}        
目标端
1:{name:10,age:10,create_time:20240508}
3:{name:33,age:30}
期望结果
1:{name:11age:20,create_time:20240508}------------------时间保留目标端的值
2:{name:22,age:20}
3:{name:33,age:30}