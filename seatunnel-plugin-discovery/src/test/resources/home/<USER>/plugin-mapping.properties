#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# This mapping is used to resolve the Jar package name without version (or call artifactId)
# corresponding to the module in the user Config, helping SeaTunnel to load the correct Jar package.

# Flink Source
flink.source.DruidSource = seatunnel-connector-flink-druid
flink.source.FakeSource = seatunnel-connector-flink-fake
flink.source.FakeSourceStream = seatunnel-connector-flink-fake
flink.source.FileSource = seatunnel-connector-flink-file
flink.source.InfluxDbSource = seatunnel-connector-flink-influxdb
flink.source.JdbcSource = seatunnel-connector-flink-jdbc
flink.source.KafkaTableStream = seatunnel-connector-flink-kafka
flink.source.SocketStream = seatunnel-connector-flink-socket
flink.source.Http = seatunnel-connector-flink-http

# Flink Sink

flink.sink.Clickhouse = seatunnel-connector-flink-clickhouse
flink.sink.ClickhouseFile = seatunnel-connector-flink-clickhouse
flink.sink.ConsoleSink = seatunnel-connector-flink-console
flink.sink.DorisSink = seatunnel-connector-flink-doris
flink.sink.DruidSink = seatunnel-connector-flink-druid
flink.sink.ElasticSearch = seatunnel-connector-flink-elasticsearch7
flink.sink.FileSink = seatunnel-connector-flink-file
flink.sink.InfluxDbSink = seatunnel-connector-flink-influxdb
flink.sink.JdbcSink = seatunnel-connector-flink-jdbc
flink.sink.Kafka = seatunnel-connector-flink-kafka
flink.sink.AssertSink = seatunnel-connector-flink-assert

# Spark Source

spark.source.ElasticSearch = seatunnel-connector-spark-elasticsearch
spark.source.Fake = seatunnel-connector-spark-fake
spark.source.FakeStream = seatunnel-connector-spark-fake
spark.source.FeishuSheet = seatunnel-connector-spark-feishu
spark.source.File = seatunnel-connector-spark-file
spark.source.Hbase = seatunnel-connector-spark-hbase
spark.source.Hive = seatunnel-connector-spark-hive
spark.source.Http = seatunnel-connector-spark-http
spark.source.Hudi = seatunnel-connector-spark-hudi
spark.source.Iceberg = seatunnel-connector-spark-iceberg
spark.source.Jdbc = seatunnel-connector-spark-jdbc
spark.source.KafkaStream = seatunnel-connector-spark-kafka
spark.source.Kudu = seatunnel-connector-spark-kudu
spark.source.MongoDB = seatunnel-connector-spark-mongodb
spark.source.Neo4j = seatunnel-connector-spark-neo4j
spark.source.Phoenix = seatunnel-connector-spark-phoenix
spark.source.Redis = seatunnel-connector-spark-redis
spark.source.SocketStream = seatunnel-connector-spark-socket
spark.source.TiDB = seatunnel-connector-spark-tidb

# Spark Sink

spark.sink.Clickhouse = seatunnel-connector-spark-clickhouse
spark.sink.ClickhouseFile = seatunnel-connector-spark-clickhouse
spark.sink.Console = seatunnel-connector-spark-console
spark.sink.Doris = seatunnel-connector-spark-doris
spark.sink.ElasticSearch = seatunnel-connector-spark-elasticsearch
spark.sink.Email = seatunnel-connector-spark-email
spark.sink.File = seatunnel-connector-spark-file
spark.sink.Hbase = seatunnel-connector-spark-hbase
spark.sink.Hive = seatunnel-connector-spark-hive
spark.sink.Hudi = seatunnel-connector-spark-hudi
spark.sink.Iceberg = seatunnel-connector-spark-iceberg
spark.sink.Jdbc = seatunnel-connector-spark-jdbc
spark.sink.Kafka = seatunnel-connector-spark-kafka
spark.sink.Kudu = seatunnel-connector-spark-kudu
spark.sink.MongoDB = seatunnel-connector-spark-mongodb
spark.sink.Phoenix = seatunnel-connector-spark-phoenix
spark.sink.Redis = seatunnel-connector-spark-redis
spark.sink.TiDB = seatunnel-connector-spark-tidb

# SeaTunnel new connector API

seatunnel.source.FakeSource = connector-fake
seatunnel.sink.Console = connector-console
seatunnel.sink.Assert = connector-assert
seatunnel.source.Kafka = connector-kafka
seatunnel.sink.Kafka = connector-kafka
seatunnel.source.Http = connector-http-base
seatunnel.sink.Http = connector-http-base
seatunnel.sink.Feishu = connector-http-feishu
seatunnel.source.Socket = connector-socket
seatunnel.sink.Hive = connector-hive
seatunnel.source.Hive = connector-hive
seatunnel.source.Clickhouse = connector-clickhouse
seatunnel.sink.Clickhouse = connector-clickhouse
seatunnel.sink.ClickhouseFile = connector-clickhouse
seatunnel.source.Jdbc = connector-jdbc
seatunnel.sink.Jdbc = connector-jdbc
seatunnel.source.Kudu = connector-kudu
seatunnel.sink.Kudu = connector-kudu
seatunnel.sink.Email = connector-email
seatunnel.source.HdfsFile = connector-file-hadoop
seatunnel.sink.HdfsFile = connector-file-hadoop
seatunnel.source.LocalFile = connector-file-local
seatunnel.sink.LocalFile = connector-file-local
seatunnel.source.OssFile = connector-file-oss
seatunnel.sink.OssFile = connector-file-oss
seatunnel.source.Pulsar = connector-pulsar
seatunnel.source.Hudi = connector-hudi
seatunnel.sink.DingTalk = connector-dingtalk
seatunnel.source.Elasticsearch = connector-elasticsearch
seatunnel.sink.Elasticsearch = connector-elasticsearch
seatunnel.source.IoTDB = connector-iotdb
seatunnel.sink.IoTDB = connector-iotdb
seatunnel.source.Neo4j = connector-neo4j
seatunnel.sink.Neo4j = connector-neo4j
seatunnel.source.FtpFile = connector-file-ftp
seatunnel.sink.FtpFile = connector-file-ftp
seatunnel.source.SftpFile = connector-file-sftp
seatunnel.sink.SftpFile = connector-file-sftp
seatunnel.sink.Socket = connector-socket
seatunnel.source.Redis = connector-redis
seatunnel.sink.Redis = connector-redis
seatunnel.sink.DataHub = connector-datahub
seatunnel.sink.Sentry = connector-sentry
seatunnel.source.MongoDB = connector-mongodb
seatunnel.sink.MongoDB = connector-mongodb
seatunnel.source.Iceberg = connector-iceberg
seatunnel.source.InfluxDB = connector-influxdb
seatunnel.source.S3File = connector-file-s3
seatunnel.sink.S3File = connector-file-s3
seatunnel.source.AmazonDynamodb = connector-amazondynamodb
seatunnel.sink.AmazonDynamodb = connector-amazondynamodb
seatunnel.source.Cassandra = connector-cassandra
seatunnel.sink.Cassandra = connector-cassandra
seatunnel.sink.StarRocks = connector-starrocks
seatunnel.source.MyHours = connector-http-myhours
seatunnel.sink.InfluxDB = connector-influxdb
seatunnel.source.GoogleSheets = connector-google-sheets
seatunnel.sink.Pulsar = connector-pulsar
