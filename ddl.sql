sqlserver
CREATE TABLE lihj.dbo.emp_20240305 (
                                       emp_id nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       emp_name nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       gender nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       account nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       org_id nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       birth_date nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       age nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       nationality nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       province nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       city nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       email nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       phone nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       begin_date nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       remark nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       create_time nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                                       update_time nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
);
oracle
CREATE TABLE "JOYADATA"."EMP_20240304"
(	"EMP_ID" NUMBER(10,0),
     "EMP_NAME" VARCHAR2(64),
     "GENDER" CHAR(1),
     "ACCOUNT" VARCHAR2(32),
     "ORG_ID" VARCHAR2(64),
     "BIRTH_DATE" CHAR(10),
     "AGE" NUMBER(10,0),
     "NATIONALITY" CHAR(3),
     "PROVINCE" VARCHAR2(6),
     "CITY" CHAR(6),
     "EMAIL" VARCHAR2(128),
     "PHONE" VARCHAR2(16),
     "BEGIN_DATE" DATE,
     "REMARK" VARCHAR2(128),
     "CREATE_TIME" TIMESTAMP (0),
     "UPDATE_TIME" TIMESTAMP (0),
     PRIMARY KEY ("EMP_ID")
);
mysql
CREATE TABLE `emp_104_9` (
                             `emp_id` int NOT NULL,
                             `emp_name` varchar(64) DEFAULT NULL,
                             `gender` char(1) DEFAULT NULL,
                             `account` varchar(32) DEFAULT NULL,
                             `org_id` varchar(64) DEFAULT NULL,
                             `birth_date` char(10) DEFAULT NULL,
                             `age` int DEFAULT NULL,
                             `nationality` char(3) DEFAULT NULL,
                             `province` varchar(6) DEFAULT NULL,
                             `city` char(6) DEFAULT NULL,
                             `email` varchar(128) DEFAULT NULL,
                             `phone` varchar(16) DEFAULT NULL,
                             `begin_date` date DEFAULT NULL,
                             `remark` varchar(128) DEFAULT NULL,
                             `create_time` datetime DEFAULT NULL,
                             `update_time` datetime DEFAULT NULL,
                             PRIMARY KEY (`emp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
dm
CREATE TABLE emp_20240305 (
                              "emp_id" VARCHAR(255),
                              "emp_name" VARCHAR(255),
                              "gender" VARCHAR(255),
                              "account" VARCHAR(255),
                              "org_id" VARCHAR(255),
                              "birth_date" VARCHAR(255),
                              "age" VARCHAR(255),
                              "nationality" VARCHAR(255),
                              "province" VARCHAR(255),
                              "city" VARCHAR(255),
                              "email" VARCHAR(255),
                              "phone" VARCHAR(255),
                              "begin_date" VARCHAR(255),
                              "remark" VARCHAR(255),
                              "create_time" VARCHAR(255),
                              "update_time" VARCHAR(255)
);