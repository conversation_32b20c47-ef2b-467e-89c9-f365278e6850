commons-codec-1.13.jar
commons-collections4-4.4.jar
commons-compress-1.20.jar
commons-io-2.11.0.jar
commons-lang3-3.5.jar
config-1.3.3.jar
disruptor-3.4.4.jar
guava-27.0-jre.jar
hazelcast-5.1.jar
jackson-annotations-2.13.3.jar
jackson-core-2.13.3.jar
jackson-databind-2.13.3.jar
jackson-dataformat-properties-2.13.3.jar
jackson-datatype-jsr310-2.13.3.jar
jcl-over-slf4j-1.7.25.jar
jcommander-1.81.jar
log4j-api-2.17.1.jar
log4j-core-2.17.1.jar
log4j-slf4j-impl-2.17.1.jar
log4j-1.2-api-2.17.1.jar
protostuff-api-1.8.0.jar
protostuff-collectionschema-1.8.0.jar
protostuff-core-1.8.0.jar
protostuff-runtime-1.8.0.jar
scala-library-2.12.15.jar
seatunnel-jackson-2.3.4-SNAPSHOT-optional.jar
seatunnel-guava-2.3.4-SNAPSHOT-optional.jar
slf4j-api-1.7.25.jar
jsqlparser-4.5.jar
animal-sniffer-annotations-1.17.jar
checker-qual-3.10.0.jar
error_prone_annotations-2.2.0.jar
failureaccess-1.0.jar
j2objc-annotations-1.1.jar
jsr305-1.3.9.jar
jsr305-3.0.0.jar
jsr305-3.0.2.jar
listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar
json-path-2.7.0.jar
json-smart-2.4.7.jar
accessors-smart-2.4.7.jar
asm-9.1.jar
avro-1.11.1.jar