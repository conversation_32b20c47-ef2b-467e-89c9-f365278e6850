import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Command usage

## Command Entrypoint

<Tabs
    groupId="engine-type"
    defaultValue="spark2"
    values={[
        {label: 'Spark 2', value: 'spark2'},
        {label: 'Spark 3', value: 'spark3'},
        {label: 'Flink 13 14', value: 'flink13'},
        {label: 'Flink 15 16', value: 'flink15'},
    ]}>
<TabItem value="spark2">

```bash
bin/start-seatunnel-spark-2-connector-v2.sh
```

</TabItem>
<TabItem value="spark3">

```bash
bin/start-seatunnel-spark-3-connector-v2.sh
```

</TabItem>
<TabItem value="flink13">

```bash
bin/start-seatunnel-flink-13-connector-v2.sh
```

</TabItem>
<TabItem value="flink15">

```bash
bin/start-seatunnel-flink-15-connector-v2.sh
```

</TabItem>
</Tabs>


## Options

<Tabs
    groupId="engine-type"
    defaultValue="spark2"
    values={[
        {label: 'Spark 2', value: 'spark2'},
        {label: 'Spark 3', value: 'spark3'},
        {label: 'Flink 13 14', value: 'flink13'},
        {label: 'Flink 15 16', value: 'flink15'},
    ]}>
<TabItem value="spark2">

```bash
Usage: start-seatunnel-spark-2-connector-v2.sh [options]
  Options:
    --check           Whether check config (default: false)
    -c, --config      Config file
    -e, --deploy-mode Spark deploy mode, support [cluster, client] (default: 
                      client) 
    -h, --help        Show the usage message
    -m, --master      Spark master, support [spark://host:port, 
                      mesos://host:port, yarn, k8s://https://host:port, 
                      local], default local[*] (default: local[*])
    -n, --name        SeaTunnel job name (default: SeaTunnel)
    -i, --variable    Variable substitution, such as -i city=beijing, or -i 
                      date=20190318 (default: [])
```

</TabItem>
<TabItem value="spark3">

```bash
Usage: start-seatunnel-spark-3-connector-v2.sh [options]
  Options:
    --check           Whether check config (default: false)
    -c, --config      Config file
    -e, --deploy-mode Spark deploy mode, support [cluster, client] (default: 
                      client) 
    -h, --help        Show the usage message
    -m, --master      Spark master, support [spark://host:port, 
                      mesos://host:port, yarn, k8s://https://host:port, 
                      local], default local[*] (default: local[*])
    -n, --name        SeaTunnel job name (default: SeaTunnel)
    -i, --variable    Variable substitution, such as -i city=beijing, or -i 
                      date=20190318 (default: [])
```

</TabItem>
<TabItem value="flink13">

```bash
Usage: start-seatunnel-flink-13-connector-v2.sh [options]
  Options:
    --check            Whether check config (default: false)
    -c, --config       Config file
    -e, --deploy-mode  Flink job deploy mode, support [run, run-application] 
                       (default: run)
    -h, --help         Show the usage message
    --master, --target Flink job submitted target master, support [local, 
                       remote, yarn-session, yarn-per-job, kubernetes-session, 
                       yarn-application, kubernetes-application]
    -n, --name         SeaTunnel job name (default: SeaTunnel)
    -i, --variable     Variable substitution, such as -i city=beijing, or -i 
                       date=20190318 (default: [])
```

</TabItem>
<TabItem value="flink15">

```bash
Usage: start-seatunnel-flink-15-connector-v2.sh [options]
  Options:
    --check            Whether check config (default: false)
    -c, --config       Config file
    -e, --deploy-mode  Flink job deploy mode, support [run, run-application] 
                       (default: run)
    -h, --help         Show the usage message
    --master, --target Flink job submitted target master, support [local, 
                       remote, yarn-session, yarn-per-job, kubernetes-session, 
                       yarn-application, kubernetes-application]
    -n, --name         SeaTunnel job name (default: SeaTunnel)
    -i, --variable     Variable substitution, such as -i city=beijing, or -i 
                       date=20190318 (default: [])
```

</TabItem>
</Tabs>

## Example

<Tabs
    groupId="engine-type"
    defaultValue="spark2"
    values={[
        {label: 'Spark 2', value: 'spark2'},
        {label: 'Spark 3', value: 'spark3'},
        {label: 'Flink 13 14', value: 'flink13'},
        {label: 'Flink 15 16', value: 'flink15'},
    ]}>
<TabItem value="spark2">

```bash
bin/start-seatunnel-spark-2-connector-v2.sh --config config/v2.batch.config.template -m local -e client
```

</TabItem>
<TabItem value="spark3">

```bash
bin/start-seatunnel-spark-3-connector-v2.sh --config config/v2.batch.config.template -m local -e client
```

</TabItem>
<TabItem value="flink13">

```bash
bin/start-seatunnel-flink-13-connector-v2.sh --config config/v2.batch.config.template
```

</TabItem>
<TabItem value="flink15">

```bash
bin/start-seatunnel-flink-15-connector-v2.sh --config config/v2.batch.config.template
```

</TabItem>
</Tabs>
