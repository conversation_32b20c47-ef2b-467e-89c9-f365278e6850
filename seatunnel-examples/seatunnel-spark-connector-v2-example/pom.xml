<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-examples</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-spark-connector-v2-example</artifactId>
    <name>SeaTunnel : Examples : Spark Connector V2</name>

    <properties>
        <spark.scope>compile</spark.scope>
        <spark.2.4.0.jackson.version>2.6.7</spark.2.4.0.jackson.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${spark.2.4.0.jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-spark-2-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--   seatunnel-transforms-v2   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-transforms-v2</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   seatunnel-transforms-v2   -->

        <!--   seatunnel connectors   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-fake</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   seatunnel connectors   -->

        <!--spark-->
        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-streaming_${scala.binary.version}</artifactId>
            <version>${spark.2.4.0.version}</version>
            <scope>${spark.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-core_${scala.binary.version}</artifactId>
            <version>${spark.2.4.0.version}</version>
            <scope>${spark.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-sql_${scala.binary.version}</artifactId>
            <version>${spark.2.4.0.version}</version>
            <scope>${spark.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.spark</groupId>
            <artifactId>spark-hive_${scala.binary.version}</artifactId>
            <version>${spark.2.4.0.version}</version>
            <scope>${spark.scope}</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.web</groupId>
                    <artifactId>javax.servlet.jsp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.thoughtworks.paranamer</groupId>
                    <artifactId>paranamer</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>net.jpountz.lz4</groupId>
            <artifactId>lz4</artifactId>
            <version>1.3.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.27</version>
        </dependency>

        <!-- 由于JDK问题，需要升级这个jar到2.8以上 -->
        <dependency>
            <groupId>com.thoughtworks.paranamer</groupId>
            <artifactId>paranamer</artifactId>
            <version>2.8</version>
        </dependency>

    </dependencies>

</project>
