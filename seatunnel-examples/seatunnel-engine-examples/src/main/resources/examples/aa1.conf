env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select * from web_dpt_recommend"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="test101901m_source_1"
        "parallelism"=2
        }





}
transform {
FieldMapper{
               source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_2"
                        field_mapper={
                            C_PK_ID=pkId
                            C_DPT_CDE=dptCde
                            C_RECOMMEND_CDE=recommendCde
                            C_CRT_CDE=crtCde
                            T_CRT_TM=crtTm
                            C_UPD_CDE=uptCde
                            T_UPD_TM=uptTm
                            C_STATUS=status
                        }
                        }
}
sink {
        Jdbc {
        url="********************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="Djmysql_dat"
            password="Mysql@#2021"
        database="gis"
        table="ggdptrecommend"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="test101901m_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["pkId"]
            "enable_upsert"="true"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}
