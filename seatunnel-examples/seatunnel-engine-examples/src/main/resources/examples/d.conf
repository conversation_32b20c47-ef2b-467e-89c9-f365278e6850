env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="***************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,card,name,age,city from test101901m Where 1=1 "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="test101901m_source_1"
        "parallelism"=2
    }





}
transform {
            FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_2"
                        field_mapper={
                            name=name
                            id=id
                            city=city
                            card=card
                            age=age
                        }
            }
            FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_3"
                        field_mapper={
                            name=name
                            id=id
                            city=city
                            card=card
                            age=age
                        }
            }
            FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_4"
                        field_mapper={
                            name=name
                            id=id
                            city=city
                            card=card
                            age=age
                        }
            }
            FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_5"
                        field_mapper={
                            name=name
                            id=id
                            city=city
                            card=card
                            age=age
                        }
            }
}
sink {
    Jdbc {
        url="***************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="tongji"
        table="test4246"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="test101901m_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["id"]
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
    Jdbc {
        url="**********************************************"
        driver="org.postgresql.Driver"
            user="postgres"
            password="joyadata"
        database="postgres"
        table="public.test4246"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="test101901m_source_1_trans_3"
        "generate_sink_sql"="true"
            "primary_keys" = ["id"]
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
    Jdbc {
        url="jdbc:dm://192.168.90.104:30236"
        driver="dm.jdbc.driver.DmDriver"
            user="SYSDBA"
            password="SYSDBA001"
        database="SYSDBA"
        table="SYSDBA.TEST4246"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="test101901m_source_1_trans_4"
        "generate_sink_sql"="true"
            "primary_keys" = ["id"]
            "field_ide"=UPPERCASE
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
    Jdbc {
        url="******************************************************"
        driver="com.microsoft.sqlserver.jdbc.SQLServerDriver"
            user="SA"
            password="Sa@12345678"
        database="lihj"
        table="dbo.test4246"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="test101901m_source_1_trans_5"
        "generate_sink_sql"="true"
            "primary_keys" = ["id"]
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
}