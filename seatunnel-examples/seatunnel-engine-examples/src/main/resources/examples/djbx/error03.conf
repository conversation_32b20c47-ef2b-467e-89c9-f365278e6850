env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="SELECT prmdue.C_RCPT_NO,                   prmdue.C_DPT_CDE,                   prmdue.C_DPTACC_CDE,                   prmdue.C_PLY_NO,                   prmdue.C_APP_NO,                   prmdue.C_EDR_NO,                   prmdue.N_TMS,                   prmdue.C_BSNS_TYP,                   prmdue.C_PROD_NO,                   prmdue.C_TRAN_FLAG,                   prmdue.C_FEETYP_CDE,                   prmdue.C_CLNT_MRK,                   prmdue.C_BALA_MRK,                   prmdue.T_INSRNC_BGN_TM,                   prmdue.T_INSRNC_END_TM,                   prmdue.C_LONGSHORT_FLAG,                   prmdue.C_BS_CUR,                   prmdue.N_BS_AMT,                   prmdue.C_DUE_CUR,                   prmdue.N_DUE_AMT,                   prmdue.T_DUE_TM,                   prmdue.C_DUE_MRK,                   prmdue.C_RP_CUR,                   prmdue.N_RP_AMT,                   prmdue.T_RP_TM,                   prmdue.C_RP_FLAG,                   prmdue.T_PAID_TM,                   prmdue.N_PAID_AMT,                   prmdue.N_PRE_AMT,                   prmdue.C_EDR_TYP,                   prmdue.C_EDR_RSN,                   prmdue.C_INST_MRK,                   prmdue.C_MAIN_CON_CDE,                   prmdue.C_PAYER_CDE,                   prmdue.C_PAYER_NME,                   prmdue.C_PAY_MDE_CDE,                   prmdue.T_PAY_BGN_TM,                   prmdue.T_PAY_END_TM,                   prmdue.C_SLSGRP_CDE,                   prmdue.C_SLS_CDE,                   prmdue.C_CHA_MRK,                   prmdue.C_CHA_CLS,                   prmdue.C_CHA_CDE,                   prmdue.C_BANK_ACCOUNT,                   prmdue.C_APP_NME,                   prmdue.C_CIRC_VHL_TYP,                   prmdue.C_LCN_NO,                   prmdue.C_AGT_AGR_NO,                   prmdue.N_TAX_RATE,                   prmdue.C_PRN_NO,                   prmdue.C_CRT_CDE,                   sysdate -1 as T_CRT_TM,                   prmdue.C_UPD_CDE,                   prmdue.T_UPD_TM,                   prmdue.C_OPT_NO,                   prmdue.C_ROLLBACK_MARK,                   prmdue.C_TO_FIN_FLAG,                   prmdue.C_BANK_CDE,                   prmdue.C_ARP_FLAG,                   prmdue.C_COMPARE_FALG,                   prmdue.N_CAN_SUM,                   prmdue.C_ACCNT_FLAG,                   prmdue.C_PLY_MRK,                   prmdue.N_OTHER_AMT,                   prmdue.C_ACCOUNT,                   prmdue.C_CUSTOMER_ACCOUNTS,                   prmdue.C_BATCH_NO,                   prmdue.C_BFN_NO,                   prmdue.T_AGREE_TM,                   prmdue.C_AGREE_CDE,                   prmdue.N_SHARES_NO,                   prmdue.T_BLN_TM,                   prmdue.C_CON_DPT_CDE,                   null as C_TRANS_MRK,                   null as T_TRANS_TM,                   prmdue.C_MEMO,                   prmdue.N_STAMP_TAX,                   prmdue.N_EDR_PRJ_NO,                   prmdue.C_POS_NO,                   prmdue.C_CAV_NO,                   prmdue.C_BAL_TYPE,                   prmdue.C_HEAD_CAV_NO,                   prmdue.C_BOOKID,                   prmdue.N_FREE_AMT,                   prmdue.C_SENDTAX_CDE,                   prmdue.C_CANCEL_FLAG,                   prmdue.C_VERIFY_FLAG,                   prmdue.T_VERIFY_TM,                   prmdue.C_TRANS_TYPE,                   prmdue.C_MANUAL_CHECK_FLAG                   from                   WEB_FIN_PRM_DUE prmdue, WEB_PLY_BASE base                   WHERE prmdue.c_app_no = base.c_app_no                   and base.c_dpt_cde like '22%'                   and SUBSTR(base.c_prod_no,1,2) <> '03'                   and prmdue.t_crt_tm>=trunc(sysdate-180)                     "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
            "parallelism"=1
        }
}

transform {
}
sink {
        Jdbc {
        url="******************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Dc_user#123"
        database="djdb"
        table="EASTCHECK.WEB_FIN_PRM_DUE_02"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
        pk_strategy =stop
	    insert_error_strategy =continue
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}