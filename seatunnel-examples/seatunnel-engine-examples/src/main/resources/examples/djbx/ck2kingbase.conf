env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
         Clickhouse {
           host = "***********:8123"
           database = "dsg"
           username = "admin"
           password = "admin"
           sql = " select id,name,address from dsg_20240425"
           server_time_zone = "UTC"
            clickhouse.options.config={
                sslmode=none
                database = "dsg"
            }
         }
}
transform {
}
sink {
    Jdbc {
        url = "***************************************"
        driver = "com.kingbase8.Driver"
        user = "dsg"
        password = "DT7C_^b^yh"
        database="dsg"
        table="dsg.dsg_20240425"
                    "primary_keys" = ["id"]
                    "enable_upsert"="true"
        "generate_sink_sql"="true"
    }
}

