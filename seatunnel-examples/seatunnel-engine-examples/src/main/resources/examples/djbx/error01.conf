env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select b.c_ply_no as c_ply_no       ,b.c_orig_ply_no as c_orig_ply_no       ,b.c_dpt_cde as c_dpt_cde       ,(select c.c_grant_dpt_cnm from abtest.web_org_grantdpt c where c.c_grant_dpt_cde= b.c_grant_dpt_cde) as c_dealer_name       ,(select c_cnm           from web_bas_comm_code          where c_par_cde = '190'            and c_cde in ('19001', '19002', '19007')            and c_cde = b.c_bsns_typ) as c_new_bsns_typ       ,(select e.c_cha_nme           from abtest.web_cus_cha e          where e.c_cha_cde = b.c_brkr_cde) as c_brkr_name       ,b.c_brkr_cde as c_brkr_cde       ,b.c_channel_src as c_cha_nme       ,(select c_sls_nme           from abtest.web_org_sales          where b.c_sls_cde = c_sls_cde) as c_sls_nme       ,b.c_sls_cde as c_sls_id       ,b.t_app_tm as t_opr_tm       ,b.t_insrnc_bgn_tm as t_insrnc_bgn_tm       ,b.t_insrnc_end_tm as t_insrnc_end_tm       ,b.c_prod_no as c_prod_no       ,b.n_amt as n_amt       ,b.n_prm as n_prm       ,a.c_app_nme as c_app_nme       ,a.c_clnt_mrk as c_app_clnt_mrk       ,a.c_certf_cls as c_app_certf_cls       ,a.c_certf_cde as c_app_certf_cde       ,a.c_mobile as c_app_mobile       ,a.c_email as c_app_email       ,a.c_province as c_province       ,a.c_city as c_city       ,i.c_insured_nme as c_insured_nme       ,i.c_clnt_mrk as c_ins_clnt_mrk       ,i.c_certf_cls as c_ins_certf_cls       ,i.c_certf_cde as c_ins_certf_cde       ,i.c_mobile as c_ins_mobile       ,i.c_email as c_ins_email       ,v.c_reg_owner as c_owner_nme       ,(select c_cnm from abtest.web_Bas_Comm_Code_Vhl where V.C_Reg_Vhl_Typ = c_cde and N_Combox_Id = '20576' and rownum=1 ) as vehicle_code       ,v.c_fst_reg_ym_bar as c_fst_reg_ym       ,v.t_certificate_date as t_certificate_date       ,v.c_chg_owner_flag as c_chg_owner_flag       ,v.c_new_plate as c_new_mrk       ,v.c_ecdemic_mrk as c_ecdemic_mrk       ,v.c_plate_no as c_plate_no       ,v.c_frm_no as c_frm_no       ,v.c_eng_no as c_eng_no       ,v.c_model_nme as c_model_nme       ,v.c_vhl_typ as c_vhl_typ       ,v.c_usage_cde as c_usage_cde       ,v.n_seat_num as n_seat_num       ,v.n_displacement as n_displacement       ,v.n_tonage as n_tonage       ,case          when b.c_prod_no in ('0316', '0320') then           (select vt.n_curb_wt              from abtest.web_ply_vs_tax vt             where b.c_app_no = vt.c_app_no)          when b.c_prod_no not in ('0316', '0320') then           v.n_resv_num_2        end as n_po_weight       ,v.c_is_online_car as c_is_online_car       , w.c_certf_cls as c_certf_cls       ,w.c_certf_cde as c_certf_cde       ,case          when b.c_prod_no in ('0316', '0320') then           pc.n_resv_num_3          when b.c_prod_no not in ('0316', '0320') then           pc.n_resv_num_2        end as n_ago_clm_rec       ,pc.pricing_adjust_value as pricing_adjust_value       ,(select c.n_resv_num_10           from abtest.web_ply_cvrg c          where b.c_app_no = c.c_app_no            and rownum = 1) as n_resv_num_10       ,sysdate as t_insert_tm       ,sysdate as t_upd_tm       ,pc.bill_elr as billElr       ,b.c_rec_sub_channel as C_NEW_CHA_TYPE_DET       ,case           when v.c_new_plate = '1' and v.c_new_plate is not null then '4'           when b.C_ORIG_PLY_NO is not null and length(b.C_ORIG_PLY_NO) > 1 then '1'           else '3'        end as c_renew_mrk       ,b.c_is_dx_prod as c_is_dx_prod       ,v.c_use_year as c_use_year       ,v.n_new_purchase_value as purchase_price       ,v.c_fuel_type as c_fuel_type       ,v.C_SPECIAL_RISK_FLAG as C_SPECIAL_RISK_FLAG       ,a.C_SEX as c_app_sex       ,a.C_RESV_TXT_7 as c_app_age       ,a.T_BIRTHDAY as t_app_birthday       ,substr(b.c_prod_no,0,2) as c_cvrg_typ       ,case         when b.c_cimrk_flag = '0' or b.c_cimrk_flag = '1' then '3'         when b.c_cimrk_flag = '2' then '1'         when b.c_cimrk_flag = '3' then '2'         when b.c_prod_no in ('0316', '0320') then '1'         when b.c_prod_no in ('0355', '0356', '0358') then '2'         end    c_policy_type      , b.t_crt_tm as t_ply_crt_tm      , (select br.c_ply_no from abtest.web_ply_base br where br.c_app_no = b.c_rel_app_no) as c_rel_ply_no,      (CASE WHEN a.c_employee_level IS NOT NULL THEN '1'             WHEN i.c_employee_level IS NOT NULL THEN '1'            WHEN w.c_employee_level IS NOT NULL THEN '1'            ELSE '0' END ) isEmployee,      (CASE WHEN a.c_employee_level IS NOT NULL or i.c_employee_level IS NOT NULL or w.c_employee_level IS NOT NULL              THEN LEAST(nvl(a.c_employee_level,999999),nvl(i.c_employee_level,999999),nvl(w.c_employee_level,999999))             ELSE NULL END) employeeLevel   from abtest.web_ply_base      b       ,abtest.web_ply_applicant a       ,abtest.web_ply_insured   i       ,abtest.web_ply_prm_coef  pc       ,abtest.web_ply_vhl       v       ,abtest.web_ply_vhlowner  w  where b.c_app_no = a.c_app_no    and b.c_app_no = i.c_app_no    and b.c_app_no = pc.c_app_no(+)    and b.c_app_no = v.c_app_no    and b.c_app_no = w.c_app_no(+)    and b.c_latest_mrk = '1'    and b.c_prod_no in        ('0316', '0320', '0325', '0326', '0327', '0336', '0355', '0356', '0358')    and b.t_crt_tm >= trunc(sysdate-1) and b.t_crt_tm < trunc(sysdate)"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
            "parallelism"=1
        }
}

transform {

}
sink {
        Jdbc {
        url="***********************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="xbdat"
            password="Xbdat#416"
        database="xbdat"
        table="renewalpolicy"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="null_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["c_ply_no"]
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}