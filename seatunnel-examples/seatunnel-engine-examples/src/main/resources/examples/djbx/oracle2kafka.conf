env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
               driver="com.mysql.cj.jdbc.Driver"
               user="root"
               password="joyadata"
               query="select * from `seatunnel_source`.`emp_quality_millions_100w`"
        "batch_size"="1024"
        "partition_column"="emp_id"
        "parallelism"=5
        }

}
transform {
}
sink {
kafka {
      topic = "kafkawriterlihj"
      bootstrap.servers = "172.29.250.34:9092"
      format = text
      field_delimiter=","
  }

}