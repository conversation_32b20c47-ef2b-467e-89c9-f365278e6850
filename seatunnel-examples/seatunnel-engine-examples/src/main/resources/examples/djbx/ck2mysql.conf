env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
         Clickhouse {
           host = "***********:8123"
           database = "dsg"
           username = "admin"
           password = "admin"
           sql = " select id,name,address from dsg_20240425"
           server_time_zone = "UTC"
            clickhouse.options.config={
                sslmode=none
                database = "dsg"
            }
         }
}
transform {
}
sink {
       Jdbc {
           url="*********************************"
           driver="com.mysql.cj.jdbc.Driver"
           user="dsg"
           password="dsg"
           database="dsg"
           table="dsg_20240425"
           generate_sink_sql="true"
       }
}

