env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="***************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="cdh"
        password="Cdh_1234"
        query="select timetype,statdate,comgrade,branchcode,subbranchcode,directorcode,agentgroupcode,updatetype,branchname,subbranchname,directorname,agentgroupname,recruit_scores,target_values,completion_rate,newagent_num,xh_num,jy_num,partner_num,mdrt_num,all_labor_num,kuduloadtime,ymflag from app_uat.gx_app_individual_recruit_his "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="gx_app_individual_recruit_his_source_1"
            "parallelism"=1
        }
}

transform {
        FieldMapper{
            source_table_name="gx_app_individual_recruit_his_source_1"
            result_table_name="gx_app_individual_recruit_his_source_1_trans_2"
                    field_mapper={
                        target_values=target_values
                        branchcode=branchcode
                        jy_num=jy_num
                        agentgroupcode=agentgroupcode
                        kuduloadtime=kuduloadtime
                        subbranchname=subbranchname
                        directorname=directorname
                        mdrt_num=mdrt_num
                        subbranchcode=subbranchcode
                        updatetype=updatetype
                        ymflag=ymflag
                        agentgroupname=agentgroupname
                        completion_rate=completion_rate
                        partner_num=partner_num
                        statdate=statdate
                        directorcode=directorcode
                        newagent_num=newagent_num
                        timetype=timetype
                        xh_num=xh_num
                        recruit_scores=recruit_scores
                        all_labor_num=all_labor_num
                        comgrade=comgrade
                        branchname=branchname
                    }
        }
}
sink {
        Jdbc {
        url="******************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="kanbandat"
            password="lvr)yyx2"
        database="kanbanuat"
        table="gx_app_individual_recruit_his"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="gx_app_individual_recruit_his_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["timetype","statdate","comgrade","branchcode","subbranchcode","directorcode","agentgroupcode","ymflag"]
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}