env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        OssFile {
        path="/dsgtest/dsg/products_oce_for_oracle_sink_oss_0_1_0.txt"
        bucket="oss://joyadata"
        access_key="LTAI5tPWRfPWF18QECwdDJRZ"
        access_secret="******************************"
        endpoint="http://cn-beijing.oss.aliyuncs.com"
        file_format_type="text"
        result_table_name="oss_ali_source_1"
        field_delimiter="\u0001"
        parse_partition_from_path="true"
        date_format="yyyy-MM-dd"
        datetime_format="yyyy-MM-dd HH:mm:ss"
        time_format="HH:mm:ss"
        skip_header_row_number=0
        compress_codec="none"
        schema= {
            fields {
            product_id=STRING
            name=STRING
            price=STRING
            stock=STRING
            }
        }
        encoding="UTF-8"
        }

}
transform {
}
sink {
        MergeLocalFile {
        final_name="D://dsg//user_oss_sink_local_orc.txt"
        path="d://dsg//data"
        source_table_name = "oss_ali_source_1"
        tmp_path="d://dsg//data//tmp"
        file_format_type="orc"
        sink_columns=["product_id","name","price","stock"]
        batch_size="1000"
        compress_codec="none"
        validates="false"
        overwrite_file="false"
        date_format="yyyy-MM-dd"
        datetime_format="yyyy-MM-dd HH:mm:ss"
        time_format="HH:mm:ss"
        fixed_field_length_strategy="false"
        encoding="UTF-8"
        }
}