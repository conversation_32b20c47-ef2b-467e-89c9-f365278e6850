 env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        SftpFile {
        host="**************"
        port="22"
        user="root"
        password="joyadata"
        path="/opt/test/"
        file_format_type="text"
            result_table_name="sftp_source_1"
            parse_partition_from_path="true"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            skip_header_row_number=0
            compress_codec="none"
            schema= {
            fields {
			TX_DT=STRING
			SRCSYS_ID=STRING
			 CIF_ORG=STRING
			 CAR_DT=STRING
			 CAR_ID=STRING
			 CAR_NM=STRING
			 CARCODE_ID=STRING
			 HOME_ID=STRING
			 W_HOMLN=DECIMAL(14,2)
			 N_STAY=DECIMAL(10,0)
			 INCOMSCR_ID=STRING
			 SPU_NM=STRING
			 SPUTEL_NO=STRING
			 INCOMSCR2_ID=STRING
			 W_INCOME=DECIMAL(8,2)
			 N_YRINCOM=STRING
			 SPUMOB_ID=STRING
			 N_DEPEND=DECIMAL(10,0)
			 REFACC1_ID=STRING
			 REFACC2_ID=STRING
			 REFACC3_ID=STRING
			 REFACC4_ID=STRING
			 REFCODE1_ID=STRING
			 REFCODE2_ID=STRING
			 REFCODE3_ID=STRING
			 REFCODE4_ID=STRING
			 REFCODE5_ID=STRING
			 REFCODE6_ID=STRING
			 REFCODE7_ID=STRING
			 REFCODE8_ID=STRING
			 REFCODE9_ID=STRING
             REFCODE10_ID=STRING
			 REFCODE11_ID=STRING
			 REFCODE12_ID=STRING
			 REFCODE13_ID=STRING
			 FIELD28=STRING
			 SCHLNMCH_NM=STRING
			 SCHLNMEN_NM=STRING
			 EMAILIN_NM=STRING
             CUSTR_TAG=STRING
            }
            }
        }
}
transform {
        FieldMapper{
            source_table_name="sftp_source_1"
            result_table_name="sftp_source_1_trans_2"
                    field_mapper={
                        CUSTR_TAG=CUSTR_TAG
                        REFCODE2_ID=REFCODE2_ID
                        CAR_ID=CAR_ID
                        REFACC4_ID=REFACC4_ID
                        REFCODE6_ID=REFCODE6_ID
                        REFACC2_ID=REFACC2_ID
                        REFCODE4_ID=REFCODE4_ID
                        REFCODE8_ID=REFCODE8_ID
                        REFCODE11_ID=REFCODE11_ID
                        REFCODE13_ID=REFCODE13_ID
                        CARCODE_ID=CARCODE_ID
                        N_STAY=N_STAY
                        SCHLNMEN_NM=SCHLNMEN_NM
                        INCOMSCR2_ID=INCOMSCR2_ID
                        EMAILIN_NM=EMAILIN_NM
                        FIELD28=FIELD28
                        HOME_ID=HOME_ID
                        SPUTEL_NO=SPUTEL_NO
                        TX_DT=TX_DT
                        INCOMSCR_ID=INCOMSCR_ID
                        REFCODE1_ID=REFCODE1_ID
                        W_INCOME=W_INCOME
                        CAR_NM=CAR_NM
                        REFCODE5_ID=REFCODE5_ID
                        REFACC3_ID=REFACC3_ID
                        REFACC1_ID=REFACC1_ID
                        REFCODE3_ID=REFCODE3_ID
                        REFCODE7_ID=REFCODE7_ID
                        REFCODE9_ID=REFCODE9_ID
                        REFCODE10_ID=REFCODE10_ID
                        SPUMOB_ID=SPUMOB_ID
                        REFCODE12_ID=REFCODE12_ID
                        SRCSYS_ID=SRCSYS_ID
                        SCHLNMCH_NM=SCHLNMCH_NM
                        CAR_DT=CAR_DT
                        W_HOMLN=W_HOMLN
                        SPU_NM=SPU_NM
                        N_DEPEND=N_DEPEND
                        CIF_ORG=CIF_ORG
                    }
        }
}
sink {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="c##QJQ"
            password="QJQ"
        database="ORCLCDB"
        table="C##QJQ.TEST_SFTP"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="sftp_source_1_trans_2"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}