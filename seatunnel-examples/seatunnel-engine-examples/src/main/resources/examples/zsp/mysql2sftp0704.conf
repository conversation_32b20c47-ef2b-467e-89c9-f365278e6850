env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="*******************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select `id`,`name` from `test_sftp` Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="test_sftp_source_1"
            "parallelism"=1
        }




}
transform {
        FieldMapper{
            source_table_name="test_sftp_source_1"
            result_table_name="test_sftp_source_1_trans_2"
                    field_mapper={
                        id=id
                        name=name
                    }
        }
}
sink {
        SftpFile {
        host="**************"
        port="22"
        user="root"
        password=joyadata
        path="/opt/zsp"
        is_enable_transaction="true"
            source_table_name="test_sftp_source_1_trans_2"
            tmp_path="/tmp/seatunnel"
            custom_filename="false"
            filename_time_format="yyyy.MM.dd"
            file_format_type="text"
            field_delimiter=","
            row_delimiter="\n"
            have_partition="false"
            batch_size="1000000"
            compress_codec="none"
        }
}