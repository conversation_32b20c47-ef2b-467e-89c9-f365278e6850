env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
Jdbc {
url="************************************************"
driver="oracle.jdbc.driver.OracleDriver"
user="c##QJQ"
password=QJQ
query="select \"EMP_ID\",\"ACCOUNT\",\"BIRTH_DATE\",\"AGE\",\"NATIONALITY\",\"CITY\",\"BEGIN_DATE\",\"CREATE_TIME\",\"INSERT_DATE\" from \"C##QJQ\".\"ZSP_TEST_GPLOAD_2000W\" Where 1=1 "
}



}
transform {
}
sink {

Console{}

}