env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select id,name,gender,birth_date,id_number,phone_number,email,home_address,marital_status,education,major,graduate_school,work_experience,position,company_name,join_date,leave_date,monthly_salary,social_security_number,emergency_contact,emergency_contact_phone,is_party_member,health_status,height,weight,blood_type,household_registration_address,current_address,hobbies,remarks,is_employed,employee_number,department,work_location,bank_account_number,bank_name,skills,certificates,social_media_account,created_at,updated_at from personnel_information_1w Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="personnel_information_1w_source_1"
            "parallelism"=1
        }
}

transform {
        FieldMapper{
            source_table_name="personnel_information_1w_source_1"
            result_table_name="personnel_information_1w_source_1_trans_2"
                    field_mapper={
                        bank_account_number=bank_account_number
                        education=education
                        gender=gender
                        birth_date=birth_date
                        created_at=created_at
                        work_location=work_location
                        skills=skills
                        home_address=home_address
                        major=major
                        updated_at=updated_at
                        blood_type=blood_type
                        bank_name=bank_name
                        leave_date=leave_date
                        social_security_number=social_security_number
                        id=id
                        health_status=health_status
                        department=department
                        emergency_contact_phone=emergency_contact_phone
                        email=email
                        work_experience=work_experience
                        height=height
                        id_number=id_number
                        household_registration_address=household_registration_address
                        join_date=join_date
                        weight=weight
                        current_address=current_address
                        graduate_school=graduate_school
                        is_employed=is_employed
                        marital_status=marital_status
                        monthly_salary=monthly_salary
                        certificates=certificates
                        social_media_account=social_media_account
                        hobbies=hobbies
                        company_name=company_name
                        name=name
                        employee_number=employee_number
                        phone_number=phone_number
                        position=position
                        remarks=remarks
                        emergency_contact=emergency_contact
                        is_party_member=is_party_member
                    }
        }
}
sink {
        Jdbc {
        url="*******************************************************************"
        driver="com.pivotal.jdbc.GreenplumDriver"
            user="gp"
            password="gpadmin"
        database="postgres"
        table="qjq.personnel_information_1w_mysql2gp0613"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="personnel_information_1w_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["id"]
            "field_ide"=LOWERCASE
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}