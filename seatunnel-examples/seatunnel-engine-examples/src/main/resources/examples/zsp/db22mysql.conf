 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="*************************************"
driver="com.ibm.db2.jcc.DB2Driver"
user="db2inst1"
password="Cdyanfa_123456"
query="select \"ID\",\"NAME\" from \"TEST \".\"TEST0904\" Where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"table.path"=""TEST "."TEST0904""
"parallelism"=1
}



}
transform {
}
sink {
        Jdbc {
        url="*******************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password=joyadata
        database="zsp_test"
        table="dm0705"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="ZSP_TEST0705_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
            field_ide="LOWERCASE"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}
