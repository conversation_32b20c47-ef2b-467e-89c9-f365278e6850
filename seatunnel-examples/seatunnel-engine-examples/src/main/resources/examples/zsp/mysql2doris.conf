env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        <PERSON> {
        fenodes="192.168.90.221:8030"
        username="root"
            password=""
        database="test"
        table="m_dept"
            query-port="9030"
            result_table_name="doris_source_1"
            doris.filter.query=" 1 = 1 "
            doris.batch.size="1024"
            doris.request.read.timeout.ms="30000"
            doris.request.connect.timeout.ms=30000
        }




}
transform {
}
sink {
        Doris {
        fenodes="192.168.90.221:8030"
        query-port="9030"
        username="root"
            password=""
        database="test"
        table="dept_0812"
            table.identifier="test.dept_0812"
            source_table_name="doris_source_1"
            sink.label-prefix="f0aeab90-d8af-4c4a-b53c-8669520c4b6b"
            sink.buffer-size="256"
            sink.buffer-count="2"
            doris.batch.size=1024

            doris.config={
                format=json
                read_json_by_line=true
            }
        }

}