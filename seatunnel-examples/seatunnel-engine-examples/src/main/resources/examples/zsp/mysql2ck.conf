env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
query="select `id`,`name`,`date_1`,`data_time` from `zsp_test`.`ck` Where 1=1 "
"fetch_size"="1000"
"split.size"="1000"
"table_path"="`ck`"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"parallelism"=1
}



}
transform {
}
sink {
Clickhouse {
source_table_name = "E000001_source_1"
host = "**************:18123"
database = "default"
table = "zsp_test_0924"
username = "root"
password = "Cdyanfa_123456"
support_upsert = false
}

}