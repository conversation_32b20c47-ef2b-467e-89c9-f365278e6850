env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Kafka {
        topic="zsp_0706"
        bootstrap.servers="192.168.90.220:9092"
            pattern="false"
            consumer.group="SeaTunnel-Consumer-Group"
            commit_on_checkpoint="true"
            format="text"
            format_error_handle_way="fail"
            field_delimiter=","
            start_mode=earliest
            start_mode.offsets=""
            start_mode.timestamp=""
            partition-discovery.interval-millis="-1"
        }




}
transform {
}
sink {
Console{}
}