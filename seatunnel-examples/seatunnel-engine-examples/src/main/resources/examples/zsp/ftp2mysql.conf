 env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
FtpFile {
host="**************"
port="21"
user="root"
password=Cdyanfa_123456
path="/data1/ftp/data/root/opt/zsp/T_863259359491129345_159f502199_0_1_0.txt"
file_format_type="text"
result_table_name="ftp_source_1"
connection_mode="active_local"
field_delimiter=","
parse_partition_from_path="true"
date_format="yyyy-MM-dd"
datetime_format="yyyy-MM-dd HH:mm:ss"
time_format="HH:mm:ss"
skip_header_row_number=0
compress_codec="none"
schema= {
fields {
id=INT
name=STRING
date_1=DATE
date_time=TIMESTAMP
}
}
}



}
transform {
}
sink {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
database="zsp_test"
table="ftp0710"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="ftp_source_1"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}
}