env {
"job.mode"="BATCH"
"job.name"="2"
"execution.parallelism"="2"
"checkpoint.interval"="1000"
}
source {
Jdbc {
url="*********************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=Cdyanfa_123456
query="select `id`,`name`,`gender`,`birth_date`,`id_number`,`phone_number`,`email`,`home_address`,`marital_status`,`education`,`major`,`graduate_school`,`work_experience`,`position`,`company_name`,`join_date`,`leave_date`,`monthly_salary`,`social_security_number`,`emergency_contact`,`emergency_contact_phone`,`is_party_member`,`health_status`,`height`,`weight`,`blood_type`,`household_registration_address`,`current_address`,`hobbies`,`remarks`,`is_employed`,`employee_number`,`department`,`work_location`,`bank_account_number`,`bank_name`,`skills`,`certificates`,`social_media_account`,`created_at`,`updated_at` from `personnel_out1` Where 1=1 "
"batch_size"="10"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"partition_column"="id"
"partition_num"="6"
"parallelism"=6
}




}
transform {
}
sink {
LocalFile {
path="/opt/zsp/"

custom_filename=true
file_name_expression="lf708"
is_enable_transaction=false

tmp_path="/dsg/test//tmp"
file_name_expression="lf708"
file_format_type="text"
sink_columns=["id","name","gender","birth_date","id_number","phone_number","email","home_address","marital_status","education","major","graduate_school","work_experience","position","company_name","join_date","leave_date","monthly_salary","social_security_number","emergency_contact","emergency_contact_phone","is_party_member","health_status","height","weight","blood_type","household_registration_address","current_address","hobbies","remarks","is_employed","employee_number","department","work_location","bank_account_number","bank_name","skills","certificates","social_media_account","created_at","updated_at"]
batch_size="1000"
compress_codec="none"
}
}