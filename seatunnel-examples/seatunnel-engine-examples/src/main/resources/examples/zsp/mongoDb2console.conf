 env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        MongoDB {
        uri="**************************************************"
        database="admin"
        collection="zsp_test0706"
        schema= {
        fields {
            _id=STRING
            name=STRING
            date_1=date
            id=int
            date_time=TIMESTAMP
        }
        }
            fetch.size="2048"
            max.time-min="600"
            result_table_name = "zsp_test0706_source_1"
        }




}
transform {
}
sink {
        Jdbc {
        url="*******************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password=joyadata
        database="zsp_test"
        table="mongodb_0706"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="zsp_test0706_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}