env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        ObsFile {
        path="/20240617/zsp/T_854910336912850945_eb687b0a06_1_1_0.txt"
        bucket="obs://cdyanfa"
        access_key="AI171YTRG8DMGK8DZJLN"
        security_key="AnpGOuW0GCAWBTcPrARQgG0fKKZ9K9QKO3uueG0W"
        endpoint="http://obs.cn-east-5.myhuaweicloud.com"
        file_format_type="text"
            parse_partition_from_path="true"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            skip_header_row_number=0
            compress_codec="none"
            schema {
                fields {
                        emp_id=string
                        emp_name=string
                        gender=string
                        account=string
                        org_id=string
                        age=string
                }
            }
        }




}
transform {
        FieldMapper{
            source_table_name="oss_huawei_source_1"
            result_table_name="oss_huawei_source_1_trans_2"
                    field_mapper={
                        emp_id=emp_id
                        emp_name=emp_name
                        gender=gender
                        account=account
                        org_id=org_id
                        age=age
                    }
        }
}
sink {
        LocalFile {
        path="/opt/zsp"
            tmp_path="/opt/zsp/tmp"
            file_format_type="text"
            field_delimiter="\u0001"
            row_delimiter="\n"
            batch_size="1000"
            compress_codec="none"
        }
}