env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
        LocalFile {
        path="/opt/lf.txt"
        file_format_type="text"
            field_delimiter="."
            row_delimiter=","
            parse_partition_from_path=true
            date_format=yyyy-MM-dd
            datetime_format="yyyy-MM-dd HH:mm:ss"
            schema= {
            fields {
                name=STRING
            }
            }
            compress_codec=none
        }




}
transform {
}
sink {
       Console{}
}