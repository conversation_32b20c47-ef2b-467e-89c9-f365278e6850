env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
Elasticsearch {
result_table_name = "_source_1"
hosts = ["**************:9200"]
index = ""
username = "elastic"
password = "changeme"
scroll_size = "100"
schema = {"fields":{"date_1":"DATE","date_time":"TIMESTAMP","name":"VARCHAR","id":"INT"}}
}




}
transform {
}
sink {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
database="zsp_test"
table="es"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="_source_1"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}
}