env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
query="select `id`,`name`,`date_1`,`date_time` from `starrocks` Where 1=1 "
"batch_size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="starrocks_source_1"
"parallelism"=1
}



}
transform {
FieldMapper{
source_table_name="starrocks_source_1"
result_table_name="starrocks_source_1_trans_2"
field_mapper={
id=id
name=name
date_1=date_1
date_time=date_time
}
}
}
sink {
OssFile {
path="/zsp0710"
tmp_path="/tmp/seatunnel"
bucket="oss://joyadata"
access_key="LTAI5tPWRfPWF18QECwdDJRZ"
access_secret="******************************"
endpoint="http://cn-beijing.oss.aliyuncs.com"
source_table_name="starrocks_source_1_trans_2"
custom_filename="false"
filename_time_format="yyyy.MM.dd"
file_format_type="text"
field_delimiter=","
row_delimiter="\n"
sink_columns=["name","date_1","id","date_time"]
have_partition="false"
is_enable_transaction="true"
batch_size="1000000"
compress_codec="none"

}
}