 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select id,name,age,birthdate,hire_date,last_modified,salary,is_active,bio,image,email,departments,notes,tinyint_field,smallint_field,mediumint_field,int_field,bigint_field,float_field,double_field,decimal_field,numeric_field,real_field,char_field,varchar_field,binary_field,varbinary_field,text_field,tinytext_field,mediumtext_field,longtext_field,blob_field,tinyblob_field,mediumblob_field,longblob_field from zsp_create where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="zsp_create_source"
"pre_sql"=[""]
"post_sql"=[""]
"parallelism"=1
}
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select id,name,date_1,data_time from ck where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="ck_source"
"pre_sql"=[""]
"post_sql"=[""]
"parallelism"=1
}



}
transform {
FieldMapper{
source_table_name="zsp_create_source"
result_table_name="zsp_create_source_trans_1"
field_mapper={
id=id
name=name
age=age
birthdate=birthdate
hire_date=hire_date
last_modified=last_modified
salary=salary
is_active=is_active
bio=bio
image=image
email=email
departments=departments
notes=notes
tinyint_field=tinyint_field
smallint_field=smallint_field
mediumint_field=mediumint_field
int_field=int_field
bigint_field=bigint_field
float_field=float_field
double_field=double_field
decimal_field=decimal_field
numeric_field=numeric_field
real_field=real_field
char_field=char_field
varchar_field=varchar_field
binary_field=binary_field
varbinary_field=varbinary_field
text_field=text_field
tinytext_field=tinytext_field
mediumtext_field=mediumtext_field
longtext_field=longtext_field
blob_field=blob_field
tinyblob_field=tinyblob_field
mediumblob_field=mediumblob_field
longblob_field=longblob_field
}
}
FieldMapper{
source_table_name="ck_source"
result_table_name="ck_source_trans_2"
field_mapper={
id=id
name=name
date_1=date_1
data_time=data_time
}
}
}
sink {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
database="zsp_test"
table="0816_1_zsp_create"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="zsp_create_source_trans_1"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="continue"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
database="zsp_test"
table="0816_1_ck"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="ck_source_trans_2"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="continue"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}

}