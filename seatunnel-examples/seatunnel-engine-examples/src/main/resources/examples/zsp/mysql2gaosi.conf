 env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select emp_id,emp_name,gender,account,org_id,birth_date,age,nationality,province,city,email,phone,begin_date,remark,create_time,update_time from emp Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="emp_source_1"
            "parallelism"=1
        }
}
transform {
        FieldMapper{
            source_table_name="emp_source_1"
            result_table_name="emp_source_1_trans_2"
                    field_mapper={
                        gender=gender
                        create_time=create_time
                        city=city
                        birth_date=birth_date
                        begin_date=begin_date
                        emp_name=emp_name
                        remark=remark
                        update_time=update_time
                        nationality=nationality
                        province=province
                        phone=phone
                        org_id=org_id
                        account=account
                        age=age
                        email=email
                        emp_id=emp_id
                    }
        }
}
sink {
        Jdbc {
        url="*****************************************"
        driver="org.postgresql.Driver"
            user="gaussdb"
            password="Cdyanfa@123456"
        database="sl"
        table="public.emp_zsp"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="emp_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["emp_id"]
            "field_ide"=LOWERCASE
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}