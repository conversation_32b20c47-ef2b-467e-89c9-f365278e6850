env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
query="select `id`,`name`,`age`,`birthdate`,`hire_date`,`last_modified`,`is_active`,`bio`,`image`,`email`,`departments`,`notes`,`tinyint_field`,`smallint_field`,`mediumint_field`,`int_field`,`bigint_field`,`float_field`,`double_field`,`numeric_field`,`real_field`,`char_field`,`varchar_field`,`binary_field`,`varbinary_field`,`text_field`,`tinytext_field`,`mediumtext_field`,`longtext_field`,`blob_field`,`tinyblob_field`,`mediumblob_field`,`longblob_field` from `zsp_test`.`zsp_create` Where 1=1 "
"batch_size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"parallelism"=1
}



}
transform {
}
sink {
kudu {
kudu_masters="192.168.90.115:7051"
table_name="zsp_create0724_1"
save_mode="overwrite"
batch_size="1024"
source_table_name = "E000001_source_1"
}

}