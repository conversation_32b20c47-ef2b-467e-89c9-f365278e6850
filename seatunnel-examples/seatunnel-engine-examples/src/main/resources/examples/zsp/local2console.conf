env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
LocalFile {
path="/opt/zsp/DS_VI_MD_FX_SPOT_CCYPAIR.txt"
file_format_type="text"
field_delimiter=","
parse_partition_from_path=true
date_format="yyyy-MM-dd"
datetime_format="yyyy-MM-dd HH:mm:ss"
time_format="HH:mm:ss"
schema= {
fields {
CURR_PAIR_REF=STRING
MID_PRICE=DOUBLE
FIRST_CURR_MX=STRING
SCND_CURR_MX=STRING
BATCH_DT=DATE
INDIRECT_MID_PRICE=DOUBLE
DISC_MID_RPICE=DOUBLE
INDIRECT_DISC_MID_PRICE=DOUBLE
}
}
}
}
transform {
}
sink {
Console{}
}