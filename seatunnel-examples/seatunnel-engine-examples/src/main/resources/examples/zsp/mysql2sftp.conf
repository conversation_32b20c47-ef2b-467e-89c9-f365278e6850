env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
      Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name from zsp_test"
        }
}
transform {
}
sink {
    SftpFile {
    host = "**************"
    port = 22
    user = "root"
    password = "joyadata"
    path = "/opt/module/zspTest"
    tmp_path = "/data/sftp/seatunnel/tmp"
    file_format_type = "text"
    field_delimiter = "\t"
    row_delimiter = "\n"
}
}
