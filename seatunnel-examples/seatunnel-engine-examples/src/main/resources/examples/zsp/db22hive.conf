 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="*************************************"
driver="com.ibm.db2.jcc.DB2Driver"
user="db2inst1"
password="Cdyanfa_123456"
query="select \"ID\",\"NAME\" from \"TEST \".\"TEST0904\" Where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"table.path"=""TEST "."TEST0904""
"parallelism"=1
}



}
transform {
}
sink {
Jdbc {
url="*****************************************"
driver="org.apache.hive.jdbc.HiveDriver"
database="default"
table="DB2_hive"
"connection_check_timeout_sec"="30"
"batch_size"="1024"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="0"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="stop"
field_ide="LOWERCASE"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}

}
