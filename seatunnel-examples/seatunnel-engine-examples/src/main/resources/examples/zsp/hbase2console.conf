env {
"job.mode"="BATCH"
"job.name"="10"
}
source {


Hbase {
result_table_name = "E000001_source_1"
zookeeper_quorum = "192.168.90.113:2181,192.168.90.114:2181,192.168.90.115:2181"
table = "zsp_test0927_1"
query_columns= ["rowkey","cf1:emp_name","cf1:gender","cf2:account","cf2:org_id","cf3:age","cf3:city"]
schema={
columns= [
{name="rowkey",type="String"},
{name="cf1:emp_name",type="STRING"},
{name="cf1:gender",type="STRING"},
{name="cf2:account",type="STRING"},
{name="cf2:org_id",type="STRING"},
{name="cf3:age",type="STRING"},
{name="cf3:city",type="STRING"}
]
}
file_path= "/dsg/app/public/dolphinscheduler/worker-server/conf/../../tmp_file/hive_file/54ba9f8219524494ba2d720af6facb12/"
}

}
transform {
FieldMapper{
source_table_name="E000001_source_1"
result_table_name="E000001_source_1_trans_2"
field_mapper={
"rowkey"=rowkey
"cf1:emp_name"=emp_name
"cf1:gender"=gender
"cf2:account"=account
"cf2:org_id"=org_id
"cf3:age"=age
"cf3:city"=city
}
}
}
sink {
Jdbc {
url="**************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
database="zsp_test"
table="emp_104_1"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="E000001_source_1_trans_2"
"generate_sink_sql"="true"
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}

}