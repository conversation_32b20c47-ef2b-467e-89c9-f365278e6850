env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="**************************************"
        driver="com.sap.db.jdbc.Driver"
        user="SYSTEM"
        password=Cdyanfa_123456
        query="select ID,NAME,ADDRESS from \"SYSTEM\".ZSP_EMP Where 1=1  and ID >'0' and ID <='2'"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"=""SYSTEM".ZSP_EMP_source_1"
            "partition_column"="ID"
            "partition_num"="5"
            "parallelism"=1
        }
}
transform {
}
sink {
        Jdbc {
        url="*******************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password=joyadata
        database="zsp_test"
        table="sap_hana"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"=""SYSTEM".ZSP_EMP_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}