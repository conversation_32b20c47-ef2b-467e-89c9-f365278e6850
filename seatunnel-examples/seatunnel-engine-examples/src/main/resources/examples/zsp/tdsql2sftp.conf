env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**********************************************************************************************************************************************************************************"
driver="com.mysql.jdbc.Driver"
user="dsg"
password="Dsgdata@123"
query="select `id`,`name`,`data_dwbm` from `test0902` Where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"table.path"="`test0902`"
}

}
transform {
}
sink {
SftpFile {
host="**************"
port="60022"
user="dsg"
password="Cdyanfa_123456"
path="/zsp/test"
tmp_path="/home/<USER>/zsp/tmp/seatunnel"
custom_filename=true
file_name_expression="test0902"
is_enable_transaction=false
filename_time_format="yyyy.MM.dd"
file_format_type="text"
field_delimiter="\u0001"
row_delimiter="\n"
have_partition="false"
sink_columns=["id","name","data_dwbm"]
batch_size="1000000"
compress_codec="none"
}

}
