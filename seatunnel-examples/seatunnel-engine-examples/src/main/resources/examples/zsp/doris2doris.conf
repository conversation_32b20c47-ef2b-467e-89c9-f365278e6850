env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  Doris {
      fenodes = "192.168.90.221:8030"
      username = root
      password = ""
      database = "zsp_test"
      table = "emp_user"
  }
}
transform {
}
sink {
  Doris {
    fenodes = "192.168.90.221:8030"
    username = root
    password = ""
    database = "zsp_test"
    table = "emp_user1"
    sink.label-prefix = "prefixbiaoqian"
    doris.config {
      format = "json"
      read_json_by_line = "true"
    }
  }
}
