env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/module/backend/test/zsp/test.txt"
    file_format_type = "text"
    field_delimiter=","
  }
}
transform {
}
sink {
    SftpFile {
    host = "**************"
    port = 22
    user = "root"
    password = "joyadata"
    path = "/opt/module/zspTest"
    tmp_path = "/data/sftp/seatunnel/tmp"
    file_format_type = "text"
}
}
