env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**********************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`age`,`city` from `seatunnel_source`.`emp_quality` Where 1=1  limit 10"
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"parallelism"=1
}



}
transform {
}
sink {

Hbase {
zookeeper_quorum = "192.168.90.113:2181"
table = "zsp_test0927_1"
rowkey_column= ["emp_id"]
rowkey_delimiter= ","
family_name {
"emp_name"="cf1"
"gender"="cf1"
"account"="cf2"
"org_id"="cf2"
"age"="cf3"
"city"="cf3"
}
file_path= "/dsg/app/public/dolphinscheduler/worker-server/conf/../../tmp_file/hive_file/54ba9f8219524494ba2d720af6facb12/"
}
}
