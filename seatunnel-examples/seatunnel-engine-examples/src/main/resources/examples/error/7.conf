env {
	"job.mode" = "BATCH"
	"job.name" = "1"
	"execution.parallelism" = "1"
	"checkpoint.interval" = "1000"
}
source {
	LocalFile {
		path = "/tmp/DS_FX_REVAL.txt"
		result_table_name = "localfile_source_1"
		file_format_type = "text"
		field_delimiter = ","
		parse_partition_from_path = true
		date_format = "yy-MM-dd"
		datetime_format = "yy-MM-dd HH:mm:ss"
		schema = {
			fields {
				BATCH_DT = STRING
				FST_CURR_AMT = STRING
				FST_CURR_CD = STRING
				SECD_CURR_AMT = STRING
				SECD_CURR_CD = STRING
				INSTR_CD = STRING
				MD_SPOT_VAL = STRING
				MUREX_INTRNL_TRAN_ID = STRING
				MUREX_IDNT_CNTR = STRING
				MUREX_INTRNL_TMSP = FLOAT
				MUREX_JOB_REF = STRING
				MUREX_DTS_REF = STRING
				AS_OF_DT = STRING
				AS_OF_DT_YYYYMM = STRING
				MUREX_PORT_CD = STRING
				NPV_PLCCY_AMT = STRING
				NPV_CCY_CD = STRING
			}
		}
		compress_codec = none
	}
	LocalFile {
		path = "/tmp/DS_LV1_PL_DETAILS.txt"
		result_table_name = "localfile_source_2"
		file_format_type = "text"
		field_delimiter = ","
		parse_partition_from_path = true
		date_format = "yyyy-MM-dd"
		datetime_format = "yyyy-MM-dd HH:mm:ss"
		schema = {
			fields {
				PL_CURR_CD = STRING
				MUREX_INTRNL_TRAN_ID = STRING
				MUREX_PORT_CD = STRING
				MUREX_DTS_REF = STRING
				BATCH_DT = STRING
			}
		}
		compress_codec = none
	}


}
transform {
	InnerJoin {
		source_table_name = "localfile_source_2"
		result_table_name = "localfile_source_2_trans_7"
		fields = ["PL_CURR_CD"]
		primary_key = "BATCH_DT"
		join_keys = {
			master.MUREX_DTS_REF = slave0.MUREX_DTS_REF
			master.MUREX_INTRNL_TRAN_ID = slave0.MUREX_INTRNL_TRAN_ID
			master.BATCH_DT = slave0.BATCH_DT
			master.MUREX_PORT_CD = slave0.MUREX_PORT_CD
		}
		join_state = "slave0"
		table_id = "14231148579968_lihj"
	}
	InnerJoin {
		source_table_name = "localfile_source_1"
		result_table_name = "localfile_source_1_trans_8"
		fields = ["MUREX_INTRNL_TRAN_ID", "MUREX_PORT_CD", "MUREX_DTS_REF", "BATCH_DT", "FST_CURR_AMT", "FST_CURR_CD", "SECD_CURR_AMT", "SECD_CURR_CD", "INSTR_CD", "MD_SPOT_VAL", "MUREX_IDNT_CNTR", "MUREX_INTRNL_TMSP", "MUREX_JOB_REF", "AS_OF_DT", "AS_OF_DT_YYYYMM", "NPV_PLCCY_AMT", "NPV_CCY_CD"]
		join_keys = {
			slave0.MUREX_INTRNL_TRAN_ID = master.MUREX_INTRNL_TRAN_ID
			slave0.MUREX_DTS_REF = master.MUREX_DTS_REF
			slave0.BATCH_DT = master.BATCH_DT
			slave0.MUREX_PORT_CD = master.MUREX_PORT_CD
		}
		join_state = "master"
		table_id = "14231148579968_lihj"
		master_fields = ["MUREX_INTRNL_TRAN_ID", "MUREX_PORT_CD", "MUREX_DTS_REF", "BATCH_DT", "FST_CURR_AMT", "FST_CURR_CD", "SECD_CURR_AMT", "SECD_CURR_CD", "INSTR_CD", "MD_SPOT_VAL", "MUREX_IDNT_CNTR", "MUREX_INTRNL_TMSP", "MUREX_JOB_REF", "AS_OF_DT", "AS_OF_DT_YYYYMM", "NPV_PLCCY_AMT", "NPV_CCY_CD", "PL_CURR_CD"]
		add_fields = ["slave0.PL_CURR_CD"]
	}
	FieldMapper {
		source_table_name = "localfile_source_1_trans_8"
		result_table_name = "localfile_source_1_trans_8_trans_10"
		field_mapper = {
			PL_CURR_CD = PL_CURR_CD
			MUREX_INTRNL_TRAN_ID = MUREX_INTRNL_TRAN_ID
			MUREX_PORT_CD = MUREX_PORT_CD
			MUREX_DTS_REF = MUREX_DTS_REF
			BATCH_DT = BATCH_DT
			FST_CURR_AMT = FST_CURR_AMT
			FST_CURR_CD = FST_CURR_CD
			SECD_CURR_AMT = SECD_CURR_AMT
			SECD_CURR_CD = SECD_CURR_CD
			INSTR_CD = INSTR_CD
			MD_SPOT_VAL = MD_SPOT_VAL
			MUREX_IDNT_CNTR = MUREX_IDNT_CNTR
			MUREX_INTRNL_TMSP = MUREX_INTRNL_TMSP
			MUREX_JOB_REF = MUREX_JOB_REF
			AS_OF_DT = AS_OF_DT
			AS_OF_DT_YYYYMM = AS_OF_DT_YYYYMM
			NPV_PLCCY_AMT = NPV_PLCCY_AMT
			NPV_CCY_CD = NPV_CCY_CD
		}
	}
}
sink {
	MergeLocalFile {
		final_name = "/tmp/out/test1.txt"
		path = "/tmp/out/"
		source_table_name = "localfile_source_1_trans_8_trans_10"
		tmp_path = "/tmp/tmp"
		file_format_type = "text"
		sink_columns = ["PL_CURR_CD", "MUREX_INTRNL_TRAN_ID", "MUREX_PORT_CD", "MUREX_DTS_REF", "BATCH_DT", "FST_CURR_AMT", "FST_CURR_CD", "SECD_CURR_AMT", "SECD_CURR_CD", "INSTR_CD", "MD_SPOT_VAL", "MUREX_IDNT_CNTR", "MUREX_INTRNL_TMSP", "MUREX_JOB_REF", "AS_OF_DT", "AS_OF_DT_YYYYMM", "NPV_PLCCY_AMT", "NPV_CCY_CD"]
		batch_size = "1000"
		compress_codec = "none"
	}

	ConsoleHole {
		source_table_name = "localfile_source_2_trans_7"
	}
}