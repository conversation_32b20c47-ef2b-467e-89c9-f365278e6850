env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="*************************************************************************************************"
        driver="com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user="sa"
        password="Cdyanfa@123456"
        query="select id,inv_pro_id,ztze,zjaf,zzcf,tzwcbq,tzwcbn,tzwckl,tzwcbl,wcjafbq,wcjafbn,wcjafkl,wcjafbl,zcjafbq,zcjafbn,zcjafkl,zcjafbl,wczcfbq,wczcfbn,wczcfkl,wczcfbl,zczcfbq,zczcfbn,zczcfkl,zczcfbl,period_type,period_value,org,currency,sort,create_by,create_org,update_by,update_org,remarks,del_flag,ygjjtjaf,xmztwcjlbq,xmztwcjlbn,xmztwcjlkl,xmztwcjlbl,ygjjtwcjafbq,ygjjtwcjafbn,ygjjtwcjafkl,ygjjtwcjafbl,ygjjtwcjlbq,ygjjtwcjlbn,ygjjtwcjlkl,ygjjtwcjlbl,ygjjtzcjafbq,ygjjtzcjafbn,ygjjtzcjafkl,ygjjtzcjafbl,bz,yesOrNoJGC,wcjafbqJGC,wcjafbnJGC,wcjafklJGC,wcjafblJGC,xmztwcjlbqJGC,xmztwcjlbnJGC,xmztwcjlklJGC,xmztwcjlblJGC,zcjafbqJGC,zcjafbnJGC,zcjafklJGC,zcjafblJGC,ygjwcbqJGC,ygjwcbnJGC,ygjwcklJGC,ygjwcblJGC,ygjjlbqJGC,ygjjlbnJGC,ygjjlklJGC,ygjjlblJGC,'2024-11-11' as etl_part from dbo.pro_inv_xmtzqk where 1=1 "
        "batch_size"="1000"
        "split.size"="1000"
        "result_table_name"="pro_inv_xmtzqk_source"
        "parallelism"=1
    }
}
transform {

}
sink {
    Doris {
    fenodes="192.168.90.221:8030"
    query-port="9030"
    username="root"
    password=""
    database="ykw"
    table="stg_cfhec_pro_inv_xmtzqk_f_d"
    table.identifier="ykw.stg_cfhec_pro_inv_xmtzqk_f_d"
    source_table_name="pro_inv_xmtzqk_source_trans_1"
    sink.enable-2pc="false"
    data_save_mode="APPEND_DATA"
    doris.config={
    format=json
    read_json_by_line=true
    }
    }

}