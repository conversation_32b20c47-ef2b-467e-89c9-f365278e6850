 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="********************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select `id`,`name`,`date_1`,`date_time` from `zsp_test`.`greenplum` Where 1=1 "
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"parallelism"=1
"empty_data_strategy"=false
}



}
transform {
}
sink {
    Jdbc {
        url="***********************************************"
        driver="org.postgresql.Driver"
        user="gp"
        password="gpadmin"
        database="postgres"
        table="public.dai_test01"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
        "field_ide"=LOWERCASE
        "enable_upsert"="false"
        "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        "use_copy_statement"="true"
        "empty_data_strategy"=false
    }

}