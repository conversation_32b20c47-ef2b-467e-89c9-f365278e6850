env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
        LocalFile {
        path="D://tmp//aa.txt"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss.SSS"
            schema= {
            fields {
                id=time
                id1=time
            }
            }
        }
}
transform {

}
sink {
Jdbc {
        url="**********************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="test"
        table="time_test1"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "batch_size"="1024"
         "is_exactly_once"="false"
         "transaction_timeout_sec"="-1"
         "auto_commit"="true"
         "support_upsert_by_query_primary_key_exist"="false"
         "source_table_name"="localfile_source_1"
         "generate_sink_sql"="true"
         "enable_upsert"="false"
         "pk_strategy"="stop"
         schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}