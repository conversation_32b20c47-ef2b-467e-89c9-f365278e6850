env {
    "job.mode"="BATCH"
    "job.name"="1"
    "execution.parallelism"="1"
    "checkpoint.interval"="1000"
}
source {
    LocalFile {
    path="/tmp/DS_FX_REVAL.txt"
    result_table_name = "localfile_source_1"
    file_format_type="text"
    field_delimiter=","
    parse_partition_from_path=true
    date_format="yyyy-MM-dd"
    datetime_format="yyyy-MM-dd HH:mm:ss"
    time_format="HH:mm:ss"
        schema= {
            fields {
            BATCH_DT=DATE
            FST_CURR_AMT=DOUBLE
            FST_CURR_CD=STRING
            SECD_CURR_AMT=DOUBLE
            SECD_CURR_CD=STRING
            INSTR_CD=STRING
            MD_SPOT_VAL=DOUBLE
            MUREX_INTRNL_TRAN_ID=DOUBLE
            MUREX_IDNT_CNTR=DOUBLE
            MUREX_INTRNL_TMSP=FLOAT
            MUREX_JOB_REF=DOUBLE
            MUREX_DTS_REF=DOUBLE
            AS_OF_DT=DATE
            AS_OF_DT_YYYYMM=DOUBLE
            MUREX_PORT_CD=STRING
            NPV_PLCCY_AMT=DOUBLE
            NPV_CCY_CD=STRING
            }
        }
    compress_codec=none
    }
LocalFile {
    path="/tmp/DS_VI_MD_FX_SPOT.txt"
    result_table_name = "localfile_source_2"
    file_format_type="text"
    field_delimiter=","
    parse_partition_from_path=true
    date_format="yyyy-MM-dd"
    datetime_format="yyyy-MM-dd HH:mm:ss"
    time_format="HH:mm:ss"
    schema= {
        fields {
            CURR_PAIR_REF=STRING
            MID_PRICE=DOUBLE
            FIRST_CURR_MX=STRING
            SCND_CURR_MX=STRING
            INDIRECT_MID_PRICE=DOUBLE
            DISC_MID_RPICE=DOUBLE
            INDIRECT_DISC_MID_PRICE=DOUBLE
            CLOSE_MID_PRICE=DOUBLE
            INDIRECT_CLOSE_MID_PRICE=DOUBLE
        }
    }
    compress_codec=none
}
LocalFile {
    path="/tmp/DS_LV1_PL_DETAILS.txt"
    result_table_name = "localfile_source_3"
    file_format_type="text"
    field_delimiter=","
    parse_partition_from_path=true
    date_format="yyyy-MM-dd"
    datetime_format="yyyy-MM-dd HH:mm:ss"
    time_format="HH:mm:ss"
    schema= {
        fields {
        PL_CURR_CD=STRING
        MUREX_INTRNL_TRAN_ID=DOUBLE
        MUREX_PORT_CD=STRING
        MUREX_DTS_REF=DOUBLE
        BATCH_DT=DATE
        }
    }
    compress_codec=none
}
LocalFile {
    path="/tmp/DS_VI_MD_FX_SPOT_CCYPAIR.txt"
    result_table_name = "localfile_source_4"
    file_format_type="text"
    field_delimiter=","
    parse_partition_from_path=true
    date_format="yyyy-MM-dd"
    datetime_format="yyyy-MM-dd HH:mm:ss"
    time_format="HH:mm:ss"
    schema= {
        fields {
            CURR_PAIR_REF=STRING
            MID_PRICE=DOUBLE
            FIRST_CURR_MX=STRING
            SCND_CURR_MX=STRING
            BATCH_DT=DATE
            INDIRECT_MID_PRICE=DOUBLE
            DISC_MID_RPICE=DOUBLE
            INDIRECT_DISC_MID_PRICE=DOUBLE
        }
    }
    compress_codec=none
}



}
transform {
    FieldMapper{
    source_table_name="localfile_source_1"
    result_table_name="localfile_source_1_trans_5"
		field_mapper={
			BATCH_DT=BATCH_DT
			FST_CURR_AMT=FST_CURR_AMT
			FST_CURR_CD=FST_CURR_CD
			SECD_CURR_AMT=SECD_CURR_AMT
			SECD_CURR_CD=SECD_CURR_CD
			INSTR_CD=INSTR_CD
			MD_SPOT_VAL=MD_SPOT_VAL
			MUREX_INTRNL_TRAN_ID=MUREX_INTRNL_TRAN_ID
			MUREX_IDNT_CNTR=MUREX_IDNT_CNTR
			MUREX_INTRNL_TMSP=MUREX_INTRNL_TMSP
			MUREX_JOB_REF=MUREX_JOB_REF
			MUREX_DTS_REF=MUREX_DTS_REF
			AS_OF_DT=AS_OF_DT
			AS_OF_DT_YYYYMM=AS_OF_DT_YYYYMM
			MUREX_PORT_CD=MUREX_PORT_CD
			NPV_PLCCY_AMT=NPV_PLCCY_AMT
			NPV_CCY_CD=NPV_CCY_CD
		}
    }
FieldMapper{
    source_table_name="localfile_source_3"
    result_table_name="localfile_source_3_trans_27"
    field_mapper={
        PL_CURR_CD=PL_CURR_CD
        MUREX_INTRNL_TRAN_ID=MUREX_INTRNL_TRAN_ID
        MUREX_PORT_CD=MUREX_PORT_CD
        MUREX_DTS_REF=MUREX_DTS_REF
        BATCH_DT=BATCH_DT
    }
}
FieldMapper{
    source_table_name="localfile_source_4"
    result_table_name="localfile_source_4_trans_31"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_4"
    result_table_name="localfile_source_4_trans_33"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_4"
    result_table_name="localfile_source_4_trans_35"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_4"
    result_table_name="localfile_source_4_trans_37"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_2"
    result_table_name="localfile_source_2_trans_39"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
        CLOSE_MID_PRICE=CLOSE_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_2"
    result_table_name="localfile_source_2_trans_41"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
        CLOSE_MID_PRICE=CLOSE_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_2"
    result_table_name="localfile_source_2_trans_43"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
        CLOSE_MID_PRICE=CLOSE_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
    }
}
FieldMapper{
    source_table_name="localfile_source_2"
    result_table_name="localfile_source_2_trans_45"
    field_mapper={
        CURR_PAIR_REF=CURR_PAIR_REF
        MID_PRICE=MID_PRICE
        FIRST_CURR_MX=FIRST_CURR_MX
        SCND_CURR_MX=SCND_CURR_MX
        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
        DISC_MID_RPICE=DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
        CLOSE_MID_PRICE=CLOSE_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
    }
}
InnerJoin{
    source_table_name="localfile_source_3_trans_27"
    result_table_name="null_trans_8"
    fields = ["PL_CURR_CD","BATCH_DT","MUREX_INTRNL_TRAN_ID","MUREX_PORT_CD","MUREX_DTS_REF"]
    primary_key = "BATCH_DT"
    join_keys={
        master.BATCH_DT=slave0.BATCH_DT
        master.MUREX_INTRNL_TRAN_ID=slave0.MUREX_INTRNL_TRAN_ID
        master.MUREX_PORT_CD=slave0.MUREX_PORT_CD
        master.MUREX_DTS_REF=slave0.MUREX_DTS_REF
    }
    mapping_keys={
    }
    join_state = "slave0"
    table_id = "14293807737216_two"
}
InnerJoin{
    source_table_name="localfile_source_1_trans_5"
    result_table_name="localfile_source_1_trans_5_trans_6"
    fields = ["BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD"]
    join_keys={
        slave0.BATCH_DT=master.BATCH_DT
        slave0.MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
        slave0.MUREX_PORT_CD=master.MUREX_PORT_CD
        slave0.MUREX_DTS_REF=master.MUREX_DTS_REF
    }
    mapping_keys={
        BATCH_DT=master.BATCH_DT
        FST_CURR_AMT=master.FST_CURR_AMT
        FST_CURR_CD=master.FST_CURR_CD
        SECD_CURR_AMT=master.SECD_CURR_AMT
        SECD_CURR_CD=master.SECD_CURR_CD
        INSTR_CD=master.INSTR_CD
        MD_SPOT_VAL=master.MD_SPOT_VAL
        MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
        MUREX_IDNT_CNTR=master.MUREX_IDNT_CNTR
        MUREX_INTRNL_TMSP=master.MUREX_INTRNL_TMSP
        MUREX_JOB_REF=master.MUREX_JOB_REF
        MUREX_DTS_REF=master.MUREX_DTS_REF
        AS_OF_DT=master.AS_OF_DT
        AS_OF_DT_YYYYMM=master.AS_OF_DT_YYYYMM
        MUREX_PORT_CD=master.MUREX_PORT_CD
        NPV_PLCCY_AMT=master.NPV_PLCCY_AMT
        NPV_CCY_CD=master.NPV_CCY_CD
        PL_CURR_CD=slave0.PL_CURR_CD
    }
    join_state = "master"
    table_id = "14293807737216_two"
    master_fields = ["BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","PL_CURR_CD"]
    add_fields = ["slave0.PL_CURR_CD"]
}
    SQL{
        source_table_name="localfile_source_1_trans_5_trans_6"
        result_table_name="localfile_source_1_trans_5_trans_6_trans_9"
        query="select PL_CURR_CD as PL_CURR_CD,BATCH_DT as BATCH_DT,FST_CURR_AMT as FST_CURR_AMT,TRIM(FST_CURR_CD) as FST_CURR_CD,SECD_CURR_AMT as SECD_CURR_AMT,TRIM(SECD_CURR_CD) as SECD_CURR_CD,INSTR_CD as INSTR_CD,MD_SPOT_VAL as MD_SPOT_VAL,MUREX_INTRNL_TRAN_ID as MUREX_INTRNL_TRAN_ID,MUREX_IDNT_CNTR as MUREX_IDNT_CNTR,MUREX_INTRNL_TMSP as MUREX_INTRNL_TMSP,MUREX_JOB_REF as MUREX_JOB_REF,MUREX_DTS_REF as MUREX_DTS_REF,AS_OF_DT as AS_OF_DT,AS_OF_DT_YYYYMM as AS_OF_DT_YYYYMM,MUREX_PORT_CD as MUREX_PORT_CD,NPV_PLCCY_AMT as NPV_PLCCY_AMT,NPV_CCY_CD as NPV_CCY_CD,'HKD' as D_CCY from localfile_source_1_trans_5_trans_6"
    }
LeftJoin{
    source_table_name="localfile_source_2_trans_41"
    result_table_name="null_trans_19"
    fields = ["INDIRECT_DISC_MID_PRICE","INDIRECT_CLOSE_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
    master.D_CCY=slave0.FIRST_CURR_MX
    master.PL_CURR_CD=slave0.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave0"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_2_trans_39"
    result_table_name="null_trans_20"
    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","CLOSE_MID_PRICE","FIRST_CURR_MX","FIRST_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.PL_CURR_CD=slave1.FIRST_CURR_MX
        master.D_CCY=slave1.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave1"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_4_trans_35"
    result_table_name="null_trans_21"
    fields = ["INDIRECT_DISC_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.PL_CURR_CD=slave2.FIRST_CURR_MX
        master.FST_CURR_CD=slave2.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave2"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_4_trans_31"
    result_table_name="null_trans_22"
    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.FST_CURR_CD=slave3.FIRST_CURR_MX
        master.PL_CURR_CD=slave3.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave3"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_4_trans_37"
    result_table_name="null_trans_23"
    fields = ["INDIRECT_DISC_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.PL_CURR_CD=slave4.FIRST_CURR_MX
        master.SECD_CURR_CD=slave4.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave4"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_4_trans_33"
    result_table_name="null_trans_24"
    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.SECD_CURR_CD=slave5.FIRST_CURR_MX
        master.PL_CURR_CD=slave5.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave5"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_2_trans_43"
    result_table_name="null_trans_25"
    fields = ["INDIRECT_DISC_MID_PRICE","INDIRECT_CLOSE_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.D_CCY=slave6.FIRST_CURR_MX
        master.SECD_CURR_CD=slave6.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave6"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_2_trans_45"
    result_table_name="null_trans_26"
    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","CLOSE_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
    primary_key = "SCND_CURR_MX"
    join_keys={
        master.SECD_CURR_CD=slave7.FIRST_CURR_MX
        master.D_CCY=slave7.SCND_CURR_MX
    }
    mapping_keys={
    }
    join_state = "slave7"
    table_id = "14293807754240_seven"
}
LeftJoin{
    source_table_name="localfile_source_1_trans_5_trans_6_trans_9"
    result_table_name="localfile_source_1_trans_5_trans_6_trans_9_trans_10"
    fields = ["PL_CURR_CD","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","D_CCY"]
    join_keys={
        slave0.FIRST_CURR_MX=master.D_CCY
        slave0.SCND_CURR_MX=master.PL_CURR_CD
        slave1.FIRST_CURR_MX=master.PL_CURR_CD
        slave1.SCND_CURR_MX=master.D_CCY
        slave2.FIRST_CURR_MX=master.PL_CURR_CD
        slave2.SCND_CURR_MX=master.FST_CURR_CD
        slave3.FIRST_CURR_MX=master.FST_CURR_CD
        slave3.SCND_CURR_MX=master.PL_CURR_CD
        slave4.FIRST_CURR_MX=master.PL_CURR_CD
        slave4.SCND_CURR_MX=master.SECD_CURR_CD
        slave5.FIRST_CURR_MX=master.SECD_CURR_CD
        slave5.SCND_CURR_MX=master.PL_CURR_CD
        slave6.FIRST_CURR_MX=master.D_CCY
        slave6.SCND_CURR_MX=master.SECD_CURR_CD
        slave7.FIRST_CURR_MX=master.SECD_CURR_CD
        slave7.SCND_CURR_MX=master.D_CCY
    }
    mapping_keys={
        PL_CURR_CD=master.PL_CURR_CD
        BATCH_DT=master.BATCH_DT
        FST_CURR_AMT=master.FST_CURR_AMT
        FST_CURR_CD=master.FST_CURR_CD
        SECD_CURR_AMT=master.SECD_CURR_AMT
        SECD_CURR_CD=master.SECD_CURR_CD
        INSTR_CD=master.INSTR_CD
        MD_SPOT_VAL=master.MD_SPOT_VAL
        MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
        MUREX_IDNT_CNTR=master.MUREX_IDNT_CNTR
        MUREX_INTRNL_TMSP=master.MUREX_INTRNL_TMSP
        MUREX_JOB_REF=master.MUREX_JOB_REF
        MUREX_DTS_REF=master.MUREX_DTS_REF
        AS_OF_DT=master.AS_OF_DT
        AS_OF_DT_YYYYMM=master.AS_OF_DT_YYYYMM
        MUREX_PORT_CD=master.MUREX_PORT_CD
        NPV_PLCCY_AMT=master.NPV_PLCCY_AMT
        NPV_CCY_CD=master.NPV_CCY_CD
        INDIRECT_DISC_MID_PRICE_1=slave0.INDIRECT_DISC_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE_1=slave0.INDIRECT_CLOSE_MID_PRICE
        CURR_PAIR_REF_1=slave1.CURR_PAIR_REF
        SCND_CURR_MX_1=slave1.SCND_CURR_MX
        DISC_MID_RPICE_1=slave1.DISC_MID_RPICE
        CLOSE_MID_PRICE_1=slave1.CLOSE_MID_PRICE
        INDIRECT_DISC_MID_PRICE_3=slave2.INDIRECT_DISC_MID_PRICE
        CURR_PAIR_REF_3=slave3.CURR_PAIR_REF
        SCND_CURR_MX_3=slave3.SCND_CURR_MX
        DISC_MID_RPICE_3=slave3.DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE_4=slave4.INDIRECT_DISC_MID_PRICE
        CURR_PAIR_REF_4=slave5.CURR_PAIR_REF
        SCND_CURR_MX_4=slave5.SCND_CURR_MX
        DISC_MID_RPICE_4=slave5.DISC_MID_RPICE
        INDIRECT_DISC_MID_PRICE_2=slave6.INDIRECT_DISC_MID_PRICE
        INDIRECT_CLOSE_MID_PRICE_2=slave6.INDIRECT_CLOSE_MID_PRICE
        CURR_PAIR_REF_2=slave7.CURR_PAIR_REF
        SCND_CURR_MX_2=slave7.SCND_CURR_MX
        DISC_MID_RPICE_2=slave7.DISC_MID_RPICE
        CLOSE_MID_PRICE_2=slave7.CLOSE_MID_PRICE
		D_CCY=master.D_CCY
    }
    join_state = "master"
    table_id = "14293807754240_seven"
    master_fields = ["PL_CURR_CD","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","D_CCY","INDIRECT_DISC_MID_PRICE_1","INDIRECT_CLOSE_MID_PRICE_1","CURR_PAIR_REF_1","SCND_CURR_MX_1","DISC_MID_RPICE_1","CLOSE_MID_PRICE_1","INDIRECT_DISC_MID_PRICE_3","CURR_PAIR_REF_3","SCND_CURR_MX_3","DISC_MID_RPICE_3","INDIRECT_DISC_MID_PRICE_4","CURR_PAIR_REF_4","SCND_CURR_MX_4","DISC_MID_RPICE_4","INDIRECT_DISC_MID_PRICE_2","INDIRECT_CLOSE_MID_PRICE_2","CURR_PAIR_REF_2","SCND_CURR_MX_2","DISC_MID_RPICE_2","CLOSE_MID_PRICE_2"]
    add_fields = ["slave0.INDIRECT_DISC_MID_PRICE","slave0.INDIRECT_CLOSE_MID_PRICE","slave1.CURR_PAIR_REF","slave1.SCND_CURR_MX","slave1.DISC_MID_RPICE","slave1.CLOSE_MID_PRICE","slave2.INDIRECT_DISC_MID_PRICE","slave3.CURR_PAIR_REF","slave3.SCND_CURR_MX","slave3.DISC_MID_RPICE","slave4.INDIRECT_DISC_MID_PRICE","slave5.CURR_PAIR_REF","slave5.SCND_CURR_MX","slave5.DISC_MID_RPICE","slave6.INDIRECT_DISC_MID_PRICE","slave6.INDIRECT_CLOSE_MID_PRICE","slave7.CURR_PAIR_REF","slave7.SCND_CURR_MX","slave7.DISC_MID_RPICE","slave7.CLOSE_MID_PRICE"]
}

}
sink {
    MergeLocalFile {
        final_name="/tmp/out/test_c_2_bj1.txt"
        path="/tmp/out"
        source_table_name = "localfile_source_1_trans_5_trans_6_trans_9_trans_10"
        tmp_path="/tmp/tmp"
        file_format_type="text"
        field_delimiter=","
        row_delimiter="\n"
        sink_columns=["CLOSE_MID_PRICE_1","CLOSE_MID_PRICE_2","INDIRECT_CLOSE_MID_PRICE_1","INDIRECT_CLOSE_MID_PRICE_2","DISC_MID_RPICE_1","DISC_MID_RPICE_2","DISC_MID_RPICE_3","DISC_MID_RPICE_4","INDIRECT_DISC_MID_PRICE_1","INDIRECT_DISC_MID_PRICE_2","INDIRECT_DISC_MID_PRICE_3","INDIRECT_DISC_MID_PRICE_4","CURR_PAIR_REF_1","CURR_PAIR_REF_2","CURR_PAIR_REF_3","CURR_PAIR_REF_4","SCND_CURR_MX_1","SCND_CURR_MX_2","SCND_CURR_MX_3","SCND_CURR_MX_4","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","PL_CURR_CD"]
        batch_size="1000"
        compress_codec="none"
    }

    ConsoleHole {
        source_table_name="null_trans_8"
    }
    ConsoleHole {
        source_table_name="null_trans_19"
    }
    ConsoleHole {
        source_table_name="null_trans_20"
    }
    ConsoleHole {
        source_table_name="null_trans_21"
    }
    ConsoleHole {
        source_table_name="null_trans_22"
    }
    ConsoleHole {
        source_table_name="null_trans_23"
    }
    ConsoleHole {
        source_table_name="null_trans_24"
    }
    ConsoleHole {
        source_table_name="null_trans_25"
    }
    ConsoleHole {
        source_table_name="null_trans_26"
    }
}