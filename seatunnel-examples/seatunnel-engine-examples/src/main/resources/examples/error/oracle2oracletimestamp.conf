env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="************************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="c##ZSP"
            password="ZSP"
        query="select \"ID\",\"DATE_TIME\",\"NAME\" from \"C##ZSP\".\"DAI_TEST_01\" Where 1=1  and DATE_TIME > TO_DATE('1900-01-01 01:01:01','yyyy-MM-dd hh24:mi:ss') and DATE_TIME <= TO_DATE('2023-04-01 12:00:00','yyyy-MM-dd hh24:mi:ss')"
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000002_source_1"
            "partition_column"="DATE_TIME"
            "partition_num"="5"
            "parallelism"=1
        }



}
transform {
}
sink {
        Jdbc {
        url="************************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="c##ZSP"
            password="ZSP"
        database="ORCLCDB"
        table="C##ZSP.DAI_TEST_02"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000002_source_1"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}
