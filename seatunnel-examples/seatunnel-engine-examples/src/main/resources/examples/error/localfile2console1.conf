env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from test.time_test"
        }
}
transform {

}
sink {
console{}
}