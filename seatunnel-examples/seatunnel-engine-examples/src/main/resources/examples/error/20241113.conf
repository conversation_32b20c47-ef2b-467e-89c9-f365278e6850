env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="*************************************************************************************************"
driver="com.microsoft.sqlserver.jdbc.SQLServerDriver"
user="sa"
password="Cdyanfa@123456"
query="select yesOrNoJGC,wcjafbqJGC,wcjafbnJGC,wcjafklJGC,wcjafblJGC,xmztwcjlbqJGC,xmztwcjlbnJGC,xmztwcjlklJGC,xmztwcjlblJGC,zcjafbqJGC,zcjafbnJGC,zcjafklJGC,zcjafblJGC,ygjwcbqJGC,ygjwcbnJGC,ygjwcklJGC,ygjwcblJGC,ygjjlbqJGC,ygjjlbnJGC,ygjj<PERSON>lJGC,ygjjlblJGC,'2024-11-11' as etl_part from dbo.pro_inv_xmtzqk where 1=1 "
"batch_size"="1000"
"split.size"="1000"
"result_table_name"="pro_inv_xmtzqk_source"
"parallelism"=1
}
}


transform {

}


sink {
 Jdbc {
        url="************************************"
        driver="com.mysql.jdbc.Driver"
        user="root"
        password=""
        database="ykw"
        table="stg_cfhec_pro_inv_xmtzqk_f_d"
        "batch_size"="1000"
        "support_upsert_by_query_primary_key_exist"="false"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
        }

}