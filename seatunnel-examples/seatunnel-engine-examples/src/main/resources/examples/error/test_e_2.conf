env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
        LocalFile {
        path="/tmp/DS_BANKCCY.txt"
            result_table_name = "localfile_source_1"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                CURR_CD=STRING
                BANK_CURR_CD=STRING
            }
            }
            compress_codec=none
        }
        LocalFile {
        path="/tmp/DS_FX_REVAL.txt"
            result_table_name = "localfile_source_2"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                BATCH_DT=DATE
                FST_CURR_AMT=DOUBLE
                FST_CURR_CD=STRING
                SECD_CURR_AMT=DOUBLE
                SECD_CURR_CD=STRING
                INSTR_CD=STRING
                MD_SPOT_VAL=DOUBLE
                MUREX_INTRNL_TRAN_ID=DOUBLE
                MUREX_IDNT_CNTR=DOUBLE
                MUREX_INTRNL_TMSP=FLOAT
                MUREX_JOB_REF=DOUBLE
                MUREX_DTS_REF=DOUBLE
                AS_OF_DT=DATE
                AS_OF_DT_YYYYMM=DOUBLE
                MUREX_PORT_CD=STRING
                NPV_PLCCY_AMT=DOUBLE
                NPV_CCY_CD=STRING
            }
            }
            compress_codec=none
        }
        LocalFile {
        path="/tmp/DS_VI_MD_FX_SPOT.txt"
            result_table_name = "localfile_source_3"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                CURR_PAIR_REF=STRING
                MID_PRICE=DOUBLE
                FIRST_CURR_MX=STRING
                SCND_CURR_MX=STRING
                INDIRECT_MID_PRICE=DOUBLE
                DISC_MID_RPICE=DOUBLE
                INDIRECT_DISC_MID_PRICE=DOUBLE
                CLOSE_MID_PRICE=DOUBLE
                INDIRECT_CLOSE_MID_PRICE=DOUBLE
            }
            }
            compress_codec=none
        }
        LocalFile {
        path="/tmp/DS_LV1_PL_DETAILS.txt"
            result_table_name = "localfile_source_4"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                PL_CURR_CD=STRING
                MUREX_INTRNL_TRAN_ID=DOUBLE
                MUREX_PORT_CD=STRING
                MUREX_DTS_REF=DOUBLE
                BATCH_DT=DATE
            }
            }
            compress_codec=none
        }
        LocalFile {
        path="/tmp/DS_VI_MD_FX_SPOT_CCYPAIR.txt"
            result_table_name = "localfile_source_5"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                CURR_PAIR_REF=STRING
                MID_PRICE=DOUBLE
                FIRST_CURR_MX=STRING
                SCND_CURR_MX=STRING
                BATCH_DT=DATE
                INDIRECT_MID_PRICE=DOUBLE
                DISC_MID_RPICE=DOUBLE
                INDIRECT_DISC_MID_PRICE=DOUBLE
            }
            }
            compress_codec=none
        }



}
transform {
FieldMapper{
            source_table_name="localfile_source_4"
            result_table_name="localfile_source_4_trans_34"
                    field_mapper={
                        PL_CURR_CD=PL_CURR_CD
                        MUREX_INTRNL_TRAN_ID=MUREX_INTRNL_TRAN_ID
                        MUREX_PORT_CD=MUREX_PORT_CD
                        MUREX_DTS_REF=MUREX_DTS_REF
                        BATCH_DT=BATCH_DT
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_5"
            result_table_name="localfile_source_5_trans_40"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_5"
            result_table_name="localfile_source_5_trans_44"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_5"
            result_table_name="localfile_source_5_trans_48"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_5"
            result_table_name="localfile_source_5_trans_52"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_1"
            result_table_name="localfile_source_1_trans_56"
                    field_mapper={
                        CURR_CD=MUREX_CURR_CD
                        BANK_CURR_CD=BANK_CURR_CD
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_1"
            result_table_name="localfile_source_1_trans_58"
                    field_mapper={
                        CURR_CD=MUREX_CURR_CD
                        BANK_CURR_CD=BANK_CURR_CD
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_3"
            result_table_name="localfile_source_3_trans_60"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                        CLOSE_MID_PRICE=CLOSE_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_3"
            result_table_name="localfile_source_3_trans_64"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                        CLOSE_MID_PRICE=CLOSE_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_3"
            result_table_name="localfile_source_3_trans_68"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                        CLOSE_MID_PRICE=CLOSE_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_3"
            result_table_name="localfile_source_3_trans_72"
                    field_mapper={
                        CURR_PAIR_REF=CURR_PAIR_REF
                        MID_PRICE=MID_PRICE
                        FIRST_CURR_MX=FIRST_CURR_MX
                        SCND_CURR_MX=SCND_CURR_MX
                        INDIRECT_MID_PRICE=INDIRECT_MID_PRICE
                        DISC_MID_RPICE=DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE=INDIRECT_DISC_MID_PRICE
                        CLOSE_MID_PRICE=CLOSE_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE=INDIRECT_CLOSE_MID_PRICE
                    }
        }
        FieldMapper{
            source_table_name="localfile_source_2"
            result_table_name="localfile_source_2_trans_6"
                    field_mapper={
                        BATCH_DT=BATCH_DT
                        FST_CURR_AMT=FST_CURR_AMT
                        FST_CURR_CD=FST_CURR_CD
                        SECD_CURR_AMT=SECD_CURR_AMT
                        SECD_CURR_CD=SECD_CURR_CD
                        INSTR_CD=INSTR_CD
                        MD_SPOT_VAL=MD_SPOT_VAL
                        MUREX_INTRNL_TRAN_ID=MUREX_INTRNL_TRAN_ID
                        MUREX_IDNT_CNTR=MUREX_IDNT_CNTR
                        MUREX_INTRNL_TMSP=MUREX_INTRNL_TMSP
                        MUREX_JOB_REF=MUREX_JOB_REF
                        MUREX_DTS_REF=MUREX_DTS_REF
                        AS_OF_DT=AS_OF_DT
                        AS_OF_DT_YYYYMM=AS_OF_DT_YYYYMM
                        MUREX_PORT_CD=MUREX_PORT_CD
                        NPV_PLCCY_AMT=NPV_PLCCY_AMT
                        NPV_CCY_CD=NPV_CCY_CD
                    }
        }
        InnerJoin{
            source_table_name="localfile_source_4_trans_34"
            result_table_name="null_trans_9"
                    fields = ["PL_CURR_CD","BATCH_DT","MUREX_INTRNL_TRAN_ID","MUREX_PORT_CD","MUREX_DTS_REF"]
                    primary_key = "BATCH_DT"
                    join_keys={
                        master.BATCH_DT=slave0_thr.BATCH_DT
                        master.MUREX_INTRNL_TRAN_ID=slave0_thr.MUREX_INTRNL_TRAN_ID
                        master.MUREX_PORT_CD=slave0_thr.MUREX_PORT_CD
                        master.MUREX_DTS_REF=slave0_thr.MUREX_DTS_REF
                    }
                    mapping_keys={
                    }
                    join_state = "slave0_thr"
                    table_id = "14296182959232_thr"
        }
        InnerJoin{
            source_table_name="localfile_source_2_trans_6"
            result_table_name="localfile_source_2_trans_6_trans_7"
                    fields = ["BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD"]
                    join_keys={
                        slave0_thr.BATCH_DT=master.BATCH_DT
                        slave0_thr.MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
                        slave0_thr.MUREX_PORT_CD=master.MUREX_PORT_CD
                        slave0_thr.MUREX_DTS_REF=master.MUREX_DTS_REF
                    }
                    mapping_keys={
                        BATCH_DT=master.BATCH_DT
                        FST_CURR_AMT=master.FST_CURR_AMT
                        FST_CURR_CD=master.FST_CURR_CD
                        SECD_CURR_AMT=master.SECD_CURR_AMT
                        SECD_CURR_CD=master.SECD_CURR_CD
                        INSTR_CD=master.INSTR_CD
                        MD_SPOT_VAL=master.MD_SPOT_VAL
                        MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
                        MUREX_IDNT_CNTR=master.MUREX_IDNT_CNTR
                        MUREX_INTRNL_TMSP=master.MUREX_INTRNL_TMSP
                        MUREX_JOB_REF=master.MUREX_JOB_REF
                        MUREX_DTS_REF=master.MUREX_DTS_REF
                        AS_OF_DT=master.AS_OF_DT
                        AS_OF_DT_YYYYMM=master.AS_OF_DT_YYYYMM
                        MUREX_PORT_CD=master.MUREX_PORT_CD
                        NPV_PLCCY_AMT=master.NPV_PLCCY_AMT
                        NPV_CCY_CD=master.NPV_CCY_CD
                        PL_CURR_CD=slave0_thr.PL_CURR_CD
                    }
                    join_state = "master"
                    table_id = "14296182959232_thr"
                    master_fields = ["BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","PL_CURR_CD"]
                    add_fields = ["slave0_thr.PL_CURR_CD"]
        }
        SQL{
            source_table_name="localfile_source_2_trans_6_trans_7"
            result_table_name="localfile_source_2_trans_6_trans_7_trans_10"
                    query="select PL_CURR_CD as PL_CURR_CD,BATCH_DT as BATCH_DT,FST_CURR_AMT as FST_CURR_AMT,TRIM(FST_CURR_CD) as FST_CURR_CD,SECD_CURR_AMT as SECD_CURR_AMT,TRIM(SECD_CURR_CD) as SECD_CURR_CD,INSTR_CD as INSTR_CD,MD_SPOT_VAL as MD_SPOT_VAL,MUREX_INTRNL_TRAN_ID as MUREX_INTRNL_TRAN_ID,MUREX_IDNT_CNTR as MUREX_IDNT_CNTR,MUREX_INTRNL_TMSP as MUREX_INTRNL_TMSP,MUREX_JOB_REF as MUREX_JOB_REF,MUREX_DTS_REF as MUREX_DTS_REF,AS_OF_DT as AS_OF_DT,AS_OF_DT_YYYYMM as AS_OF_DT_YYYYMM,MUREX_PORT_CD as MUREX_PORT_CD,NPV_PLCCY_AMT as NPV_PLCCY_AMT,NPV_CCY_CD as NPV_CCY_CD,'HKD' as D_CCY from localfile_source_2_trans_6_trans_7"
        }
        LeftJoin{
            source_table_name="localfile_source_3_trans_64"
            result_table_name="null_trans_20"
                    fields = ["INDIRECT_DISC_MID_PRICE","INDIRECT_CLOSE_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.D_CCY=slave0.FIRST_CURR_MX
                        master.PL_CURR_CD=slave0.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave0"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_3_trans_60"
            result_table_name="null_trans_21"
                    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","CLOSE_MID_PRICE","FIRST_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.PL_CURR_CD=slave1.FIRST_CURR_MX
                        master.D_CCY=slave1.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave1"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_5_trans_48"
            result_table_name="null_trans_22"
                    fields = ["INDIRECT_DISC_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.PL_CURR_CD=slave2.FIRST_CURR_MX
                        master.FST_CURR_CD=slave2.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave2"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_5_trans_40"
            result_table_name="null_trans_23"
                    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","FIRST_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.FST_CURR_CD=slave3.FIRST_CURR_MX
                        master.PL_CURR_CD=slave3.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave3"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_5_trans_52"
            result_table_name="null_trans_24"
                    fields = ["INDIRECT_DISC_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.PL_CURR_CD=slave4.FIRST_CURR_MX
                        master.SECD_CURR_CD=slave4.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave4"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_5_trans_44"
            result_table_name="null_trans_25"
                    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","FIRST_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.SECD_CURR_CD=slave5.FIRST_CURR_MX
                        master.PL_CURR_CD=slave5.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave5"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_3_trans_68"
            result_table_name="null_trans_26"
                    fields = ["INDIRECT_DISC_MID_PRICE","INDIRECT_CLOSE_MID_PRICE","FIRST_CURR_MX","SCND_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.D_CCY=slave6.FIRST_CURR_MX
                        master.SECD_CURR_CD=slave6.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave6"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_3_trans_72"
            result_table_name="null_trans_27"
                    fields = ["CURR_PAIR_REF","SCND_CURR_MX","DISC_MID_RPICE","CLOSE_MID_PRICE","FIRST_CURR_MX"]
                    primary_key = "SCND_CURR_MX"
                    join_keys={
                        master.SECD_CURR_CD=slave7.FIRST_CURR_MX
                        master.D_CCY=slave7.SCND_CURR_MX
                    }
                    mapping_keys={
                    }
                    join_state = "slave7"
                    table_id = "14296182977408_two"
        }
        LeftJoin{
            source_table_name="localfile_source_2_trans_6_trans_7_trans_10"
            result_table_name="localfile_source_2_trans_6_trans_7_trans_10_trans_11"
                    fields = ["PL_CURR_CD","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","D_CCY"]
                    join_keys={
                        slave0.FIRST_CURR_MX=master.D_CCY
                        slave0.SCND_CURR_MX=master.PL_CURR_CD
                        slave1.FIRST_CURR_MX=master.PL_CURR_CD
                        slave1.SCND_CURR_MX=master.D_CCY
                        slave2.FIRST_CURR_MX=master.PL_CURR_CD
                        slave2.SCND_CURR_MX=master.FST_CURR_CD
                        slave3.FIRST_CURR_MX=master.FST_CURR_CD
                        slave3.SCND_CURR_MX=master.PL_CURR_CD
                        slave4.FIRST_CURR_MX=master.PL_CURR_CD
                        slave4.SCND_CURR_MX=master.SECD_CURR_CD
                        slave5.FIRST_CURR_MX=master.SECD_CURR_CD
                        slave5.SCND_CURR_MX=master.PL_CURR_CD
                        slave6.FIRST_CURR_MX=master.D_CCY
                        slave6.SCND_CURR_MX=master.SECD_CURR_CD
                        slave7.FIRST_CURR_MX=master.SECD_CURR_CD
                        slave7.SCND_CURR_MX=master.D_CCY
                    }
                    mapping_keys={
                        PL_CURR_CD=master.PL_CURR_CD
                        BATCH_DT=master.BATCH_DT
                        FST_CURR_AMT=master.FST_CURR_AMT
                        FST_CURR_CD=master.FST_CURR_CD
                        SECD_CURR_AMT=master.SECD_CURR_AMT
                        SECD_CURR_CD=master.SECD_CURR_CD
                        INSTR_CD=master.INSTR_CD
                        MD_SPOT_VAL=master.MD_SPOT_VAL
                        MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
                        MUREX_IDNT_CNTR=master.MUREX_IDNT_CNTR
                        MUREX_INTRNL_TMSP=master.MUREX_INTRNL_TMSP
                        MUREX_JOB_REF=master.MUREX_JOB_REF
                        MUREX_DTS_REF=master.MUREX_DTS_REF
                        AS_OF_DT=master.AS_OF_DT
                        AS_OF_DT_YYYYMM=master.AS_OF_DT_YYYYMM
                        MUREX_PORT_CD=master.MUREX_PORT_CD
                        NPV_PLCCY_AMT=master.NPV_PLCCY_AMT
                        NPV_CCY_CD=master.NPV_CCY_CD
                        INDIRECT_DISC_MID_PRICE_1=slave0.INDIRECT_DISC_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE_1=slave0.INDIRECT_CLOSE_MID_PRICE
                        CURR_PAIR_REF_1=slave1.CURR_PAIR_REF
                        SCND_CURR_MX_1=slave1.SCND_CURR_MX
                        DISC_MID_RPICE_1=slave1.DISC_MID_RPICE
                        CLOSE_MID_PRICE_1=slave1.CLOSE_MID_PRICE
                        INDIRECT_DISC_MID_PRICE_3=slave2.INDIRECT_DISC_MID_PRICE
                        CURR_PAIR_REF_3=slave3.CURR_PAIR_REF
                        SCND_CURR_MX_3=slave3.SCND_CURR_MX
                        DISC_MID_RPICE_3=slave3.DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE_4=slave4.INDIRECT_DISC_MID_PRICE
                        CURR_PAIR_REF_4=slave5.CURR_PAIR_REF
                        SCND_CURR_MX_4=slave5.SCND_CURR_MX
                        DISC_MID_RPICE_4=slave5.DISC_MID_RPICE
                        INDIRECT_DISC_MID_PRICE_2=slave6.INDIRECT_DISC_MID_PRICE
                        INDIRECT_CLOSE_MID_PRICE_2=slave6.INDIRECT_CLOSE_MID_PRICE
                        CURR_PAIR_REF_2=slave7.CURR_PAIR_REF
                        SCND_CURR_MX_2=slave7.SCND_CURR_MX
                        DISC_MID_RPICE_2=slave7.DISC_MID_RPICE
                        CLOSE_MID_PRICE_2=slave7.CLOSE_MID_PRICE
                    }
                    join_state = "master"
                    table_id = "14296182977408_two"
                    master_fields = ["PL_CURR_CD","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","D_CCY","INDIRECT_DISC_MID_PRICE_1","INDIRECT_CLOSE_MID_PRICE_1","CURR_PAIR_REF_1","SCND_CURR_MX_1","DISC_MID_RPICE_1","CLOSE_MID_PRICE_1","INDIRECT_DISC_MID_PRICE_3","CURR_PAIR_REF_3","SCND_CURR_MX_3","DISC_MID_RPICE_3","INDIRECT_DISC_MID_PRICE_4","CURR_PAIR_REF_4","SCND_CURR_MX_4","DISC_MID_RPICE_4","INDIRECT_DISC_MID_PRICE_2","INDIRECT_CLOSE_MID_PRICE_2","CURR_PAIR_REF_2","SCND_CURR_MX_2","DISC_MID_RPICE_2","CLOSE_MID_PRICE_2"]
                    add_fields = ["slave0.INDIRECT_DISC_MID_PRICE","slave0.INDIRECT_CLOSE_MID_PRICE","slave1.CURR_PAIR_REF","slave1.SCND_CURR_MX","slave1.DISC_MID_RPICE","slave1.CLOSE_MID_PRICE","slave2.INDIRECT_DISC_MID_PRICE","slave3.CURR_PAIR_REF","slave3.SCND_CURR_MX","slave3.DISC_MID_RPICE","slave4.INDIRECT_DISC_MID_PRICE","slave5.CURR_PAIR_REF","slave5.SCND_CURR_MX","slave5.DISC_MID_RPICE","slave6.INDIRECT_DISC_MID_PRICE","slave6.INDIRECT_CLOSE_MID_PRICE","slave7.CURR_PAIR_REF","slave7.SCND_CURR_MX","slave7.DISC_MID_RPICE","slave7.CLOSE_MID_PRICE"]
        }
        SQL{
            source_table_name="localfile_source_2_trans_6_trans_7_trans_10_trans_11"
            result_table_name="localfile_source_2_trans_6_trans_7_trans_10_trans_11_trans_28"
                    query="select (CASE WHEN PL_CURR_CD = FST_CURR_CD THEN FST_CURR_AMT WHEN  SCND_CURR_MX_3 is null or LENGTH(SCND_CURR_MX_3) = 0 THEN FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 ELSE (CASE WHEN  SCND_CURR_MX_3 = PL_CURR_CD Then FST_CURR_AMT * DISC_MID_RPICE_3 ELSE FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 END ) END) as FstCurrAmtPL,PL_CURR_CD as PL_CURR_CD,DISC_MID_RPICE_3 as DISC_MID_RPICE_3,INDIRECT_DISC_MID_PRICE_3 as INDIRECT_DISC_MID_PRICE_3,CASE WHEN PL_CURR_CD = 'HKD' THEN (CASE WHEN  PL_CURR_CD = FST_CURR_CD THEN FST_CURR_AMT WHEN  SCND_CURR_MX_3 is null or LENGTH(SCND_CURR_MX_3) = 0 THEN FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 ELSE (CASE WHEN  SCND_CURR_MX_3 = PL_CURR_CD Then FST_CURR_AMT * DISC_MID_RPICE_3 ELSE FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 END ) END) WHEN  SCND_CURR_MX_1 is null or LENGTH(SCND_CURR_MX_1) = 0 THEN (CASE WHEN PL_CURR_CD = FST_CURR_CD THEN FST_CURR_AMT WHEN  SCND_CURR_MX_3 is null or LENGTH(SCND_CURR_MX_3) = 0 THEN FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 ELSE (CASE WHEN  SCND_CURR_MX_3 = PL_CURR_CD Then FST_CURR_AMT * DISC_MID_RPICE_3 ELSE FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 END ) END) * INDIRECT_DISC_MID_PRICE_1 ELSE (CASE WHEN SCND_CURR_MX_1 = 'HKD' Then (CASE WHEN PL_CURR_CD = FST_CURR_CD THEN FST_CURR_AMT WHEN  SCND_CURR_MX_3 is null or LENGTH(SCND_CURR_MX_3) = 0 THEN FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 ELSE (CASE WHEN  SCND_CURR_MX_3 = PL_CURR_CD Then FST_CURR_AMT * DISC_MID_RPICE_3 ELSE FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 END ) END) * DISC_MID_RPICE_1 ELSE (CASE WHEN PL_CURR_CD = FST_CURR_CD THEN FST_CURR_AMT WHEN  SCND_CURR_MX_3 is null or LENGTH(SCND_CURR_MX_3) = 0 THEN FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 ELSE (CASE WHEN  SCND_CURR_MX_3 = PL_CURR_CD Then FST_CURR_AMT * DISC_MID_RPICE_3 ELSE FST_CURR_AMT * INDIRECT_DISC_MID_PRICE_3 END ) END) * INDIRECT_DISC_MID_PRICE_1 END ) END as FST_CURR_AMT_HKE,CASE WHEN PL_CURR_CD = 'HKD' THEN (CASE WHEN  PL_CURR_CD = SECD_CURR_CD THEN SECD_CURR_AMT WHEN  SCND_CURR_MX_4 is null or LENGTH(SCND_CURR_MX_4) = 0 THEN SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 ELSE (CASE WHEN  SCND_CURR_MX_4 = PL_CURR_CD Then SECD_CURR_AMT * DISC_MID_RPICE_4 ELSE SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 END ) END) WHEN  SCND_CURR_MX_1 is null or LENGTH(SCND_CURR_MX_1) = 0 THEN (CASE WHEN PL_CURR_CD = SECD_CURR_CD THEN SECD_CURR_AMT WHEN  SCND_CURR_MX_4 is null or LENGTH(SCND_CURR_MX_4) = 0 THEN SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 ELSE (CASE WHEN  SCND_CURR_MX_4 = PL_CURR_CD Then SECD_CURR_AMT * DISC_MID_RPICE_4 ELSE SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 END ) END) * INDIRECT_DISC_MID_PRICE_2 ELSE (CASE WHEN SCND_CURR_MX_1 = 'HKD' Then (CASE WHEN PL_CURR_CD = SECD_CURR_CD THEN SECD_CURR_AMT WHEN  SCND_CURR_MX_4 is null or LENGTH(SCND_CURR_MX_4) = 0 THEN SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 ELSE (CASE WHEN  SCND_CURR_MX_4 = PL_CURR_CD Then SECD_CURR_AMT * DISC_MID_RPICE_4 ELSE SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 END ) END) * DISC_MID_RPICE_2 ELSE (CASE WHEN PL_CURR_CD = SECD_CURR_CD THEN SECD_CURR_AMT WHEN  SCND_CURR_MX_4 is null or LENGTH(SCND_CURR_MX_4) = 0 THEN SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 ELSE (CASE WHEN  SCND_CURR_MX_4 = PL_CURR_CD Then SECD_CURR_AMT * DISC_MID_RPICE_4 ELSE SECD_CURR_AMT * INDIRECT_DISC_MID_PRICE_4 END ) END) * INDIRECT_DISC_MID_PRICE_2 END ) END as SECD_CURR_AMT_HKE,BATCH_DT as BATCH_DT,FST_CURR_AMT as FST_CURR_AMT,FST_CURR_CD as FST_CURR_CD,SECD_CURR_AMT as SECD_CURR_AMT,SECD_CURR_CD as SECD_CURR_CD,INSTR_CD as INSTR_CD,MD_SPOT_VAL as MD_SPOT_VAL,MUREX_INTRNL_TRAN_ID as MUREX_INTRNL_TRAN_ID,MUREX_IDNT_CNTR as MUREX_IDNT_CNTR,MUREX_INTRNL_TMSP as MUREX_INTRNL_TMSP,MUREX_JOB_REF as MUREX_JOB_REF,MUREX_DTS_REF as MUREX_DTS_REF,AS_OF_DT as AS_OF_DT,AS_OF_DT_YYYYMM as AS_OF_DT_YYYYMM,MUREX_PORT_CD as MUREX_PORT_CD,NPV_PLCCY_AMT as NPV_PLCCY_AMT,NPV_CCY_CD as NPV_CCY_CD from localfile_source_2_trans_6_trans_7_trans_10_trans_11"
        }
        LeftJoin{
            source_table_name="localfile_source_1_trans_58"
            result_table_name="null_trans_32"
                    fields = ["BANK_CURR_CD","MUREX_CURR_CD"]
                    primary_key = "MUREX_CURR_CD"
                    join_keys={
                        master.SECD_CURR_CD=slave0.MUREX_CURR_CD
                    }
                    mapping_keys={
                    }
                    join_state = "slave0"
                    table_id = "14296183026432_one"
        }
        LeftJoin{
            source_table_name="localfile_source_1_trans_56"
            result_table_name="null_trans_33"
                    fields = ["MUREX_CURR_CD","BANK_CURR_CD"]
                    primary_key = "MUREX_CURR_CD"
                    join_keys={
                        master.FST_CURR_CD=slave1.MUREX_CURR_CD
                    }
                    mapping_keys={
                    }
                    join_state = "slave1"
                    table_id = "14296183026432_one"
        }
        LeftJoin{
            source_table_name="localfile_source_2_trans_6_trans_7_trans_10_trans_11_trans_28"
            result_table_name="localfile_source_2_trans_6_trans_7_trans_10_trans_11_trans_28_trans_29"
                    fields = ["FstCurrAmtPL","PL_CURR_CD","DISC_MID_RPICE_3","INDIRECT_DISC_MID_PRICE_3","FST_CURR_AMT_HKE","SECD_CURR_AMT_HKE","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD"]
                    join_keys={
                        slave0.MUREX_CURR_CD=master.SECD_CURR_CD
                        slave1.MUREX_CURR_CD=master.FST_CURR_CD
                    }
                    mapping_keys={
                        FstCurrAmtPL=master.FstCurrAmtPL
                        PL_CURR_CD=master.PL_CURR_CD
                        DISC_MID_RPICE_3=master.DISC_MID_RPICE_3
                        INDIRECT_DISC_MID_PRICE_3=master.INDIRECT_DISC_MID_PRICE_3
                        FST_CURR_AMT_HKE=master.FST_CURR_AMT_HKE
                        SECD_CURR_AMT_HKE=master.SECD_CURR_AMT_HKE
                        BATCH_DT=master.BATCH_DT
                        FST_CURR_AMT=master.FST_CURR_AMT
                        FST_CURR_CD=master.FST_CURR_CD
                        SECD_CURR_AMT=master.SECD_CURR_AMT
                        SECD_CURR_CD=master.SECD_CURR_CD
                        INSTR_CD=master.INSTR_CD
                        MD_SPOT_VAL=master.MD_SPOT_VAL
                        MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
                        MUREX_IDNT_CNTR=master.MUREX_IDNT_CNTR
                        MUREX_INTRNL_TMSP=master.MUREX_INTRNL_TMSP
                        MUREX_JOB_REF=master.MUREX_JOB_REF
                        MUREX_DTS_REF=master.MUREX_DTS_REF
                        AS_OF_DT=master.AS_OF_DT
                        AS_OF_DT_YYYYMM=master.AS_OF_DT_YYYYMM
                        MUREX_PORT_CD=master.MUREX_PORT_CD
                        NPV_PLCCY_AMT=master.NPV_PLCCY_AMT
                        NPV_CCY_CD=master.NPV_CCY_CD
                        BANK_CURR_CD_2=slave0.BANK_CURR_CD
                        MUREX_CURR_CD_1=slave1.MUREX_CURR_CD
                        BANK_CURR_CD_1=slave1.BANK_CURR_CD
                    }
                    join_state = "master"
                    table_id = "14296183026432_one"
                    master_fields = ["FstCurrAmtPL","PL_CURR_CD","DISC_MID_RPICE_3","INDIRECT_DISC_MID_PRICE_3","FST_CURR_AMT_HKE","SECD_CURR_AMT_HKE","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","BANK_CURR_CD_2","MUREX_CURR_CD_1","BANK_CURR_CD_1"]
                    add_fields = ["slave0.BANK_CURR_CD","slave1.MUREX_CURR_CD","slave1.BANK_CURR_CD"]
        }

}
sink {
        MergeLocalFile {
        final_name="/tmp/test_e_2_bj1.txt"
        path="/tmp/out"
        source_table_name = "localfile_source_2_trans_6_trans_7_trans_10_trans_11_trans_28_trans_29"
            tmp_path="/tmp/tmp"
            file_format_type="text"
            field_delimiter=","
            row_delimiter="\n"
            sink_columns=["MUREX_CURR_CD_1","BANK_CURR_CD_1","BANK_CURR_CD_2","FstCurrAmtPL","PL_CURR_CD","FST_CURR_AMT_HKE","SECD_CURR_AMT_HKE","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_INTRNL_TRAN_ID","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","MUREX_DTS_REF","AS_OF_DT","AS_OF_DT_YYYYMM","MUREX_PORT_CD","NPV_PLCCY_AMT","NPV_CCY_CD","DISC_MID_RPICE_3","INDIRECT_DISC_MID_PRICE_3"]
            batch_size="1000"
            compress_codec="none"
        }

        ConsoleHole {
        source_table_name="null_trans_9"
        }
        ConsoleHole {
        source_table_name="null_trans_20"
        }
        ConsoleHole {
        source_table_name="null_trans_21"
        }
        ConsoleHole {
        source_table_name="null_trans_22"
        }
        ConsoleHole {
        source_table_name="null_trans_23"
        }
        ConsoleHole {
        source_table_name="null_trans_24"
        }
        ConsoleHole {
        source_table_name="null_trans_25"
        }
        ConsoleHole {
        source_table_name="null_trans_26"
        }
        ConsoleHole {
        source_table_name="null_trans_27"
        }
        ConsoleHole {
        source_table_name="null_trans_32"
        }
        ConsoleHole {
        source_table_name="null_trans_33"
        }
}