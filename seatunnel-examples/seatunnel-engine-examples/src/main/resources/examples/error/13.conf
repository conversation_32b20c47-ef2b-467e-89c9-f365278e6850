 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="*******************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="Cdyanfa_123456"
query="select `id`,`sn`,`name`,`price`,`num`,`alert_num`,`image`,`images`,`weight`,`create_time`,`update_time`,`category_name`,`brand_name`,`spec`,`sale_num`,`comment_num`,`status` from `test`.`tb_sku` Where 1=1 "
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"parallelism"=1
}



}
transform {
}
sink {
Jdbc {
url="****************************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="Cdyanfa_123456"
database="dsg_etl_tmp_d"
table="t_7694222911736391727556_e000001"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="E000001_source_1"
"generate_sink_sql"="true"
"primary_keys" = ["id"]
"enable_upsert"="true"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
"insert_error_strategy"=continue
}

}