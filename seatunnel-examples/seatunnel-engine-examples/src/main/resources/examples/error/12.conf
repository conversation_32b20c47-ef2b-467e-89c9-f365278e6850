env {
    "job.mode"="STREAMING"
    "job.name"="10"
}
source {
    Kafka {
    topic="ljw_test"
    bootstrap.servers="**************:9092"
    pattern="false"
    consumer.group="SeaTunnel-Consumer-Group"
    result_table_name="kafka_source_1"
    commit_on_checkpoint="true"
    format="json"
    format_error_handle_way="fail"
    field_delimiter=""
    start_mode=group_offsets
    start_mode.offsets=""
    start_mode.timestamp=""
    partition-discovery.interval-millis="-1"
    }



}
transform {
SQL{
source_table_name="kafka_source_1"
result_table_name="kafka_source_1_trans_2"
query="select agent_send_timestamp, collector_recv_timestamp, raw_message, ip, index, logical_index, logtype, hostname, appname, domain, context_id, tag, id, raw_message_length, timestamp from kafka_source_1 where 1 = 1"
}
SQL{
source_table_name="kafka_source_1_trans_2"
result_table_name="kafka_source_1_trans_2_trans_3"
query="select agent_send_timestamp,collector_recv_timestamp,raw_message,ip,index,logical_index,logtype,hostname,appname,domain,context_id,tag,id,raw_message_length,timestamp,substr(timestamp,1,10) as etl_date from kafka_source_1_trans_2"
}
}
sink {
    Hive {
    source_table_name = "kafka_source_1_trans_2_trans_3"
    table_name = "ljw.gyrx_test"
    metastore_uri = "thrift://**************:9083"
    }

}