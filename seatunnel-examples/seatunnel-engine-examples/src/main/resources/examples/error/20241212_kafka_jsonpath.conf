env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Kafka {
    topic="gongyinruixin"
    bootstrap.servers="192.168.90.221:9092"
    pattern="false"
    consumer.group="SeaTunnel-Consumer-Group11"
    result_table_name="kafka_source_1"
    commit_on_checkpoint="true"
    schema= {
    fields {
    "agent_send_timestamp"="BIGINT"
    "collector_recv_timestamp"="BIGINT"
    "$.k8s.k8s_namespace"="STRING"
    "$.k8s.runtime"="STRING"
    "$.k8s.aaa"="STRING"
    "$.k8s.k8s_labels.app"="STRING"
    "$.k8s.k8s_labels.tianniu-app-type"="STRING"
    "$.k8s.k8s_labels.subtype.a"="STRING"
    "$.k8s.k8s_labels.subtype.b"="STRING"
    "$.k8s.image_id"="STRING"
    "ip"="STRING"
    "source"="STRING"
    "hostname"="STRING"
    "appname"="STRING"
    "tag"="STRING"
    "timestamp"="BIGINT"
    }
    }
    mappers = {
    "agent_send_timestamp" = "agent_send_timestamp"
    "collector_recv_timestamp" = "collector_recv_timestamp"
    "$.k8s.k8s_namespace" = "k8s_k8s_namespace"
    "$.k8s.runtime" = "k8s_runtime"
    "$.k8s.aaa" = "k8s_aaa"
    "$.k8s.k8s_labels.app" = "k8s_k8s_labels_app"
    "$.k8s.k8s_labels.tianniu-app-type" = "k8s_k8s_labels_tianniu_app_type"
    "$.k8s.k8s_labels.subtype.a" = "k8s_k8s_labels_subtype_a"
    "$.k8s.k8s_labels.subtype.b" = "k8s_k8s_labels_subtype_b"
    "$.k8s.image_id" = "k8s_image_id"
    "ip" = "ip"
    "source" = "source"
    "hostname" = "hostname"
    "appname" = "appname"
    "tag" = "tag"
    "timestamp" = "timestamp"
    }
    format="json"
    format_error_handle_way="fail"
    field_delimiter=""
    start_mode=group_offsets
    start_mode.offsets=""
    start_mode.timestamp=""
    partition-discovery.interval-millis="-1"
    }



}
transform {
}
sink {
MergeLocalFile {
    final_name="/usr/loca/lihj/json_20241212.txt"
    path="/usr/loca/lihj"
    source_table_name = "kafka_source_1"
    tmp_path="/usr/loca/lihj/tmp"
    file_format_type="text"
    field_delimiter="\u0001"
    row_delimiter="\n"
    sink_columns=["agent_send_timestamp","collector_recv_timestamp","k8s_k8s_namespace","k8s_runtime","k8s_aaa","k8s_k8s_labels_app","k8s_k8s_labels_tianniu_app_type","k8s_k8s_labels_subtype_a","k8s_k8s_labels_subtype_b","k8s_image_id","ip","source","hostname","appname","tag","timestamp"]
    batch_size="1000"
    compress_codec="none"
    overwrite_file="false"
}

}