env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
LocalFile {
path="/opt/zxyh_test/DS_FX_REVAL.txt"
result_table_name = "localfile_source_1"
file_format_type="text"
field_delimiter=","
parse_partition_from_path=true
date_format="yyyy-MM-dd"
datetime_format="yyyy-MM-dd HH:mm:ss"
time_format="HH:mm:ss"
schema= {
    fields {
        BATCH_DT=DATE
        FST_CURR_AMT=DOUBLE
        FST_CURR_CD=STRING
        SECD_CURR_AMT=DOUBLE
        SECD_CURR_CD=STRING
        INSTR_CD=STRING
        MD_SPOT_VAL=DOUBLE
        MUREX_INTRNL_TRAN_ID=DOUBLE
        MUREX_IDNT_CNTR=DOUBLE
        MUREX_INTRNL_TMSP=FLOAT
        MUREX_JOB_REF=DOUBLE
        MUREX_DTS_REF=DOUBLE
        AS_OF_DT=DATE
        AS_OF_DT_YYYYMM=DOUBLE
        MUREX_PORT_CD=STRING
        NPV_PLCCY_AMT=DOUBLE
        NPV_CCY_CD=STRING
    }
}
compress_codec=none
}
LocalFile {
    path="/opt/zxyh_test/DS_LV1_PL_DETAILS.txt"
    result_table_name = "localfile_source_2"
    file_format_type="text"
    field_delimiter=","
    parse_partition_from_path=true
    date_format="yyyy-MM-dd"
    datetime_format="yyyy-MM-dd HH:mm:ss"
    time_format="HH:mm:ss"
    schema= {
    fields {
    PL_CURR_CD=STRING
    MUREX_INTRNL_TRAN_ID=DOUBLE
    MUREX_PORT_CD=STRING
    MUREX_DTS_REF=DOUBLE
    BATCH_DT=DATE
}
}
compress_codec=none
}



}
transform {
    InnerJoin{
        source_table_name="localfile_source_2"
        result_table_name="localfile_source_2_trans_7"
        fields = ["PL_CURR_CD"]
        primary_key = "BATCH_DT"
        join_keys={
        master.MUREX_DTS_REF=slave0.MUREX_DTS_REF
        master.MUREX_INTRNL_TRAN_ID=slave0.MUREX_INTRNL_TRAN_ID
        master.BATCH_DT=slave0.BATCH_DT
        master.MUREX_PORT_CD=slave0.MUREX_PORT_CD
        }
        join_state = "slave0"
        table_id = "14242614501504"
    }
    InnerJoin{
        source_table_name="localfile_source_1"
        result_table_name="localfile_source_1_trans_8"
        fields = ["MUREX_INTRNL_TRAN_ID","MUREX_PORT_CD","MUREX_DTS_REF","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","AS_OF_DT","AS_OF_DT_YYYYMM","NPV_PLCCY_AMT","NPV_CCY_CD"]
        join_keys={
        slave0.MUREX_INTRNL_TRAN_ID=master.MUREX_INTRNL_TRAN_ID
        slave0.MUREX_DTS_REF=master.MUREX_DTS_REF
        slave0.BATCH_DT=master.BATCH_DT
        slave0.MUREX_PORT_CD=master.MUREX_PORT_CD
        }
        join_state = "master"
        table_id = "14242614501504"
        master_fields = ["MUREX_INTRNL_TRAN_ID","MUREX_PORT_CD","MUREX_DTS_REF","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","AS_OF_DT","AS_OF_DT_YYYYMM","NPV_PLCCY_AMT","NPV_CCY_CD","PL_CURR_CD"]
        add_fields = ["slave0.PL_CURR_CD"]
    }
}
sink {
    MergeLocalFile {
        final_name="/opt/zxyh_test/out/test_a.txt"
        path="/opt/zxyh_test/out"
        source_table_name = "localfile_source_1_trans_8"
        tmp_path="/opt/zxyh_test/out/tmp"
        file_format_type="text"
        field_delimiter=","
        row_delimiter="\n"
        sink_columns=["PL_CURR_CD","MUREX_INTRNL_TRAN_ID","MUREX_PORT_CD","MUREX_DTS_REF","BATCH_DT","FST_CURR_AMT","FST_CURR_CD","SECD_CURR_AMT","SECD_CURR_CD","INSTR_CD","MD_SPOT_VAL","MUREX_IDNT_CNTR","MUREX_INTRNL_TMSP","MUREX_JOB_REF","AS_OF_DT","AS_OF_DT_YYYYMM","NPV_PLCCY_AMT","NPV_CCY_CD"]
        batch_size="1000"
        compress_codec="none"
    }

    ConsoleHole {
        source_table_name="localfile_source_2_trans_7"
    }
}