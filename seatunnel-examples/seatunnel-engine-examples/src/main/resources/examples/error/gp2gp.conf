env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="*******************************************************************"
        driver="com.pivotal.jdbc.GreenplumDriver"
        user="gp"
        password="gpadmin"
        query="select * from emp_20241113"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"

    }

}
transform {

}
sink {
    Jdbc {
url="*******************************************************************"
        driver="com.pivotal.jdbc.GreenplumDriver"
		user="gp"
        password="gpadmin"
        database="postgres"
        table="public.emp_20241114"
        "batch_size"="1000"
        "support_upsert_by_query_primary_key_exist"="merge"
        "generate_sink_sql"="true"
    }
}