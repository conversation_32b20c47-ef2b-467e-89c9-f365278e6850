env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="************************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="c##QJQ"
        password="QJQ"
        query="select \"EMP_ID\",\"EMP_NAME\",\"GENDER\",\"ACCOUNT\",\"ORG_ID\",\"BIRTH_DATE\",\"AGE\",\"NATIONALITY\",\"PROVINCE\",\"CITY\",\"EMAIL\",\"PHONE\",\"BEGIN_DATE\",\"REMARK\",\"CREATE_TIME\",\"UPDATE_TIME\",\"SAL\" from \"C##QJQ\".\"EMP2\" Where 1=1  and UPDATE_TIME > TO_DATE('2023-10-28 09:13:22','yyyy-MM-dd hh24:mi:ss') and UPDATE_TIME <= TO_DATE('2023-10-28 09:13:22','yyyy-MM-dd hh24:mi:ss')"
        "batch_size"="1000"
        "split.size"="1000"
        "table.path"=""C##QJQ"."EMP2""
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "partition_column"="UPDATE_TIME"
            "partition_num"="5"
            "parallelism"=1
        }



}
transform {
}
sink {
        Jdbc {
        url="*********************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="test"
        table="dai_test_0904"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "primary_keys" = ["EMP_ID"]
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}