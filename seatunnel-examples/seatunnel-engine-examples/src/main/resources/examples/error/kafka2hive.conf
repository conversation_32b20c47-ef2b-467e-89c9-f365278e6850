env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
	 Kafka {
		 topic="tables"
		 bootstrap.servers="docker1:9092"
		 pattern="false"
		 consumer.group="SeaTunnel-Consumer-Group"
		 result_table_name="kafka_source_1"
		 commit_on_checkpoint="true"
		 schema= {
		 fields {
		 id=INT
		 name=STRING
		 sex=INT
		 emp_id=INT
		 emp_name=STRING
		 appname=STRING
		 }
		 }
		 format="json"
		 format_error_handle_way="skip"
		 field_delimiter=""
		 start_mode=earliest
		 start_mode.offsets=""
		 start_mode.timestamp=""
		 partition-discovery.interval-millis="-1"
	 }



 }
 transform {
	 SQL{
		 source_table_name="kafka_source_1"
		 result_table_name="kafka_source_1_trans_2"
		 query="select emp_id, emp_name, appname from kafka_source_1 where appname ='A01'"
	 }
 }
 sink {
	 Jdbc {
		 url="*****************************************"
		 driver="org.apache.hive.jdbc.HiveDriver"
		 user="hive"
		 password="hive"
		 database="default"
		 table="H_A01"
		 "connection_check_timeout_sec"="30"
		 "batch_size"="1024"
		 "is_exactly_once"="false"
		 "max_commit_attempts"="3"
		 "transaction_timeout_sec"="-1"
		 "max_retries"="0"
		 "auto_commit"="true"
		 "support_upsert_by_query_primary_key_exist"="false"
		 "source_table_name"="kafka_source_1_trans_2"
		 "generate_sink_sql"="true"
		 "enable_upsert"="false"
		 "pk_strategy"="stop"
		 schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
	 }

 }