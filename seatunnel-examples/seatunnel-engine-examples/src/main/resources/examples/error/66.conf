env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
"checkpoint.interval"="1000"
}
source {
    Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="c##YKW"
        password=YKW
        query="SELECT  systimestamp AS c FROM dual"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "parallelism"=1
    }
}
transform {

}
sink {
    console{}
}