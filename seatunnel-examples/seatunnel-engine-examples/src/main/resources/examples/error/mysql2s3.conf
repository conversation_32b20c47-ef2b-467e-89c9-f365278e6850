
env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
    Jdbc {
        url="**************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select `id`,`name`,`date_1`,`date_time` from `starrocks` Where 1=1 and id <=-100"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
    }



}
transform {
}
sink {
    S3File {
        path="/lihj_20240920"
        tmp_path="/tmp/seatunnel"
        bucket="s3a://joyadata"
        access_key="minio"
        secret_key="Cdyanfa_123456"
        fs.s3a.endpoint="http://192.168.90.221:9000"
        fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        file_format_type="text"
        field_delimiter="\u0001"
        row_delimiter="\n"
        have_partition="false"
        sink_columns=["id","name","date_1","date_time"]
        is_enable_transaction="true"
        batch_size="1000000"
        filename_time_format="yyyy.MM.dd"
        compress_codec="none"
    }
}


