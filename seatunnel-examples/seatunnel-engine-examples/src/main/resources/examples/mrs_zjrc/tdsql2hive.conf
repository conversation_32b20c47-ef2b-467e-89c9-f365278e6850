env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name from emp_user_gbk"
       }
}
transform {
}
sink {
     Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        database="default"
        table="test_utf8"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
        support_upsert_by_query_primary_key_exist="true"
        enable_upsert="false"
	generate_sink_sql=true
      }

}

