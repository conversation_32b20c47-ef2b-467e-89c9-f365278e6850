env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="**********************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="dwinst"
        password="abc123"
        query="select id,name,address from APOC.EMP_USER"
       }
}
transform {
}
sink {
     Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        database="default"
        table="test01"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
        support_upsert_by_query_primary_key_exist="true"
        enable_upsert="false"
        generate_sink_sql=true
      }

}

