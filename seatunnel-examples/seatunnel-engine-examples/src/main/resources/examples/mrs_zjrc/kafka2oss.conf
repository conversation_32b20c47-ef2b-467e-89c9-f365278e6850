env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Kafka {
            bootstrap.servers = "158.219.101.118:9092,158.219.101.119:9092,158.219.101.120:9092"
            topic = "xone20240315"
            consumer.group = "test_group"
           start_mode = "earliest"
       }
}
transform {
}
sink {
   OssFile {
       path="/xone/osstest/20240321_kafka"
       bucket="oss://dl-bucket"
       access_key="I1Lcap4pYznpEGSU"
       access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
       endpoint="http://oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
       file_format_type="text"
   }


}

