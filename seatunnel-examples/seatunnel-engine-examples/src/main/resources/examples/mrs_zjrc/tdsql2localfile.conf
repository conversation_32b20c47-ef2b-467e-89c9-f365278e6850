env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
   LocalFile {
       path="/home/<USER>/joyadata/joyadata-st/localFile"
       file_format_type="text"
       field_delimiter="\t"
       row_delimiter="\n"
   }
}

