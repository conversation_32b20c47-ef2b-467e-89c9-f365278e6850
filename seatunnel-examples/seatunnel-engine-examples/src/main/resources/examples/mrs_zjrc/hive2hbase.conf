env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name,address from default.test01"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
 Hbase {
    zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
    table = "emp_user"
    rowkey_column=["id"]
    family_name = {
        all_columns = "cf1"
    }
    file_path="/home/<USER>/joyadata/mrs/"
    user="sjjh_poc"
    server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
  }

}

