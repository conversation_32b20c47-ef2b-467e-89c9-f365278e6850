env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="**********************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="dwinst"
        password="abc123"
        query="select id,name,address from APOC.EMP_USER"
       }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[ID]
        enable_upsert="true"

    }
}

