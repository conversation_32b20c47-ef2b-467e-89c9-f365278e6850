env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
  Clickhouse {
    host = "**************:21426"
    database = "dsg"
    table = "emp_user004_all"
    username = "sjjh_poc"
    password = "Bigdata@123"
    clickhouse.url.config="aa"
    clickhouse.options.config={
        ssl=true
        sslmode=NONE
        database = "dsg"
    }
  }
}

