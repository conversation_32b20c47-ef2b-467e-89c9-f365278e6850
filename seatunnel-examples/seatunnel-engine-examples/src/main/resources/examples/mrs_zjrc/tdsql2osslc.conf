env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:mysql://***************:15146/sjffdb"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
   S3File {
       path="/xone/osslctest/20240321"
       bucket="s3a://dl-bucket"
       access_key="XH5VPE3VM4XIG8GW5FEL"
       secret_key="m96flfmXsif5760NQxT4rINZ3nFOX00UQvbADvm4"
       fs.s3a.endpoint="http://s3.kf.zjnx.net:8009"
       file_format_type="csv"
       fs.s3a.aws.credentials.provider = "com.amazonaws.auth.InstanceProfileCredentialsProvider"
   }
}

