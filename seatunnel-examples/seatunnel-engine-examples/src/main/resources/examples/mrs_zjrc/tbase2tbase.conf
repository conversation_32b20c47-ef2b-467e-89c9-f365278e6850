env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*******************************************"
        driver="org.postgresql.Driver"
        user="sjff"
        password="sjff1234"
        query="select * from sjff.t2f_13"
        partition_column="id"
        partition_num =10
       }
}
transform {
}
sink {
     Jdbc {
        url="******************************************"
         driver="org.postgresql.Driver"
         user="sjff"
         password="sjff1234"
         database="sjff"
         table="sjff.t2f_13"
         support_upsert_by_query_primary_key_exist="false"
         generate_sink_sql="true"
         enable_upsert="false"
	query=""
         primary_keys=[id]
     }
}

