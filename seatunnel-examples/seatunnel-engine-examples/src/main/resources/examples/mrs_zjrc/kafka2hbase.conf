env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
         Kafka {
                    bootstrap.servers = "***************:9092,***************:9092,***************:9092"
                    topic = "xone20240402"
                    consumer.group = "hive_test_group"
                    start_mode = "earliest"
                    format=text
                    field_delimiter=","
                    schema{
                       fields{
                             id="int"
                             name="string"
                             address="string"
                       }
                    }
               }
}
transform {
}
sink {
  Hbase {
     zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
     table = "emp_user"
     rowkey_column=["id"]
     family_name = {
         all_columns = "cf1"
     }
     file_path="/home/<USER>/joyadata/mrs/"
     user="sjjh_poc"
     server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
   }

}

