env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        OssFile {
            path="/xone/osstest/20240321_kafka/T_823103791707455489_db12f19eea_0_1_0.txt"
            bucket="oss://dl-bucket"
            access_key="I1Lcap4pYznpEGSU"
            access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
            endpoint="http://oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
            file_format_type="text"
   field_delimiter=","
                   schema{
                        fields{
                            id=int
                            name=string
                            address=string
                        }
                   }
        }
}
transform {
}
sink {
    Jdbc {
        url="jdbc:mysql://***************:15146/sjffdb"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id,name]
        enable_upsert="true"

    }
}

