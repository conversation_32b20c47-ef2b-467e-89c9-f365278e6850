env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:hive2://**************:24002/default"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name from default.emp_user"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
     Jdbc {
         url="***********************************************"
         driver="org.postgresql.Driver"
         user="sjjh_poc"
         password="Bigdata@123"
         database="sjjh_poc"
         table="sjjh_poc.test.emp_user"
         support_upsert_by_query_primary_key_exist="false"
         generate_sink_sql="true"
         primary_keys=[id]
         enable_upsert="false"

     }
}

