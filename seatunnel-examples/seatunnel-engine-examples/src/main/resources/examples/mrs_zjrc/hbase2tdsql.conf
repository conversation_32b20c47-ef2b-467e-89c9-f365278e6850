env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Hbase {
        zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
        table = "emp_user"
        query_columns=["cf1:name","cf1:address"]
    schema={
        columns=[{
            name="cf1:name"
            type="string"
        },{
            name="cf1:address"
            type="string"
        }]
    }
    file_path="/home/<USER>/joyadata/mrs/"
    user="sjjh_poc"
    server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
    result_table_name="hbase_source"
  }


}
transform {
    FieldMapper{
        source_table_name="hbase_source"
        result_table_name="hbase_source1"
        field_mapper={
            "cf1:name"=name
            "cf1:address"=address
        }
    }
}
sink {
    Jdbc {
        source_table_name="hbase_source1"
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user1"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[name]
        enable_upsert="true"

    }
}

