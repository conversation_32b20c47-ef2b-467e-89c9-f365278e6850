env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
   S3File {
       path="/xone/osslctest/20240328_zsp_txt"
       bucket="s3a://dl-bucket"
       access_key="IC76Z2QYHZS2KQJFYZJ2"
       secret_key="ldTtYsl6BDsjPmBysvM9BtGHAWcpJli9OWor5BRq"
       fs.s3a.endpoint="http://s3.kf.zjnx.net:8009"
	file_format_type="text"
       field_delimiter=","
       fs.s3a.aws.credentials.provider = "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
   }
}

