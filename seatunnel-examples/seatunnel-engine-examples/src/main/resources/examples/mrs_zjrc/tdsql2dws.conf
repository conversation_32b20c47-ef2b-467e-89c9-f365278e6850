env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
         user="sjff"
         password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
     Jdbc {
         url="***********************************************"
         driver="org.postgresql.Driver"
         user="sjjh_poc"
         password="Bigdata@123"
         database="sjjh_poc"
         table="test.emp_user"
         support_upsert_by_query_primary_key_exist="true"
         generate_sink_sql="true"
         primary_keys=[id]
         enable_upsert="false"

     }
}

