env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name from default.test"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
  Elasticsearch {
    hosts = ["*************:24100","**************:24100"]
    tls_verify_certificate = false
    tls_verify_hostname = false
    index = "xone_test_20240327"
    index_type = "xone"
    "schema_save_mode"="CREATE_SCHEMA_WHEN_NOT_EXIST"
    "data_save_mode"="APPEND_DATA"
  }
}

