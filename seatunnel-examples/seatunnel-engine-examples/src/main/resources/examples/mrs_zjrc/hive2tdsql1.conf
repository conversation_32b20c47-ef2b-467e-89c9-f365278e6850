env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Hive {
        table_name= "default.test"
        metastore_uri= "thrift://**************:21088"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
	hive_site_path="/home/<USER>/joyadata/mrs/hive-site.xml"
       }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }
}

