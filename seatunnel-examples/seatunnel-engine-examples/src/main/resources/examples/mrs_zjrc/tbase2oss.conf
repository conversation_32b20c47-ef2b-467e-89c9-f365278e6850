env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:postgresql://*************:11004/sjff"
        driver="org.postgresql.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from sjff.emp_user"
       }
}
transform {
}
sink {
   OssFile {
       path="/xone/osstest/20240321_tbase"
       bucket="oss://dl-bucket"
       access_key="I1Lcap4pYznpEGSU"
       access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
       endpoint="http://oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
       file_format_type="orc"
   }
}

