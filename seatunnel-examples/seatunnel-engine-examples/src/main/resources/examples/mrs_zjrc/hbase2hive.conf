env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Hbase {
        zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
        table = "emp_user"
        query_columns=["cf1:name","cf1:address"]
    schema={
        columns=[{
            name="cf1:name"
            type="string"
        },{
            name="cf1:address"
            type="string"
        }]
    }
    file_path="/home/<USER>/joyadata/mrs/"
    user="sjjh_poc"
    server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
    result_table_name="hbase_source"
  }


}
transform {
    FieldMapper{
        source_table_name="hbase_source"
        result_table_name="hbase_source1"
        field_mapper={
            "cf1:name"=name
            "cf1:address"=address
        }
    }
}
sink {
         Jdbc {
             url="*****************************************"
             driver="org.apache.hive.jdbc.HiveDriver"
             user="sjjh_poc"
             password="Bigdata@123"
             database="default"
             table="test01"
             use_kerberos="true"
             kerberos_principal="sjjh_poc"
             kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
             krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
             support_upsert_by_query_primary_key_exist="true"
             enable_upsert="false"
             generate_sink_sql=true
           }
}

