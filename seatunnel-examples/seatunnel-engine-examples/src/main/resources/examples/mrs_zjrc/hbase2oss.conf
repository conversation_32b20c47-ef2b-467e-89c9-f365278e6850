env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Hbase {
        zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
        table = "emp_user"
        query_columns=["cf1:name","cf1:address"]
    schema={
        columns=[{
            name="cf1:name"
            type="string"
        },{
            name="cf1:address"
            type="string"
        }]
    }
    file_path="/home/<USER>/joyadata/mrs/"
    user="sjjh_poc"
    server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
    result_table_name="hbase_source"
  }


}
transform {
    FieldMapper{
        source_table_name="hbase_source"
        result_table_name="hbase_source1"
        field_mapper={
            "cf1:name"=name
            "cf1:address"=address
        }
    }
}
sink {
    OssFile {
         path="/xone/osstest/20240402_hbase"
         bucket="oss://dl-bucket"
         access_key="I1Lcap4pYznpEGSU"
         access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
         endpoint="oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
         file_format_type="text"
         field_delimiter=","
     }
}

