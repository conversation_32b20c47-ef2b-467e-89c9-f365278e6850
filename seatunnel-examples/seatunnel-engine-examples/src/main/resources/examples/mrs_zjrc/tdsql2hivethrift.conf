env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name from emp_user"
       }
}
transform {
}
sink {
    Hive {
        table_name= "default.emp_user"
        metastore_uri= "thrift://**************:21088"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
        hive_site_path="/home/<USER>/joyadata/mrs/hive-site.xml"        
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
    }
}

