env {
	"job.mode"="BATCH"
	"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user1"
       }
}
transform {
}
sink {
   Kafka {
       bootstrap.servers = "***************:9092,***************:9092,***************:9092"
       topic = "xone20240402"
       format = text
       field_delimiter = ","
   }
}

