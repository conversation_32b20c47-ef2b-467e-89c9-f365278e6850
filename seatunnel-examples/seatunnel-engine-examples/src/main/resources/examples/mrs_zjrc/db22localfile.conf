env {

"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="**********************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="dwinst"
        password="abc123"
        query="select id,name,address from APOC.EMP_USER"
       }
}
transform {
}
sink {
   LocalFile {
       path="/home/<USER>/joyadata/joyadata-st/localFile"
       tmp_path="/home/<USER>/joyadata/joyadata-st/localFile/tmp"
     file_format_type="text"
       field_delimiter=","
       row_delimiter="\n"
       sink_columns=["ID","NAME","ADDRESS"]
   }


}

