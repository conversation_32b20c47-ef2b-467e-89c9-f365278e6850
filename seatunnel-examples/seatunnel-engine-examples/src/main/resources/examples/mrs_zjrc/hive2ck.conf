env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name from default.test"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
      Clickhouse {
        host = "**************:21426"
        database = "dsg"
        table = "emp_user004_all"
        username = "sjjh_poc"
        password = "Bigdata@123"
        clickhouse.url.config="aa"
        clickhouse.options.config={
            ssl=true
            sslmode=NONE
            database = "dsg"
        }
      }

}

