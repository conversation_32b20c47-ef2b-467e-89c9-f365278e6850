env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
           Jdbc {
              url="****************************************"
              driver="org.apache.hive.jdbc.HiveDriver"
              user="all_analysis"
              password="123456"
              query="select id,name from default.emp_user"
             }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[ID]
        enable_upsert="true"

    }
}

