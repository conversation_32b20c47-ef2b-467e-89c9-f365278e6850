env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:mysql://***************:15146/sjffdb"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
    Jdbc {
        url="**********************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="dwinst"
        password="abc123"
        database="DW"
        table="APOC.EMP_USER"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }
}

