env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Kafka {
            bootstrap.servers = "***************:9092,***************:9092,***************:9092"
            topic = "xone20240315"
            consumer.group = "tdsql1_test_group"
            start_mode = "earliest"
            format=text
            field_delimiter=","
            schema{
               fields{
                     id="int"
                     name="string"
                     address="string"
               }
            }
       }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }


}

