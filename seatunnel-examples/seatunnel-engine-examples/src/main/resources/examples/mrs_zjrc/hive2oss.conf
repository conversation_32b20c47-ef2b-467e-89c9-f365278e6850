env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name from default.test"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
   OssFile {
       path="/xone/osstest/zsp/20240327_hive"
       bucket="oss://dl-bucket"
       access_key="I1Lcap4pYznpEGSU"
       access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
       endpoint="http://oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
       file_format_type="orc"
   }
}

