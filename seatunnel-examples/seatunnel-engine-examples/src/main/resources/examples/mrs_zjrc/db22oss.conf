env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:db2://**************:50000/DW"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="dwinst"
        password="abc123"
        query="select id,name,address from APOC.EMP_USER"
       }
}
transform {
}
sink {
  OssFile {
       path="/xone/osstest/zsp/20240327"
       bucket="oss://dl-bucket/"
       access_key="I1Lcap4pYznpEGSU"
       access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
       endpoint="oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
       file_format_type="orc"
   }

}

