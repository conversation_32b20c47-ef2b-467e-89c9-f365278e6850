env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        OssFile {
                   path="/xone/osstest/20240321_kafka/T_823103791707455489_db12f19eea_0_1_0.txt"
                   bucket="oss://dl-bucket"
                   access_key="I1Lcap4pYznpEGSU"
                   access_secret="7xGIQ6zedlPov6Mvgkjpo0et7Elteo"
                   endpoint="http://oss1cfd-cn-hangzhou-kfcsy-d01-a.ops.dc-tst-zj96596.com"
                   file_format_type="text"
                   field_delimiter=","
                   schema{
                        fields{
                            id=int
                            name=string
                            address=string
                        }
                   }
               }

}
transform {
}
sink {
      Jdbc {
          url="*****************************************"
          driver="org.apache.hive.jdbc.HiveDriver"
          user="sjjh_poc"
          password="Bigdata@123"
          database="default"
          table="test01"
          use_kerberos="true"
          kerberos_principal="sjjh_poc"
          kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
          krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
          support_upsert_by_query_primary_key_exist="true"
          enable_upsert="false"
          generate_sink_sql=true
        }
}

