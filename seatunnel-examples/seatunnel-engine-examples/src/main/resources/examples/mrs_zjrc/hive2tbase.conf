env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="sjjh_poc"
        password="Bigdata@123"
        query="select id,name from default.test"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
       }
}
transform {
}
sink {
    Jdbc {
        url="******************************************"
        driver="org.postgresql.Driver"
        user="sjff"
        password="sjff1234"
        database="sjff"
        table="sjff.emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }
}

