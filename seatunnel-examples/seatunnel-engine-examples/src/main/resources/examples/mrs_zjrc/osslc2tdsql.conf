env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
         S3File {
             path="/xone/osslctest/20240321/T_823032862600069121_e99a7165bf_0_1_0.orc"
             bucket="s3a://dl-bucket"
             access_key="IC76Z2QYHZS2KQJFYZJ2"
             secret_key="ldTtYsl6BDsjPmBysvM9BtGHAWcpJli9OWor5BRq"
             fs.s3a.endpoint="http://s3.kf.zjnx.net:8009"
             file_format_type="orc"
             fs.s3a.aws.credentials.provider = "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
         }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }
}

