env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Hive {
        table_name= "default.test"
        metastore_uri= "thrift://**************:21088"
        use_kerberos="true"
        kerberos_principal="sjjh_poc"
        kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
        krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
	hive_site_path="/home/<USER>/joyadata/mrs/hive-site.xml"
       }
}
transform {
}
sink {
    Hive {
        table_name= "default.test"
        metastore_uri= "thrift://**************:21088"
       }
}

