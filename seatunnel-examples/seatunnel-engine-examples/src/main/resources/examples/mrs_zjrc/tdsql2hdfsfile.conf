env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="jdbc:mysql://***************:15146/sjffdb"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
  HdfsFile {
    fs.defaultFS = "hdfs://hacluster"
    path = "/user/hive/warehouse/emp_user"
    file_format_type = "text"
    kerberos_principal="sjjh_poc"
    kerberos_keytab_path="/home/<USER>/joyadata/mrs/user.keytab"
    krb5_path="/home/<USER>/joyadata/mrs/krb5.conf"
    hdfs_site_path="/home/<USER>/joyadata/mrs/hdfs-site.xml"
  }
}

