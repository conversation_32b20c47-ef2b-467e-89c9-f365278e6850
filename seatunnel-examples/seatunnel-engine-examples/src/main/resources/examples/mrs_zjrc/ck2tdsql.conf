env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
         Clickhouse {
           host = "**************:21426"
           database = "dsg"
           username = "sjjh_poc"
           password = "Bigdata@123"
           sql = " select id,name,address from emp_user004_all"
           server_time_zone = "UTC"
           result_table_name= "emp_user004_all"
            clickhouse.options.config={
        ssl=true
        sslmode=none
        database = "dsg"
    }
         }
}
transform {
}
sink {

       Jdbc {
           url="*****************************************"
           driver="com.mysql.cj.jdbc.Driver"
           user="sjff"
           password="sjff1234"
           database="sjffdb"
           table="emp_user"
           support_upsert_by_query_primary_key_exist="true"
           generate_sink_sql="true"
           primary_keys=[id]
           enable_upsert="true"

       }
}

