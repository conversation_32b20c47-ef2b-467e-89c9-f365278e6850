env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   LocalFile {
       path="/home/<USER>/joyadata/joyadata-st/localFile"
       schema={
        fields{
            id = int
            name=string
	   address=string
            }
       }
       file_format_type="text"
       delimiter="\t"
   }
}
transform {
}
sink {
    Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        database="sjffdb"
        table="emp_user"
        support_upsert_by_query_primary_key_exist="true"
        generate_sink_sql="true"
        primary_keys=[id]
        enable_upsert="true"

    }
}

