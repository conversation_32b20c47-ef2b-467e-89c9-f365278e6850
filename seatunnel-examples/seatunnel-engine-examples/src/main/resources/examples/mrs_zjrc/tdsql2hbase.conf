env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="sjff"
        password="sjff1234"
        query="select id,name,address from emp_user"
       }
}
transform {
}
sink {
  Hbase {
    zookeeper_quorum = "**************:24002,**************:24002,**************:24002"
    table = "emp_user"
    rowkey_column=["id"]
    family_name = {
        all_columns = "cf1"
    }
    file_path="/home/<USER>/joyadata/mrs/"
    user="sjjh_poc"
    server_principal="zookeeper/hadoop.cs_bdp_zjrcu"
  }
}

