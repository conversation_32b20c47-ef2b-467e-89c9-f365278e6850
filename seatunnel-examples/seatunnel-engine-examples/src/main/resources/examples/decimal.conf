env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="***************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,card,name,age,city,insert_date from test101901m Where 1=1  and id >'0' and id <='200'"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="test101901m_source_1"
         "partition_column"="id"
         "partition_num"="5"
        "parallelism"=2
    }





}
transform {
            FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_2"
                        field_mapper={
                            city=city
                            insert_date=insert_date
                            name=name
                            id=id
                            card=card
                            age=age
                        }
            }
}
sink {
    Jdbc {
        url="***************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="tongji"
        table="test41223"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="test101901m_source_1_trans_2"
        "generate_sink_sql"="true"
        "primary_keys" = ["id"]
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
}