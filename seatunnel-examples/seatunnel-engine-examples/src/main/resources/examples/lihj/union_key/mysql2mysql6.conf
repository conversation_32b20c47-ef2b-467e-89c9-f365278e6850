env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from `seatunnel_source`.`emp_quality_lihj`"
        "batch_size"="10"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        }

}
transform {
}
sink {
        Jdbc {
         url="************************************************"
            driver="oracle.jdbc.driver.OracleDriver"
            user="c##ZSP"
            password="ZSP"
            database="ORCLCDB"
            table="C##ZSP.EMP_20408010"
            "connection_check_timeout_sec"="30"
            "batch_size"="10"
            "is_exactly_once"="false"
            "transaction_timeout_sec"="-1"
            "support_upsert_by_query_primary_key_exist"="false"
            "generate_sink_sql"="true"
            "primary_keys" = ["EMP_ID"]
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
            schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "pk_strategy"="stop"
            "insert_error_strategy"=continue
     }
}
