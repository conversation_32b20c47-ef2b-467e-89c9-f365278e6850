env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01_all"
        "result_table_name"="user01_all"
    }
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user02_all"
        "result_table_name"="user02_all"
    }
}
transform {
    UnionAll {
            source_table_name = "user01_all"
            result_table_name = "user01_all_1"
            fields = [id,name,address,phone]
            primary_key="id"
            join_state="master"
            table_id="202406032"
          }
      UnionAll {
            source_table_name = "user02_all"
            result_table_name = "user02_all_1"
            fields = [user_id,chinese,score,maths]
            primary_key="user_id"
            join_state="slave1"
            table_id="202406032"
      }

}
sink {
    console{
     "source_table_name"="user01_all_1"
    }

    Jdbc {
        "source_table_name"="user02_all_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_unnion_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
