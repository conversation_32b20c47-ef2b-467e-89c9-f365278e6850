env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="***********************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password=
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_104` "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }

}
transform {
}
sink {
     console{
     }

}