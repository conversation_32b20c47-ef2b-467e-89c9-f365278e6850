env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        HTTP {
        "table_path"=1
            result_table_name="E000008_source_1"
            url="http://192.168.100.58:8000/api/tms/products/index"
        method="GET"
        format="json"
        params= {
        }
        headers = {
                token="99e17574dbef4b8aa451e7f38644c7bd"
        }
            body="{}"
        schema = {
           fields = {
                groupType="STRING"
                isDefault="STRING"
                dbid="BIGINT"
                name="STRING"
                tenantCode="STRING"
                id="STRING"
                url="STRING"
            }
        }
        json_field = {
                groupType="$.result[*].groupType"
                isDefault="$.result[*].isDefault"
                dbid="$.result[*].dbid"
                name="$.result[*].name"
                tenantCode="$.result[*].tenantCode"
                id="$.result[*].id"
                url="$.result[*].url"
        }
        body_send_type="raw"
        connect_timeout_ms = "15000"
        datetimeFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormat ="yyyy-MM-dd"
        timeFormat ="HH:mm:ss"
        "table_path"=1
        }

}
transform {
        SQL{
            source_table_name="E000008_source_1"
            result_table_name="E000008_source_1_trans_2"
                    query="select dbid, tenantCode, id, groupType, url, isDefault, name from E000008_source_1 where 1=1"
        }
        FieldMapper{
            source_table_name="E000008_source_1_trans_2"
            result_table_name="E000008_source_1_trans_2_trans_3"
                    field_mapper={
                        "dbid"="dbid"
                        "tenantCode"="tenantcode"
                        "id"="id"
                        "groupType"="grouptype"
                        "url"="url"
                        "isDefault"="isdefault"
                        "name"="name"
                    }
        }
}
sink {
        console{
        source_table_name="E000008_source_1_trans_2_trans_3"
        }
}