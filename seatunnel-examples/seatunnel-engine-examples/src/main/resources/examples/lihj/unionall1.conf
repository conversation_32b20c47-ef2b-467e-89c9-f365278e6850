env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }

}
transform {
SQL {
    source_table_name = "user01"
    result_table_name = "fake1"
    query = "select id,address as name,address from user01"
  }
  SQL {
      source_table_name = "fake1"
      result_table_name = "fake2"
      query = "select id,name,address as score from fake1"
    }

}
sink {

    Jdbc {
        "source_table_name"="fake2"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
