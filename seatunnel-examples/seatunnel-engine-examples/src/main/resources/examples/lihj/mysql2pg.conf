env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from `seatunnel_source`.`emp_quality_millions_100w` "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "partition_column"="emp_id"
        "parallelism"=2
        }

}
transform {
}
sink {
 Jdbc {
        url="**********************************************"
        driver="org.postgresql.Driver"
        user="postgres"
        password="Cdyanfa_123456"
        database="postgres"
        table="public.emp20240909"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        generate_sink_sql=true
        }
}