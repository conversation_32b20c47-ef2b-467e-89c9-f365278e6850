env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     Jdbc {
         url="**************************************************************************************************************************************************************************************"
         driver="com.mysql.cj.jdbc.Driver"
         user="root"
         password="joyadata"
         query="select `id`,`name`,`date_1`,`data_time` from `zsp_test`.`ck` Where 1=1"
         "batch_size"="1000"
         "split.size"="1000"
         "connection_check_timeout_sec"="30"
         "snapshot.split.size"="8096"
         "snapshot.fetch.size"="1024"
         "connect.timeout.ms"="30000"
         "connection.pool.size"="20"
         "chunk-key.even-distribution.factor.lower-bound"="0.05"
         "chunk-key.even-distribution.factor.upper-bound"="100"
         "sample-sharding.threshold"="1000"
         "inverse-sampling.rate"="1000"
         "startup.mode"="INITIAL"
         "stop.mode"="NEVER"
         "result_table_name"="E000001_source_1"
     }
 
 
 
 }
 transform {
 }
 sink {
     Hive {
         source_table_name = "E000001_source_1"
         table_name = "default.zsp_test0828_name2"
         metastore_uri = "thrift://192.168.90.113:9083"
     }
 
 }
  