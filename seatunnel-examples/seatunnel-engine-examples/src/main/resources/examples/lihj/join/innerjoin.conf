env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user02"
        "result_table_name"="user02"
    }

}
transform {
    InnerJoin {
            source_table_name = "user02"
            result_table_name = "user02_1"
            fields = [user_id, score]
            primary_key="user_id"
            join_keys={
                "master.id"="slave1.user_id"
                "master.address"="slave1.score"
            }
            join_state="slave1"
            table_id="20240603"
            mapping_keys={
                "slave1.user_id"="user_id"
                "slave1.score"="score"
            }
          }
      InnerJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,address,phone]
            master_fields=[id,name,address,phone,user_id, score]
            add_fields=[slave1.user_id, slave1.score]
            join_keys={
                "slave1.user_id"="master.id"
                "slave1.score"="master.address"
            }
            join_state="master"
            table_id="20240603"
            mapping_keys={
                "id"="master.id"
                "name"="master.name"
                "address"="master.address"
                "phone"="master.phone"
                "user_id"="slave1.user_id"
                "score"="slave1.score"
            }
      }

}
sink {
    console{
     "source_table_name"="user02_1"
    }
    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3_youh"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
