env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user011"
        "result_table_name"="user02"
    }
}
transform {
    LeftJoin {
            source_table_name = "user02"
            result_table_name = "user02_1"
            fields = [id,name]
            primary_key="id"
            join_keys={
                "master.id"="slave1.id"
                "master.address"="slave1.name"
            }
            join_state="slave1"
            table_id="202406031_li2"
            mapping_keys={
                "slave1.id"="id2"
                "slave1.name"="name2"
            }
          }
      LeftJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,phone,address]
            master_fields=[id1,name1,phone,address,id2,name2]
            add_fields=[slave1.id2, slave1.name2]
            join_keys={
                "slave1.id"="master.id"
                "slave1.name"="master.address"
            }
            join_state="master"
            table_id="202406031_li2"
            mapping_keys={
                "id1"="master.id"
                "name1"="master.name"
                "id2"="slave1.id"
                "name2"="slave1.name"
                "phone"="master.phone"
            }
      }

}
sink {
    console{
     "source_table_name"="user02_1"
    }

    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="left_join_20240715"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
