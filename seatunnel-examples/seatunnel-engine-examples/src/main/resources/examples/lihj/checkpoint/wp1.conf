env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
          url="**************************************************"
          driver="com.mysql.cj.jdbc.Driver"
          user="root"
          password="joyadata"
          query="select * from seatunnel_source.emp_quality_millions_100w "
          "partition_column"="emp_id"
          "partition_num"="5"
          "parallelism"=5
        }




}
transform {
}
sink {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="seatunnel_source"
        table="emp_104_16"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
        }

}
