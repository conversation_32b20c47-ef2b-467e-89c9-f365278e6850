env {
"job.mode"="BATCH"
"job.name"="20"
parallelism=10
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`
        ,`update_time` from `seatunnel_source`.`emp_quality_millions_100w` where emp_id <= 100000"
        "partition_column"="emp_id"
        "partition_num"="10"
        }

}
transform {
}
sink {
   MergeLocalFile {
       path="d://tmp"
       tmp_path="d://tmp1"
       file_format_type="csv"
       field_delimiter=","
       row_delimiter="\n"
       final_name="d://tmp//emp_quality_millions_100w.csv"
   }
}
