env {
"job.mode"="BATCH"
"job.name"="2"
"execution.parallelism"="2"
"checkpoint.interval"="1000"
}
source {
Jdbc {
url="**********************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password=joyadata
query="select `id`,`gmt` from `time` Where 1=1 "
"batch_size"="10"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"partition_column"="id"
"partition_num"="6"
"parallelism"=6
}




}
transform {
}
sink {
LocalFile {
       path="D://tmp"
       tmp_path="D://tmp1"
       file_format_type="text"
       field_delimiter=","
       row_delimiter="\n"
       validate_file="D://tmp//222.txt"
       validate_content="20241024"
   }
}