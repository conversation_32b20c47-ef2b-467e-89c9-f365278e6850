env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
 Jdbc {
 url="****************************************************************************************************************************************************************************************************"
 driver="com.mysql.cj.jdbc.Driver"
 user="root"
 password="joyadata"
 query="select dates,slicr_pk_id,lgin_actno,pwd,user_nm from seatunnel_source.usr_info_1 Where 1=1 "
 "fetch_size"="1000"
 "split.size"="1000"
 "connection_check_timeout_sec"="30"
 "snapshot.split.size"="8096"
 "snapshot.fetch.size"="1024"
 "connection.pool.size"="20"
 "chunk-key.even-distribution.factor.lower-bound"="0.05"
 "chunk-key.even-distribution.factor.upper-bound"="100"
 "sample-sharding.threshold"="1000"
 "inverse-sampling.rate"="1000"
 "startup.mode"="INITIAL"
 "stop.mode"="NEVER"
 "result_table_name"="E000003_source_1"
 "parallelism"=1
 "empty_data_strategy"=false
 }



 }
 transform {
 ReplaceAll{
 source_table_name="E000003_source_1"
 result_table_name="E000003_source_1_trans_2"
     "dates"={
         pattern= ["1994"]
         replacement= ["QQQQQ"]
     }
 }
 }
 sink {
 MergeLocalFile {
 final_name="/home/<USER>/2222.txt"
 path="/home/<USER>"
 source_table_name = "E000003_source_1_trans_2"
 tmp_path="/home/<USER>/tmp"
 file_format_type="csv"
 sink_columns=["dates","slicr_pk_id","lgin_actno","pwd","user_nm"]
 batch_size="1000"
 compress_codec="none"
 overwrite_file="false"
 "empty_data_strategy"=false
 }

 }