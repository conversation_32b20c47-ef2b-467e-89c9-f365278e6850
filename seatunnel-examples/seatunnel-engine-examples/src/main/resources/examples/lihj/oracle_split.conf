env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="************************************************"
        driver="oracle.jdbc.OracleDriver"
            schema="C##ZSP"
            user="c##ZSP"
            password="ZSP"
        query="select \"EMP_ID\",\"EMP_NAME\",\"GENDER\",\"ACCOUNT\",\"ORG_ID\",\"BIRTH_DATE\",\"AGE\",\"NATIONALITY\",\"PROVINCE\",\"CITY\",\"EMAIL\",\"PHONE\",\"BEGIN_DATE\",\"REMARK\",\"CREATE_TIME\",\"UPDATE_TIME\" from \"C##ZSP\".\"EMP_2040809\" Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "partition_column"="EMP_ID"
            "partition_num"="5"
            "parallelism"=5
            "table_path"=1
        }





}
transform {
}
sink {
console{}

}
