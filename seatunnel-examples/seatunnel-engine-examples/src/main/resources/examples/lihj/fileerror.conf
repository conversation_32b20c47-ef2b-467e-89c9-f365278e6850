env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="****************************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_104` Where 1=2 "
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"parallelism"=1
"empty_data_strategy"=true
}




}
transform {
}
sink {
    LocalFile {
    path="/tmp/lihj"
    source_table_name = "E000001_source_1"
    custom_filename=true
    file_name_expression="aa_${transactionId}"
    is_enable_transaction=true
    tmp_path="/tmp/lihj/tmp"
    file_format_type="text"
    field_delimiter="\u0001"
    row_delimiter="\n"
    sink_columns=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
    batch_size="1000"
    compress_codec="none"
    validate_file="/tmp/lihj/********"
    "empty_data_strategy"=true
    }

}