env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    HdfsFile {
    fs.defaultFS="hdfs://192.168.90.113:8020"
    path="/tmp/joyadata/20240528/T_849914308388192257_036c58d4f4_0_1_0.txt"
    file_format_type="text"
    field_delimiter = ","
    schema {
        fields {
          id = int
          name = string
          age = int
          sex = string
          email = string
          email1 = string
          email2 = string
          datatime = date
        }
      }
    }

}
transform {

}
sink {
    Jdbc {
        url="*****************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        database="test"
        table="student_0528"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "generate_sink_sql"="true"
    }
}