env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from `seatunnel_source`.`emp_quality_lihj`"
            "batch_size"="1024"
            result_table_name="mysql1"
            table_path="seatunnel_source.emp_quality_lihj"
        }

}
transform {
  sql {
    source_table_name = "mysql1"
    query = "select * from mysql1"
    result_table_name = "mysql11"
  }
    sql {
      source_table_name = "mysql1"
      query = "select * from mysql1"
      result_table_name = "mysql22"
    }

}
sink {
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_union_key_01"
        "support_upsert_by_query_primary_key_exist"="true"
        "generate_sink_sql"="true"
        "primary_keys" = ["emp_id"]
        "enable_upsert"="true"
        source_table_name="mysql11"
    }
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_union_key_02"
        "support_upsert_by_query_primary_key_exist"="true"
        "generate_sink_sql"="true"
        "primary_keys" = ["emp_id"]
        "enable_upsert"="true"
        source_table_name="mysql22"
    }

}
