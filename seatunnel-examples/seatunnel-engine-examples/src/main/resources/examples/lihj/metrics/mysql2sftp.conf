env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from `seatunnel_source`.`emp_quality_lihj`"
            "batch_size"="1024"
            result_table_name="mysql1"
            table_path="seatunnel_source.emp_quality_lihj"
        }

}
transform {


}
sink {
   SftpFile {
       host = "**************"
       port = 22
       user = root
       password = "joyadata"
       path = "tmp/seatunnel/json"
       row_delimiter = "\n"
       partition_dir_expression = "${k0}=${v0}"
       is_partition_field_write_in_file = true
       file_name_expression = "${transactionId}_${now}"
       file_format_type = "json"
       filename_time_format = "yyyy.MM.dd"
       is_enable_transaction = true
     }

}
