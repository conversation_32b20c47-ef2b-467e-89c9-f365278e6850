env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from `seatunnel_source`.`emp_quality_millions_100w` where emp_id<=10000"
            "batch_size"="1024"
            result_table_name="mysql1"
            table_path="这是源端"
        }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from `seatunnel_source`.`emp_104`"
            "batch_size"="1024"
            result_table_name="mysql2"
            table_path="这是源端104"
        }
}
transform {
}
sink {
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_union_key_01"
         generate_sink_sql = true
        "support_upsert_by_query_primary_key_exist"="true"
        "primary_keys" = ["emp_id"]
        "enable_upsert"="true"
        source_table_name="mysql1"
    }
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_union_key_02"
        generate_sink_sql = true
        "support_upsert_by_query_primary_key_exist"="true"
        "primary_keys" = ["emp_id"]
        "enable_upsert"="true"
        source_table_name="mysql2"
    }

}
