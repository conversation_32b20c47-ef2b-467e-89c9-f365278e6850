# Configuration snippets may be placed in this directory as well
includedir /etc/krb5.conf.d/

[logging]
 default = FILE:/var/log/krb5libs.log
 kdc = FILE:/var/log/krb5kdc.log
 admin_server = FILE:/var/log/kadmind.log

[libdefaults]
 dns_lookup_realm = false
 dns_lookup_kdc = false
 ticket_lifetime = 24h
 #renew_lifetime = 7d
 forwardable = true
 rdns = false
 udp_preference_limit = 1
 default_realm = ADMIN.COM
 #default_ccache_name = KEYRING:persistent:%{uid}
 default_tkt_enctypes = aes256-cts-hmac-sha1-96 aes128-cts-hmac-sha1-96 arcfour-hmac-md5
 default_tgs_enctypes = aes256-cts-hmac-sha1-96 aes128-cts-hmac-sha1-96 arcfour-hmac-md5
 permitted_enctypes   = aes256-cts-hmac-sha1-96 aes128-cts-hmac-sha1-96 arcfour-hmac-md5 

[realms]
 ADMIN.COM = {
  kdc = bigdata-03
  admin_server = bigdata-03
 }

[domain_realm]
  #.admin.com = ADMIN.COM
  bigdata-03 = ADMIN.COM
  bigdata-01 = ADMIN.COM
  #bigdata-02 = ADMIN.COM
  #doris-server-01 = ADMIN.COM
  ADMIN.com = ADMIN.COM