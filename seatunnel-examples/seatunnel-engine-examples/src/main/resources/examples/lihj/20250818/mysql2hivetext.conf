env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password="joyadata"
        query="SELECT `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` FROM `seatunnel_source`.`emp_104` WHERE 1=1 "
        "fetch_size"="1000"
            "result_table_name"="E000003_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }


}
transform {
}
sink {
        HdfsFile {
        fs.defaultFS = "hdfs://bigdata-01:8020"
        path = "d://dsg"
        tmp_path = "d://dsg//tmp"
            source_table_name = "E000003_source_1"
            hdfs_site_path = "D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\********\\hdfs-site.xml"
            custom_filename=true
            file_name_expression="test0818"
            is_enable_transaction=false
            file_format_type = "text"
            field_delimiter = "\u0001"
            row_delimiter = "\n"
            partition_by=null
            sink_columns=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
            krb5_path = "D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\********\\krb5.conf"
            kerberos_principal = "hive/<EMAIL>"
            kerberos_keytab_path = "D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\********\\hive.keytab"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
        }
}