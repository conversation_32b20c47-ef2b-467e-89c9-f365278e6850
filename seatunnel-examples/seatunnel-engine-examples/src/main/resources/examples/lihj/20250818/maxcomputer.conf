env {
"job.mode"="BATCH"
"job.name"="10"
}
source {

        Maxcompute {
            accessId="LTAI5t6QbR82f91qfpr1KgHA"
            accessKey="******************************"
            endpoint="http://service.cn-beijing.maxcompute.aliyun.com/api"
            project="dsg_2024mc"
            table_name="example_simple"
            split_row="10"
            schema= {
                fields {
                    id="STRING"
                    name="STRING"
                    amount="DOUBLE"
                    created_at="DATETIME"
                    is_active="BOOLEAN"
                }
            }
        }

}
transform {
}
sink {
       console{}
}
