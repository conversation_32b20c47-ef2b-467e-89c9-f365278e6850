env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from `seatunnel_source`.`emp_104_1`"
            "batch_size"="1024"
            result_table_name="source1"
        }

}
transform {
    ReplaceAll{
        source_table_name="source1";
        result_table_name="sink1"
        replace_field_patterns={
            remark={
                pattern=["null","a"]
                replacement =["a","2"]
            }
        }
    }

}
sink {
        Jdbc {
         source_table_name="sink1"
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_source"
            table="emp_104_10"
            "generate_sink_sql"="true"
            "support_upsert_by_query_primary_key_exist"="true"
            "generate_sink_sql"="true"
            "primary_keys" = ["emp_id"]
            "enable_upsert"="true"
        }
}
