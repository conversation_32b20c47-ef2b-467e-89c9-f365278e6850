env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user02"
        "result_table_name"="user02"
    }
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user03"
        "result_table_name"="user03"
    }
}
transform {
    InnerJoin {
            source_table_name = "user02"
            result_table_name = "user02_1"
            fields = [user_id, score]
            primary_key="user_id"
            join_keys={
                "master.id"="slave1.user_id"
                "master.name"="slave1.score"
            }
            join_state="slave1"
            table_id="20240603"
          }
    InnerJoin {
          source_table_name = "user03"
          result_table_name = "user03_1"
          fields = [user_cid, money]
          primary_key="user_cid"
          join_keys={
              "master.id"="slave2.user_cid"
          }
          join_state="slave2"
          table_id="20240603"
        }
      InnerJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,address,phone]
            master_fields=[id,name,address,phone,user_id, score,user_cid,money]
            add_fields=[slave1.user_id, slave1.score,slave2.user_cid,slave2.money]
            join_keys={
                "slave1.user_id"="master.id"
                "slave1.score"="master.name"
                "slave2.user_cid"="master.id"
            }
            join_state="master"
            table_id="20240603"
      }

}
sink {
    console{
     "source_table_name"="user02_1"
    }
    console{
     "source_table_name"="user03_1"
    }

    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3_youh"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
