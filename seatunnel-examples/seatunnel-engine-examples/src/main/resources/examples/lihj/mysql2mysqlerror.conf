env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="**********************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
query="select `id`,`name`,`time_dt` from `seatunnel_source`.`t_uesr` Where 1=1 and time_dt >'1900-01-01 01:01:01' and time_dt <='2024-09-11 12:12:12'"
"batch_size"="1000"
"split.size"="1000"
"table_path"="`t_uesr`"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connect.timeout.ms"="30000"
"connect.max-retries"="3"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000001_source_1"
"partition_column"="time_dt"
"partition_num"="5"
"parallelism"=1
}



}
transform {
}
sink {
Jdbc {
url="********************************************************************************************************************************************************************************************"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="joyadata"
database="seatunnel_sink"
table="dai_test_user"
"generate_sink_sql"="true"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"max_retries"="1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="E000001_source_1"
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
}

}