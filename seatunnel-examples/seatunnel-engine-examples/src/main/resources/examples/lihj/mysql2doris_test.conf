env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from doris_test"
        "batch_size"="1024"
        result_table_name="all_data"
    }
}
transform {
    sql{
        source_table_name="all_data"
        result_table_name="f10"
        query="select * from all_data where partition_value < '2024-06-11'";
    }
    sql{
            source_table_name="all_data"
            result_table_name="f11"
            query="select * from all_data where partition_value = '2024-06-11'";
    }
}
sink {
  Doris {
  source_table_name="f10"
    fenodes = "192.168.90.221:8030"
    username = root
    password = ""
    database = "test"
    table = "table_partition_range"
    sink.enable-2pc = "true"
    sink.label-prefix = "test_json"
    doris.config {
      format = "json"
      read_json_by_line = "true"
    }
  }
    Doris {
      source_table_name="f11"
      fenodes = "192.168.90.221:8030"
      username = root
      password = ""
      database = "test"
      table = "table_partition_range"
      sink.enable-2pc = "true"
      sink.label-prefix = "test_json1"
      doris.config {
        format = "json"
        read_json_by_line = "true"
      }
    }
}
