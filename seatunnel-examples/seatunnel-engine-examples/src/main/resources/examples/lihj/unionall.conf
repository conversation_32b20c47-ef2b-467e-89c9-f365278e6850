env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select user_id,score from user02"
            "result_table_name"="user02"
        }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select user_cid,money from user03"
            "result_table_name"="user03"
        }
}
transform {
    FieldMapper  {
         source_table_name = "user02"
         result_table_name = "user02_1"
         field_mapper  {
            user_id = id
            score=score
         }
    }
    FieldMapper  {
         source_table_name = "user03"
         result_table_name = "user03_1"
         field_mapper  {
            user_cid = id
            money=money
         }
    }
}
sink {
    Jdbc {
        "source_table_name"="user01"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
    Jdbc {
        "source_table_name"="user02_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
    Jdbc {
        "source_table_name"="user03_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
}
