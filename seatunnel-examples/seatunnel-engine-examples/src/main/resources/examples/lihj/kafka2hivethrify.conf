env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
    Kafka{
         bootstrap.servers = "192.168.90.104:9092"
         topic="kafkawriterlihj"
         consumer.group="dsg_20240518"
         format="text"
         field_delimiter=","
         start_mode="earliest"
         result_table_name="fake"
         schema {
            fields{
                emp_id="string"
                emp_name="string"
                gender="string"
                account="string"
                org_id="string"
                birth_date="string"
                age="string"
                nationality="string"
                province="string"
                city="string"
                email="string"
                phone="string"
                begin_date="string"
                remark="string"
                create_time="string"
                update_time="string"
            }
         }
    }

}
transform {
}
sink {
  Hive {
        table_name= "default.emp_20240518"
        metastore_uri= "thrift://192.168.90.113:9083"
    }
}