env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from user022"
            "result_table_name"="user02"
        }
}
transform {
    InnerJoin {
        source_table_name = "user02"
        result_table_name = "user02_1"
        fields = [user_id, score]
        primary_key="id"
        join_keys={
            id="user_id"
            name="chinese"
        }
        join_state="slave"
        table_id="20240605"
      }
      InnerJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,address,phone]
            master_fields=[id,name,address,phone,user_id, score]
            add_fields=[user_id, score]
            join_keys={
                id="user_id"
                name="chinese"
            }
            join_state="master"
            table_id="20240605"
      }
}
sink {
    console{
     "source_table_name"="user02_1"
    }

    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
