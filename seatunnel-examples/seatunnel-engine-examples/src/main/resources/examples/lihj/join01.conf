{
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name,address,phone from user01 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="user01_source_1"
            "parallelism"=1
        }
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select user_id,score,maths,chinese,english from user02 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="user02_source_2"
            "parallelism"=1
        }
        Jdbc {
        url="*************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,rule_id,rule_name,rule_display,rule_props_value,rule_explain,rule_type_id,del_flag,remark,create_dept_id,create_user_id,update_time,tenant_id,project_id,create_by,create_time,update_by from student0 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="student0_source_3"
            "parallelism"=1
        }
}
transform {
        LeftJoin{
            source_table_name="user02_source_2"
            result_table_name="user02_source_2_trans_7"
                    fields = ["user_id","score","maths","chinese","english"]
                    primary_key = "user_id"
                    join_keys={
                        id=user_id
                    }
                    join_state = "slave"
                    table_id = "13986085332608"
        }
        LeftJoin{
            source_table_name="student0_source_3"
            result_table_name="student0_source_3_trans_8"
                    fields = ["id","rule_id","rule_name","rule_display","rule_props_value","rule_explain","rule_type_id","del_flag","remark","create_dept_id","create_user_id","update_time","tenant_id","project_id","create_by","create_time","update_by"]
                    primary_key = "id"
                    join_keys={
                        id=id
                    }
                    join_state = "slave"
                    table_id = "13986085332608"
        }
        LeftJoin{
            source_table_name="user01_source_1"
            result_table_name="user01_source_1_trans_4"
                    fields = ["id","name","address","phone"]
                    join_keys={
                        id=id
                    }
                    join_state = "master"
                    table_id = "13986085332608"
                    master_fields = ["id","name","address","phone","user_id","score","maths","chinese","english","id","rule_id","rule_name","rule_display","rule_props_value","rule_explain","rule_type_id","del_flag","remark","create_dept_id","create_user_id","update_time","tenant_id","project_id","create_by","create_time","update_by"]
                    add_fields = ["user_id","score","maths","chinese","english","id","rule_id","rule_name","rule_display","rule_props_value","rule_explain","rule_type_id","del_flag","remark","create_dept_id","create_user_id","update_time","tenant_id","project_id","create_by","create_time","update_by"]
        }
}
sink {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="seatunnel_source"
        table="user_join_01"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="user01_source_1_trans_4"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
        ConsoleHole {
            source_table_name="user02_source_2_trans_7"
        }
        ConsoleHole {
            source_table_name="student0_source_3_trans_8"
        }
}