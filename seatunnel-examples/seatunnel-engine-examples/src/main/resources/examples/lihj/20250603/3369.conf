env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Doris {
        fenodes="192.168.100.12:8030"
        username="root"
        password="Cdyanfa_123456"
        database="test"
        table="employee"
            query-port="9030"
            result_table_name="doris_source_1"
            doris.filter.query=" 1 = 1 "
            doris.batch.size="1024"
            doris.request.read.timeout.ms="30000"
            doris.request.connect.timeout.ms=30000
            "parallelism"=1
        }





}
transform {
}
sink {
        Jdbc {
        url="******************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        database="ykw"
        table="employee_doris250529"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="doris_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "partition_keys"=[]
        }

}