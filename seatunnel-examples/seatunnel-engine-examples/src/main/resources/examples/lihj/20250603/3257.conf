env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
 LocalFile {
 path="d://plxz7.txt"
 result_table_name = "localfile_source_1"
 file_format_type="text"
 field_delimiter=","
 parse_partition_from_path=true
 date_format="yyyy-MM-dd"
 datetime_format="yyyy-MM-dd HH:mm:ss"
 read_columns = [col_numeric, col_time,col_timestamp,col_year]
 time_format="HH:mm:ss"
     schema= {
         fields {
             col_bigint=BIGINT
             col_bigint_unsigned=BIGINT
             col_int=INT
             col_int_unsigned=INT
             col_integer=INT
             col_integer_unsigned=INT
             col_mediumint=INT
             col_mediumint_unsigned=INT
             col_smallint=SMALLINT
             col_smallint_unsigned=SMALLINT
             col_tinyint=TINYINT
             col_tinyint_unsigned=TINYINT
             col_double=DOUBLE
             col_double_precision=DOUBLE
             col_double_unsigned=DOUBLE
             col_float=FLOAT
             col_float_unsigned=FLOAT
             col_real=FLOAT
             col_numeric=DECIMAL
             col_char=STRING
             col_varchar=STRING
             col_long_varchar=STRING
             col_text=STRING
             col_mediumtext=STRING
             col_longtext=STRING
             col_tinytext=STRING
             col_binary=BLOB
             col_varbinary=BLOB
             col_long_varbinary=BLOB
             col_blob=BLOB
             col_mediumblob=BLOB
             col_longblob=BLOB
             col_tinyblob=BLOB
             col_bit=BIT
             col_bool=BIT
             col_enum=STRING
             col_set=STRING
             col_json=STRING
             col_date=DATE
             col_datetime=DATETIME
             col_time=TIME
             col_timestamp=DATETIME
             col_year=DATE
         }
     }
 }
 }
 transform {
     FieldMapper{
     source_table_name="localfile_source_1"
     result_table_name="localfile_source_1_trans_2"
     field_mapper={
     "col_numeric"="col_numeric"
     "col_time"="col_time"
     "col_timestamp"="col_timestamp"
     "col_year"="col_year"
     }
     }
 }
 sink {
    console{}

 }