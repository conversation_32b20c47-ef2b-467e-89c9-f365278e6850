env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from doris_test"
        "batch_size"="1024"
        result_table_name="all_data"
    }
}
transform {
    sql{
            source_table_name="all_data"
            result_table_name="f11"
            query="select * from all_data where partition_value = '2024-06-11'";
    }
}
sink {
    Console{
        source_table_name="f11"
    }
}
