env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
    url="************************************************"
       driver="oracle.jdbc.driver.OracleDriver"
       user="c##ZSP"
       password="ZSP"
    query="select * from C##ZSP.EMP_2040808 where EMP_ID<=10000"
    "batch_size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connect.timeout.ms"="30000"
    "connect.max-retries"="3"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="E000003_source_1"
    "parallelism"=1
    }




}
transform {
}
sink {
    Jdbc {
    url="************************************************"
    driver="oracle.jdbc.driver.OracleDriver"
    user="c##ZSP"
    password="ZSP"
    database="ORCLCDB"
    table="C##ZSP.EMP_2040809"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "max_commit_attempts"="3"
    "transaction_timeout_sec"="-1"
    "max_retries"="0"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="E000003_source_1"
    "generate_sink_sql"="true"
    "primary_keys" = ["EMP_ID","BEGIN_DATE"]
    "field_ide"=UPPERCASE
    "enable_upsert"="true"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        "pk_strategy"="stop"
    "conflict_strategy_row"=delete_conflicting_before_inserting_rows

    }

}