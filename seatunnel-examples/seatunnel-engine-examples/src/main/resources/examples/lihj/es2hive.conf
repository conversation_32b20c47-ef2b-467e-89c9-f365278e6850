env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
        Elasticsearch {
            result_table_name = "testsyyang_source_1"
            hosts = ["**************:9200"]
            index = "testsyyang"
            username = "elastic"
            password = "changeme"
            source = [

                    searchFields
                    ,
                    status
                    ,
                    callTime
                    ,
                    businessType
                    ,
                    finalDecisionName
                    ,
                    policyUuid
                    ,
                    outputFields
                    ,
                    bizTime
                    ,
                    eventType
                    ,
                    tokenId
                    ,
                    cost
                    ,
                    finalScore
                    ,
                    statDate
                    ,
                    invokeType
                    ,
                    reasonCode
                    ,
                    contextFields
                    ,
                    bizId
                    ,
                    bizModel
                    ,
                    orgCode
                    ,
                    finalDecisionCode
                    ,
                    extensionParams
                    ,
                    policyMode
                    ,
                    policyCode
                    ,
                    appCode
                    ,
                    ruleSetResult
                    ,
                    requestParams
                    ,
                    reasonMessage
                    ,
                    serviceCode
                    ,
                    policyVersion
            ]
            scroll_size = "100"
        }

}
transform {
            FieldMapper{
                source_table_name="testsyyang_source_1"
                result_table_name="testsyyang_source_1_trans_2"
                        field_mapper={
                            policyVersion=id
                            reasonMessage=text_column
                        }
            }
}
sink {
    Hive {
        source_table_name = "testsyyang_source_1_trans_2"
        table_name="default.es2hive"
        metastore_uri="thrift://192.168.90.113:9083"
  }

}