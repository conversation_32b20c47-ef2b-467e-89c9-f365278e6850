env {
    "job.mode"="STREAMING"
    "job.name"="10"
}
source {


    MySQL-CDC {
        result_table_name = "MySQL_cdc_yang_kw"
        username = "root"
        password = Cdyanfa_123456
        table-names = ["test.yang_kw"]
        base-url = "*************************************"
        server-time-zone = "GMT+08"
    }
}
transform {
}
sink {
    Jdbc {
        url="***********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="joyadata"
            password="joyadata"
        database="HELOWIN"
        table="JOYADATA.YANG_KW_001"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="MySQL_cdc_yang_kw"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
    }
}