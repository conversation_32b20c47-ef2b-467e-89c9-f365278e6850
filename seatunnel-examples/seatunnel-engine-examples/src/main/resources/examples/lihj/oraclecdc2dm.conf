env {
    "job.mode"="STREAMING"
    "job.name"="10"
}
source {

    Oracle-CDC {
        result_table_name = "Oracle_cdc_TEST_MYSQL_OUT2"
        username = "joyadata"
        password = joyadata
        database-names = ["HELOWIN"]
        schema-name = ["JOYADATA"]
        "startup.mode"="initial"
        table-names = ["HELOWIN.JOYADATA.TEST_MYSQL_OUT2"]
        base-url = "***********************************************"
    }



}
transform {
}
sink {
    Jdbc {
        url="jdbc:dm://192.168.90.104:30236"
        driver="dm.jdbc.driver.DmDriver"
            user="SYSDBA"
            password="SYSDBA001"
        database="SYSDBA"
        table="SYSDBA.WWNN"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="Oracle_cdc_TEST_MYSQL_OUT2"
        "generate_sink_sql"="true"
    }
   }