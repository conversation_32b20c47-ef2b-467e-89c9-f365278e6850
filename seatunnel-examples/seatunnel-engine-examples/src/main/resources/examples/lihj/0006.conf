env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="jdbc:impala://***********:21050/default;AuthMech=0;KrbRealm={KrbRealm};KrbHostFQDN={KrbHostFQDN};KrbServiceName=impala"
        driver="com.cloudera.impala.jdbc41.Driver"
        user="impala"
        password=""
        query="select org_code,insu_obj,org_level1_code,org_level1_name,org_level2_code,org_level2_name,org_level3_code,org_level3_name,org_level4_code,org_level4_name,org_name,data_load_dt from t_d_org_view_20221122 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="t_d_org_view_20221122_source_1"
            "parallelism"=1
        }
}
transform {
}
sink {
        Jdbc {
        url="***********************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="dsg"
            password="dsg@123"
        database="test1"
        table="t_d_org_view_20240619"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="t_d_org_view_20221122_source_1"
        "generate_sink_sql"="true"
            "primary_keys" = ["insu_obj"]
            "enable_upsert"="true"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}