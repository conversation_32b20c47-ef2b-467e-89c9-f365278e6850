env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
          url="************************************************"
          driver="com.mysql.cj.jdbc.Driver"
          user="root"
          password="joyadata"
          query="select * from lihj_20250324 "
        }
}
transform {
}
sink {
    MergeLocalFile {
    path = "d://tmp//hive//warehouse//test1"
    final_name="D:\\opt\\zsp\\file\\zsp_hebing.txt"
    field_delimiter = "\t"
    row_delimiter = "\n"
    file_format_type = "json"
    "encoding"="GBK"
  }
}