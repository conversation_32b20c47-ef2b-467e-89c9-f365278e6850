env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_104` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }

}
transform {
}
sink {
        HdfsFile {
        fs.defaultFS = "hdfs://192.168.90.113:8020"
        path = "/tmp/lihj/hdfsfile"
        tmp_path = "/tmp/lihj/hdfsfile/tmp"
            source_table_name = "E000001_source_1"
            custom_filename=true
            file_name_expression=""
            is_enable_transaction=false
            file_format_type = "parquet"
            partition_by=null
            sink_columns=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
        }

}
