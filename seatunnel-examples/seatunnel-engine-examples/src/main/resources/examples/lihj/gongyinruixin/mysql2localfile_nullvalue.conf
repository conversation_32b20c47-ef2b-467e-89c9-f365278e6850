env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
          url="************************************************"
          driver="com.mysql.cj.jdbc.Driver"
          user="root"
          password="joyadata"
          query="select * from lihj_20250324 "
        }
}
transform {
}
sink {
    LocalFile {
    path = "d://tmp//hive//warehouse//test1"
    field_delimiter = "\t"
    row_delimiter = "\n"
    file_format_type = "json"
    "encoding"="GBK"
    "null_to_value"="11111111"
  }
}