 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="***************************************"
        driver="com.alipay.oceanbase.jdbc.Driver"
            schema="DSG"
            user="sys@ob_oracle"
            password="DSGdata@123#"
        query="select \"CUSTOMER_ID\",\"NAME\",\"EMAIL\",\"CREATED_AT\" from \"DSG\".\"CUSTOMERS\" Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "compatible_mode"=oracle
            "table_path"=1
        }





}
transform {
}
sink {
        Jdbc {
        url="***************************************"
        driver="com.alipay.oceanbase.jdbc.Driver"
            user="sys@ob_oracle"
            password="DSGdata@123#"
        database="DSG"
        table="DSG.TEST001"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "compatible_mode"=oracle
            "partition_keys"=[]
        }

}