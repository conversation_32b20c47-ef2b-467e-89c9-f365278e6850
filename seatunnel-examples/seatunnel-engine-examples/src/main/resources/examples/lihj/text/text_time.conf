env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        LocalFile {
        path="D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\text\\file\\4.txt"
            result_table_name = "localfile_source_1"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                user_field8=DECIMAL
                user_field9=DATE
                user_field10=TIME
                user_field11=TIMESTAMP
            }
            }
            compress_codec=none
            encoding=UTF-8
        }
}
transform {
}
sink {
        Jdbc {
        url="*****************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
        database="bj"
        table="aaa2_plxz_20252522"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="localfile_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "partition_keys"=[]
        }

}
