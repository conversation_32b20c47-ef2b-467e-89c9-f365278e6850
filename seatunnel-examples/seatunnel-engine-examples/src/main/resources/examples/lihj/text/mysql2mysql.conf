env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            schema="ds_lihj_string"
            user="lihj"
            password="joyadata\"123"
        query="select `id`,`user_name`,`user_password`,`user_type`,`email`,`phone`,`tenant_id`,`create_time`,`update_time`,`queue`,`state`,`time_zone` from `ds_lihj_string`.`t_ds_user` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "table_path"=1
        }





}
transform {
}
sink {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="lihj"
            password="joyadata\"123"
        database="seatunnel_source"
        table="t_ds_user_20250522"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "partition_keys"=[]
        }

}
