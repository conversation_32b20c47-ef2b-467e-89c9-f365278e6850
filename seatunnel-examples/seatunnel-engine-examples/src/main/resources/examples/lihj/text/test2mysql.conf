env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
LocalFile {
    path = "d://111.txt"
    file_format_type = "text"
    field_delimiter=","
    schema = {
      fields {
        a1 = "map<string, string>"
        a2 = "array<string>"
        a3 = string
      }
    }
  }
}
transform {
}
sink {
Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_source"
        table="text2mysql"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="user01_source_1_trans_3_trans_7"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}