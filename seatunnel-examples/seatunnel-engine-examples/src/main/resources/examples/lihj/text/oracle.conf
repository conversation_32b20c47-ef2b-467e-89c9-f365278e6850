 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
    url="************************************************"
    driver="oracle.jdbc.driver.OracleDriver"
    user="c##BJ"
    password="BJ"
    query="select \"DEPTNO\",\"DNAME\",\"LOC\" from \"C##BJ\".\"DEPT\" Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="E000001_source_1"
    "parallelism"=1
    "table_path"=1
    }





}
transform {
}
sink {
       console{}

}