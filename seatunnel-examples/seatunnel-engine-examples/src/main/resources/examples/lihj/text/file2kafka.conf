env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    LocalFile {
        path="D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\text\\file\\4.txt"
            result_table_name = "localfile_source_1"
        file_format_type="text"
            field_delimiter=","
            parse_partition_from_path=true
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            schema= {
            fields {
                user_field8=String
                user_field9=String
                user_field10=String
                user_field11=String
            }
            }
            compress_codec=none
            encoding=UTF-8
        }
}
transform {
}
sink {
    kafka {
      topic = "kafkawriterlihj_json4"
      bootstrap.servers = "127.0.0.1:9092"
      format = AVRO
      field_delimiter=","
  }
}