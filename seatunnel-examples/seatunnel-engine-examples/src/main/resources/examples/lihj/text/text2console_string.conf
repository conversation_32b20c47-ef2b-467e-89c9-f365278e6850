env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
LocalFile {
    path = "D:\\DSG\\git_repo\\scheduler\\seatunnel\\seatunnel-examples\\seatunnel-engine-examples\\src\\main\\resources\\examples\\lihj\\text\\file\\3.txt"
    file_format_type = "csv"
    field_delimiter=","
    schema = {
      fields {
        a1 = "int"
        a2 = "bigint"
        a3 = string
        a4 = short
        a5 = boolean
        a6 = double
        a7 = float
      }
    }
  }
}
transform {
}
sink {
console{}
}