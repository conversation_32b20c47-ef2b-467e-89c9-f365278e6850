env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    Hive {
        table_name="default.mytable_text"
        metastore_uri="thrift://**************:9083"
        read_columns=[id,text_column]
  }

}
transform {
}
sink {
  Elasticsearch {
    hosts = ["**************:9200"]
    tls_verify_certificate = false
    tls_verify_hostname = false
    index = "20240409"
    index_type = "xone"
    "schema_save_mode"="CREATE_SCHEMA_WHEN_NOT_EXIST"
    "data_save_mode"="APPEND_DATA"
    username = "elastic"
    password = "changeme"
  }
}