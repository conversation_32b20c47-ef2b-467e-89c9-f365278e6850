env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="******************************************************"
        driver="com.microsoft.sqlserver.jdbc.SQLServerDriver"
        user="sa"
        password="Cdyanfa@123456"
        query="select id,name,gender,birth_date,id_number,phone_number,email,home_address,marital_status,education,major,graduate_school,work_experience,position,company_name,join_date,leave_date,monthly_salary,social_security_number,emergency_contact,emergency_contact_phone,is_party_member,health_status,height,weight,blood_type,household_registration_address,current_address,hobbies,remarks,is_employed,employee_number,department,work_location,bank_account_number,bank_name,skills,certificates,social_media_account,created_at,updated_at from test.personnel_information_1w_mysql Where 1=1 "
        "batch_size"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "parallelism"=1
        }
}

transform {
}
sink {
        Jdbc {
        url="******************************************************"
        driver="com.microsoft.sqlserver.jdbc.SQLServerDriver"
            user="sa"
            password="Cdyanfa@123456"
        database="qjq"
        table="test.personnel_information_1w_mysql_zsp"
        "support_upsert_by_query_primary_key_exist"="false"
        "generate_sink_sql"="true"
            "field_ide"=LOWERCASE
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}