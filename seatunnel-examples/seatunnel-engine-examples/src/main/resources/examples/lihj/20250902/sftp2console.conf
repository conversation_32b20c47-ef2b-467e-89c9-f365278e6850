env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        FtpFile {
        host="**************"
        port="21"
        user="dgr"
        password="joyadata"
        path="/home/<USER>/demo.txt"
        file_format_type="text"
            result_table_name="ftp_source_1"
            connection_mode="active_local"
            field_delimiter="/u0001"
            parse_partition_from_path="true"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            skip_header_row_number=0
            compress_codec="none"
            schema= {
            fields {
                statistic_dt="DATE"
                branch_org_cd="STRING"
                branch_org_cn_abbr="STRING"
                org_cd="STRING"
                org_cn_abbr="STRING"
                cust_manager_no="STRING"
                cust_manager_nm="STRING"
                dep_loan_flag="STRING"
                bizline_nm="STRING"
                cust_no="STRING"
                cust_nm="STRING"
                entp_scale="STRING"
                entp_nature="STRING"
                indsty_type_cd="STRING"
                indsty_type="STRING"
                listed_company_flag="STRING"
                subj_cn_nm="STRING"
                twelve_level_class="STRING"
                acct_open_dt="STRING"
                loan_issue_dt="STRING"
                due_dt="STRING"
                acct_stat="STRING"
                str_ratio_pct="DECIMAL(10,0)"
                cust_qty="INT"
                active_dep_qty="INT"
                loan_qty="INT"
                today_bal="DECIMAL(10,0)"
                y_avg_bal="DECIMAL(10,0)"
                today_bal_add="DECIMAL(10,0)"
                y_avg_bal_add="DECIMAL(10,0)"
                today_bal_add_ld="DECIMAL(10,0)"
                today_bal_add_lm="DECIMAL(10,0)"
                real_rate="DECIMAL(10,0)"
                fpt_rate="DECIMAL(10,0)"
                min_int="DECIMAL(10,0)"
                XW="STRING"
                YDH="STRING"
                CYL="STRING"
                jnczz="STRING"
                l_m_min_int="DECIMAL(10,0)"
                l_y_min_int="DECIMAL(10,0)"
            }
            }
        }


}
transform {
}
sink {
      console{}
}
