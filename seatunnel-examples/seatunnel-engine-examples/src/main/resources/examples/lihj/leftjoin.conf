env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
            query="select * from user022"
            "result_table_name"="user02"
        }
}
transform {
    LeftJoin {
        source_table_name = "user02"
        result_table_name = "user02_1"
        fields = [user_id, score,cdate]
        primary_key="id"
        join_keys={
            id="user_id"
            name="chinese"
        }
        join_state="slave"
        table_id="20240603"
      }
      LeftJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,address,phone]
            master_fields=[id,name,address,phone,user_id, score,cdate]
            add_fields=[user_id, score,cdate]
            join_keys={
                id="user_id"
                name="chinese"
            }
            join_state="master"
            table_id="20240603"
      }
}
sink {
    console{
     "source_table_name"="user02_1"
    }
    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
