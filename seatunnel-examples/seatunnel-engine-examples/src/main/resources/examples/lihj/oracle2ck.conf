env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="***********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="joyadata"
        password="joyadata"
        query="select EMPNO,ENAME,JOB,MGR,HIREDATE,SAL,COMM,DEPTNO from EMP Where 1=1 "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="EMP_source_1"
        "parallelism"=2
    }
}
transform {
            FieldMapper{
                source_table_name="EMP_source_1"
                result_table_name="EMP_source_1_trans_2"
                        field_mapper={
                            ENAME=ENAME
                            COMM=COMM
                            HIREDATE=HIREDATE
                            EMPNO=EMPNO
                            MGR=MGR
                            JOB=JOB
                            DEPTNO=DEPTNO
                            SAL=SAL
                        }
            }
}
sink {
    Clickhouse {
        source_table_name = "EMP_source_1_trans_2"
        host = "**************:8123"
        database = "default"
        table = "ykw001"
        username = "default"
        password = "joyadata"
        support_upsert = false
        "field_ide"="lowercase"
    }
}