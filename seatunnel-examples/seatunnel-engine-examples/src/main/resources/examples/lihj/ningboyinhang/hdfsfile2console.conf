env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        HdfsFile {
        path = "/dsg/app/zsp/file/zsp0901_0.orc"
        file_format_type = "orc"
        fs.defaultFS = "hdfs://192.168.90.113:8020"
        result_table_name = "E000001_source_1"
        field_delimiter=","
        date_format="yyyy-MM-dd"
        datetime_format="yyyy-MM-dd HH:mm:ss"
        time_format="HH:mm:ss"
        remote_user="hive"
        }
}
transform {
}
sink {
      console{}
}