env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
    Kafka{
         bootstrap.servers = "**************:9092"
         topic="lihj20240606"
         consumer.group="dsg_20240518"
         format="json"
         start_mode="earliest"
         result_table_name="source01"
    }

}
transform {
    JsonPath{
        source_table_name = "source01"
        result_table_name = "source02"
        columns=[
            {
                "src_field"="content"
                path="$.tag"
                "dest_field" = "tag"
            },{
                "src_field"="content"
                path="$.appname"
                "dest_field" = "appname"
           }
        ]
    }
    SQL{
        source_table_name = "source02"
        result_table_name = "source02_1"
        query ="select * from source02 where tag like '%huawei_switch%' and appname='snmp'"
    }
    SQL{
        source_table_name = "source02"
        result_table_name = "source03_1"
        query ="select * from source02 where tag like '%huawei_S12700%' and appname='switch'"
    }

    JsonPath{
            source_table_name = "source02_1"
            result_table_name = "source02_1_1"
            columns=[
                {
                    "src_field"="content"
                    path="$.agent_send_timestamp"
                    "dest_field" = "agent_send_timestamp"
                },{
                    "src_field"="content"
                    path="$.collector_recv_timestamp"
                    "dest_field" = "collector_recv_timestamp"
               },{
                                   "src_field"="content"
                                   path="$.raw_message"
                                   "dest_field" = "raw_message"
                },{
                                   "src_field"="content"
                                   path="$.ip"
                                   "dest_field" = "ip"
               },{
                                   "src_field"="content"
                                   path="$.index"
                                   "dest_field" = "index"
               },{
                                   "src_field"="content"
                                   path="$.logical_index"
                                   "dest_field" = "logical_index"
               },{
                                   "src_field"="content"
                                   path="$.logtype"
                                   "dest_field" = "logtype"
               },{
                                   "src_field"="content"
                                   path="$.hostname"
                                   "dest_field" = "hostname"
               },{
                                   "src_field"="content"
                                   path="$.domain"
                                   "dest_field" = "domain"
               },{
                                   "src_field"="content"
                                   path="$.appname"
                                   "dest_field" = "appname"
               },{
                                   "src_field"="content"
                                   path="$.context_id"
                                   "dest_field" = "context_id"
               },{
                                   "src_field"="content"
                                   path="$.tag"
                                   "dest_field" = "tag"
               },{
                                   "src_field"="content"
                                   path="$.id"
                                   "dest_field" = "id"
               },{
                                   "src_field"="content"
                                   path="$.raw_message_length"
                                   "dest_field" = "raw_message_length"
               },{
                                   "src_field"="content"
                                   path="$.timestamp"
                                   "dest_field" = "timestamp"
               }
            ]
    }
    JsonPath{
            source_table_name = "source03_1"
            result_table_name = "source03_1_1"
            columns=[
                {
                    "src_field"="content"
                    path="$._index"
                    "dest_field" = "_index"
                },{
                    "src_field"="content"
                    path="$.raw_message"
                    "dest_field" = "raw_message"
               },{
                                   "src_field"="content"
                                   path="$.source"
                                   "dest_field" = "source"
                },{
                                   "src_field"="content"
                                   path="$['switch.sw_name']"
                                   "dest_field" = "switch.sw_name"
               },{
                                   "src_field"="content"
                                   path="$.hostname"
                                   "dest_field" = "hostname"
               },{
                                   "src_field"="content"
                                   path="$.logtype"
                                   "dest_field" = "logtype"
               },{
                                   "src_field"="content"
                                   path="$.appname"
                                   "dest_field" = "appname"
               },{
                                   "src_field"="content"
                                   path="$['switch.count']"
                                   "dest_field" = "switch.count"
               },{
                                   "src_field"="content"
                                   path="$['switch.pri']"
                                   "dest_field" = "switch.pri"
               },{
                                   "src_field"="content"
                                   path="$.host"
                                   "dest_field" = "host"
               },{
                                   "src_field"="content"
                                   path="$['switch.kvmsg.SourceIP']"
                                   "dest_field" = "switch.kvmsg.SourceIP"
               },{
                                   "src_field"="content"
                                   path="$.tag"
                                   "dest_field" = "tag"
               },{
                                   "src_field"="content"
                                   path="$.timestamp"
                                   "dest_field" = "timestamp"
               },{
                                   "src_field"="content"
                                   path="$.agent_send_timestamp"
                                   "dest_field" = "agent_send_timestamp"
               },{
                                   "src_field"="content"
                                   path="$['switch.severity']"
                                   "dest_field" = "switch.severity"
               },{
                                   "src_field"="content"
                                   path="$.collector_recv_timestamp"
                                   "dest_field" = "collector_recv_timestamp"
               },{
                                   "src_field"="content"
                                   path="$.ip"
                                   "dest_field" = "ip"
               },{
                                   "src_field"="content"
                                   path="$['switch.describe']"
                                   "dest_field" = "switch.describe"
               },{
                                   "src_field"="content"
                                   path="$['switch.level']"
                                   "dest_field" = "switch.level"
               },{
                                   "src_field"="content"
                                   path="$['switch.brief']"
                                   "dest_field" = "switch.brief"
               },{
                                   "src_field"="content"
                                   path="$['switch.module']"
                                   "dest_field" = "switch.module"
               },{
                                   "src_field"="content"
                                   path="$.context_id"
                                   "dest_field" = "context_id"
               },{
                                   "src_field"="content"
                                   path="$._id"
                                   "dest_field" = "_id"
               },{
                                   "src_field"="content"
                                   path="$['switch.logtag']"
                                   "dest_field" = "switch.logtag"
               },{
                                   "src_field"="content"
                                   path="$.ip_addr"
                                   "dest_field" = "ip_addr"
               },{
                                   "src_field"="content"
                                   path="$.event_time"
                                   "dest_field" = "event_time"
               }
            ]
    }
}
sink {
    Jdbc {
     source_table_name = "source02_1_1"
        url="jdbc:mysql://**************:13306/seatunnel_sink?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="02"
        "generate_sink_sql"="true"
    }
        Jdbc {
         source_table_name = "source03_1_1"
            url="jdbc:mysql://**************:13306/seatunnel_sink?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="01"
            "generate_sink_sql"="true"
        }
}