env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name,address,phone from user01 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="user01_source_1"
            "parallelism"=1
        }
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select user_id,score,maths,chinese,english from user02 Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="user02_source_2"
            "parallelism"=1
        }

}

transform {
        LeftJoin{
            source_table_name="user02_source_2"
            result_table_name="user01_source_1_trans_3_trans_5"
                    fields = ["user_id","score","maths","chinese","english"]
                    primary_key = "user_id"
                    join_keys={
                        id=user_id
                    }
                    join_state = "slave"
                    table_id = "13922014416896"
        }
        LeftJoin{
            source_table_name="user01_source_1"
            result_table_name="user01_source_1_trans_3"
                    fields = ["id","name","address","phone"]
                    join_keys={
                        id=user_id
                    }
                    join_state = "master"
                    table_id = "13922014416896"
                    master_fields = ["id","name","address","phone","user_id","score","maths","chinese","english"]
                    add_fields = ["user_id","score","maths","chinese","english"]
        }
        FieldMapper{
            source_table_name="user01_source_1_trans_3"
            result_table_name="user01_source_1_trans_3_trans_7"
                    field_mapper={
                        score=score
                        id=id
                        user_id=user_id
                    }
        }
}
sink {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
        database="seatunnel_source"
        table="user_join_01"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="user01_source_1_trans_3_trans_7"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
        ConsoleHole {
            source_table_name="user01_source_1_trans_3_trans_5"
        }
}