env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name` from `seatunnel_source`.`emp_quality`"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        }

}
transform {
}
sink {
    Jdbc {
        url="******************************************"
        driver="com.cloudera.impala.jdbc41.Driver"
        user=""
        password=""
        database="default"
        table="emp_02"
        support_upsert_by_query_primary_key_exist="false"
        generate_sink_sql="true"
        enable_upsert="false"
    }
}