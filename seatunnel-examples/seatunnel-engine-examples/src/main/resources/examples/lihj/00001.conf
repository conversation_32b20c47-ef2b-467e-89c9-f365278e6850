env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
 Jdbc {
 url="*************************************"
 driver="com.ibm.db2.jcc.DB2Driver"
 user="db2inst1"
 password=Cdyanfa_123456
 query="select \"ID\",\"TEXT\" from \"TEST    \".\"ZSP_1223\" Where 1=2 "
 "fetch_size"="1000"
 "split.size"="1000"
 "connection_check_timeout_sec"="30"
 "snapshot.split.size"="8096"
 "snapshot.fetch.size"="1024"
 "connection.pool.size"="20"
 "chunk-key.even-distribution.factor.lower-bound"="0.05"
 "chunk-key.even-distribution.factor.upper-bound"="100"
 "sample-sharding.threshold"="1000"
 "inverse-sampling.rate"="1000"
 "startup.mode"="INITIAL"
 "stop.mode"="NEVER"
 "result_table_name"="E000001_source_1"
 "parallelism"=1
 "empty_data_strategy"=true
 }




 }
 transform {
 ReplaceAll{
 source_table_name="E000001_source_1"
 result_table_name="E000001_source_1_trans_1"
 replace_field_patterns={
 "TEXT"={
 pattern= ["\\n","\\r"]
 replacement= [null,null]
 }
 }
 replace_field_patterns={
 "ID"={
 pattern= ["\\n","\\r"]
 replacement= [null,null]
 }
 }
 }
 }
 sink {
 LocalFile {
 path="/opt/zsp/file"
 source_table_name = "E000001_source_1_trans_1"


 tmp_path="/opt/zsp/file/tmp"
 file_format_type="text"
 field_delimiter="\u0001"
 row_delimiter="\n"
 sink_columns=["ID","TEXT"]
 batch_size="1000"
 compress_codec="none"
 "empty_data_strategy"=true
 }

 }