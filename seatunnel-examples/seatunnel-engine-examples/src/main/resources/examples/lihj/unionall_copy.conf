env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name from user01"
        "result_table_name"="user01"
    }

}
transform {
      Copy {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields{
                address=name
            }
      }


}
sink {
    console{
     "source_table_name"="user01_1"
    }


}
