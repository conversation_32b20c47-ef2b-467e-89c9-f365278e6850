env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   ArgoLocalFile {
       path="/usr/local/gpdata/"
       schema={
        fields{
            emp_id = int
            emp_name=string
            gender=string
            account=string
            org_id=string
            birth_date=string
            age=int
            nationality=string
            province=string
            city=string
            email=string
            phone=string
            begin_date=string
            remark=string
            create_time=string
            update_time=string
            }
       }
       file_format_type="csv"
       delimiter=","
       argo_url="*********************************************"
       argo_driver="org.postgresql.Driver"
       argo_user="gp"
       argo_password="gpadmin"
       argo_database="public"
       argo_table="emp"
       argo_schema="xxx"
       argo_gpfdist_address="gpfdist://*************:8089/,gpfdist://*************:8090/"
       argo_prefix="emp_test.csv"
   }
}
transform {
}
sink {
     Console{
     }
}

