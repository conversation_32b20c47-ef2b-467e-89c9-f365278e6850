env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from `seatunnel_source`.`emp_quality_millions_100w`"
        "batch_size"="1024"
        "partition_column"="emp_id"
        }

}
transform {
}
sink {
Jdbc {
    driver = org.postgresql.Driver
    url = "*********************************************"
    user = gp
    password = gpadmin
    generate_sink_sql = true
    database = public
    table = "emp"
  }

}
