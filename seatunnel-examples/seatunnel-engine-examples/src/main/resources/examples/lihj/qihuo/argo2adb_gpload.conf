env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   ArgoAdbHdfsFile{
           fs.defaultFS = "hdfs://192.168.90.113:8020"
           path = "/user/hive/hdfsfile"
           file_format_type = "text"
           field_delimiter = ","
           row_delimiter = "\n"
           is_enable_transaction = true
           argo_url="*********************************"
           argo_user="hive"
           argo_password="123456"
           argo_schema="default"
           argo_table="emp_quality1_1024"
           argo_tmp_table_name="tmp_user3"
           schema {
                   fields {
                    emp_id = int
                    emp_name=string
                    gender=string
                    account=string
                    org_id=string
                    birth_date=string
                    age=int
                    nationality=string
                    province=string
                    city=string
                    email=string
                    phone=string
                    begin_date=string
                    remark=string
                    create_time=string
                    update_time=string
                   }
           }
       }
}
transform {
}
sink {
    ArgoAdbLocalFile{
           path="/usr/local/gpdata/emp"
           file_format_type="csv"
           delimiter=","
           adb_url="**********************************************"
           adb_driver="org.postgresql.Driver"
           adb_user="gp"
           adb_password="gpadmin"
           adb_database="public"
           adb_table="emp"
           adb_gpfdist_address="gpfdist://*************:8089/"
           adb_prefix="/emp_test111.csv"
           adb_tmp_file_path="/usr/local/gpdata"
    }
}

