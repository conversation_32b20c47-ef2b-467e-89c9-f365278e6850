env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        ArgoAdbHdfsFile{
            fs.defaultFS="hdfs://192.168.90.113:8020"
            path="/user/hive/hdfsfile"
            file_format_type = "text"
            field_delimiter = ","
            row_delimiter = "\n"
            is_enable_transaction = true
            argo_url="*****************************************"
            argo_user="hive"
            argo_password="hive"
            argo_schema="default"
            argo_table="emp_quality1_1024"
            argo_tmp_table_name="tmp_1740382608208_emp_test"
            schema= {
                fields {
                    emp_id=STRING
                    emp_name=STRING
                    gender=STRING
                    account=STRING
                    org_id=STRING
                    birth_date=STRING
                    age=STRING
                    nationality=STRING
                    province=STRING
                    city=STRING
                    email=STRING
                    phone=STRING
                    begin_date=STRING
                    remark=STRING
                    create_time=STRING
                    update_time=STRING
                }
            }
        }
}
transform {
}
sink {
        ArgoAdbLocalFile{
            path="/usr/local/soft/********/tmp"
            file_format_type="csv"
            field_delimiter=","
            batch_size="***********"
            adb_url="********************************************"
            adb_driver="org.postgresql.Driver"
            adb_user="gp"
            adb_password="gpadmin"
            adb_database="public"
            adb_table="emp"
            adb_gpfdist_address="gpfdist://************:8089/"
            adb_tmp_file_path="/emp/0.csv"
            adb_gpfdist_path="/usr/local/gpdata/"
            adb_external_table_name="emp_test111"
        }
}
