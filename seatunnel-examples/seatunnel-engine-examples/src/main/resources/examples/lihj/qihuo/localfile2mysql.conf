env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   LocalFile {
       path="/usr/local/aa/"
       schema={
        fields{
            emp_id = int
            emp_name=string
            gender=string
            account=string
            org_id=string
            birth_date=string
            age=int
            nationality=string
            province=string
            city=string
            email=string
            phone=string
            begin_date=string
            remark=string
            create_time=string
            update_time=string
            }
       }
       file_format_type="csv"
       delimiter=","
   }
}
transform {
}
sink {
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_104lihj20240407"
        "batch_size"="1024"
        "auto_commit"="true"
        "generate_sink_sql"="true"
    }
}

