env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   ArgoAdbHdfsFile{
           fs.defaultFS = "hdfs://192.168.90.113:8020"
           path = "/tmp/20241216"
           file_format_type = "text"
           field_delimiter = ","
           row_delimiter = "\n"
           is_enable_transaction = true
           argo_url="*********************************"
           argo_user="hive"
           argo_password="123456"
           argo_schema="default"
           argo_table="emp_quality1_1024"
           argo_tmp_table_name="tmp_user3"
           schema {
                   fields {
                    emp_id = int
                    emp_name=string
                    gender=string
                    account=string
                    org_id=string
                    birth_date=string
                    age=int
                    nationality=string
                    province=string
                    city=string
                    email=string
                    phone=string
                    begin_date=string
                    remark=string
                    create_time=string
                    update_time=string
                   }
           }
       }
}
transform {
}
sink {
    console{}
}

