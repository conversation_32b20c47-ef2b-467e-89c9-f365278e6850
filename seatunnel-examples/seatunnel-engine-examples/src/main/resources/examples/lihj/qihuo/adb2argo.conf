env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   ArgoLocalFile {
       schema={
        fields{
            emp_id = int
            emp_name=string
            gender=string
            account=string
            org_id=string
            birth_date=string
            age=int
            nationality=string
            province=string
            city=string
            email=string
            phone=string
            begin_date=string
            remark=string
            create_time=string
            update_time=string
            }
       }
       path="/usr/local/gpdata/emp_test111.csv"
       file_format_type="csv"
       delimiter=","
       argo_url="**********************************************"
       argo_driver="org.postgresql.Driver"
       argo_user="gp"
       argo_password="gpadmin"
       argo_database="public"
       argo_table="emp"
       argo_schema="xxx"
       argo_gpfdist_address="gpfdist://**************:8089/"
       argo_prefix="/emp_test111.csv"
   }
}
transform {
}
sink {
    ArgoHdfsFile{
        fs.defaultFS = "hdfs://nameservice1"
        path = "/tmp/lihj/hdfsfile"
        file_format_type = "text"
        field_delimiter = ","
        row_delimiter = "\n"
        is_enable_transaction = true
        argo_url="************************"
        argo_user="hive"
        argo_password="123456"
        argo_schema="default"
        argo_table="user_20240606"
        argo_tmp_table_name="tmp_user3"
        krb5_path="/usr/local/soft/tdh-config/krb5.conf"
        kerberos_principal="hdfs/node1@TDH"
        kerberos_keytab_path="/usr/local/soft/tdh-config/hdfs.keytab"
        hdfs_site_path="/usr/local/soft/tdh-config/hdfs-site.xml"

    }
}

