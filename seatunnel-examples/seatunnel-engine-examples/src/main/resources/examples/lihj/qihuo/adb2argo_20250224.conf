env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   ArgoLocalFile {
       path="/usr/local/soft/********/tmp"
       schema={
        fields{
            emp_id = int
            emp_name=string
            gender=string
            account=string
            org_id=string
            birth_date=string
            age=int
            nationality=string
            province=string
            city=string
            email=string
            phone=string
            begin_date=string
            remark=string
            create_time=string
            update_time=string
            }
       }
       file_format_type="csv"
       field_delimiter=","
       adb_url="********************************************"
       adb_driver="org.postgresql.Driver"
       adb_user="gp"
       adb_password="gpadmin"
       adb_database="public"
       adb_table="emp"
       argo_schema="xxx"
       adb_gpfdist_address="gpfdist://************:8089/"
       adb_tmp_file_path="/emp_argo.csv"
       adb_external_table_name="emp_argo7"
       adb_gpfdist_path="/usr/local/gpdata/"
       adb_external_table_delimiter=","
   }
}

transform {
}
sink {
    ArgoHdfsFile{
        fs.defaultFS = "hdfs://**************:8020"
        path = "/user/hive/hdfsfile"
        file_format_type = "text"
        field_delimiter = ","
        row_delimiter = "\n"
        is_enable_transaction = true
        argo_url="*********************************"
        argo_user="hive"
        argo_password="hive"
        argo_schema="default"
        argo_table="user_20243"
        argo_tmp_table_name="tmp_user3"

    }
}

