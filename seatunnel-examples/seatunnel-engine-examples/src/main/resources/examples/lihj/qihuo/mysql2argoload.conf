env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality`"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "partition_column"="emp_id"
        }

}
transform {
}
sink {
    ArgoHdfsFile{
        fs.defaultFS = "hdfs://192.168.90.113:8020"
        path = "/user/hive/hdfsfile"
        file_format_type = "text"
        field_delimiter = ","
        row_delimiter = "\n"
        is_enable_transaction = true
        argo_url="*********************************"
        argo_user="hive"
        argo_password="hive"
        argo_schema="default"
        argo_table="user_20243"
        argo_tmp_table_name="tmp_user3"

    }

}
