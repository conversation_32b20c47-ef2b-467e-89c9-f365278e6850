env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="jdbc:informix-sqli://192.168.5.127:9088/lr:INFORMIXSERVER=ol_informix1410"
        driver="com.informix.jdbc.IfxDriver"
            schema="lr"
            user="itest"
            password="itest"
        query="select user_id,first_name,last_name,email,gender,date_of_birth,created_at from users Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "table_path"=1
        }
}
transform {
}
sink {
        Jdbc {
        url="jdbc:informix-sqli://192.168.5.127:9088/lr:INFORMIXSERVER=ol_informix1410"
        driver="com.informix.jdbc.IfxDriver"
            user="itest"
            password="itest"
        database="itest"
        table="test_user"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "partition_keys"=[]
        }

}