env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="jdbc:informix-sqli://*************:9088/lr:INFORMIXSERVER=ol_informix1410"
        driver="com.informix.jdbc.IfxDriver"
        schema="lt"
        user="itest"
        password="itest"
        query="select id,big_number,tiny_number,float_value,double_value,decimal_value,event_date,user_name,code,description,is_active,event_json from lt.all_data_types_12_nosmallint_0528 Where 1=1 "
        "fetch_size"="1000"
        "result_table_name"="E000004_source_1"
        "parallelism"=1
        "table_path"=1
        }

}
transform {
}
sink {
        Doris {
        fenodes="**************:8030"
        query-port="9030"
        username="root"
        password="Cdyanfa_123456"
        database="test"
        table="informix_doris_manytype_20250709"
        table.identifier="test.informix_doris_manytype_20250709"
        source_table_name="E000004_source_1"
        sink.label-prefix="ad1dfbe4-cf73-41d2-b992-0cfffb3e14ec"
        sink.buffer-size="256"
        sink.buffer-count="2"
        doris.batch.size=1024
        data_save_mode="APPEND_DATA"
        doris.config={
            format=json
            read_json_by_line=true
        }
        }
}