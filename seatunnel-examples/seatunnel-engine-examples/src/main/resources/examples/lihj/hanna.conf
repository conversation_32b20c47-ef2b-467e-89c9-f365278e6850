env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="*************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select id,name,age,sex,email from student"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="newtable_source_1_trans_2"
        "parallelism"=2
    }
}
transform {
}
sink {
    Jdbc {
        url="*******************************"
        driver="com.sap.db.jdbc.Driver"
        user="SYSTEM"
        password="Dsgdata@123"
        database="DEMO"
        table="STUDENT"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="newtable_source_1_trans_2"
        "generate_sink_sql"="true"
         "field_ide"=UPPERCASE
    }
}