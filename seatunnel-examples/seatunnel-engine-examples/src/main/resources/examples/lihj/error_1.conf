env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select id,name,gender,birth_date,id_number,phone_number,email,home_address,marital_status,education,major,graduate_school,work_experience,position,company_name,join_date,leave_date,monthly_salary,social_security_number,emergency_contact,emergency_contact_phone,is_party_member,health_status,height,weight,blood_type,household_registration_address,current_address,hobbies,remarks,is_employed,employee_number,department,work_location,bank_account_number,bank_name,skills,certificates,social_media_account,created_at,updated_at from personnel_information_1w Where 1=1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="personnel_information_1w_source_1"
            "parallelism"=1
        }
}
transform {
}
sink {
        Jdbc {
        url="*****************************************"
        driver="org.postgresql.Driver"
            user="postgres"
            password="Cdyanfa_123456"
        database="qjq"
        table="test.personnel_information_1w"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="personnel_information_1w_source_1"
        "generate_sink_sql"="true"
            "field_ide"=LOWERCASE
            "enable_upsert"="true"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}
