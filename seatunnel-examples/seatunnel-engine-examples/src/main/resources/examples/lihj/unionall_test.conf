env {
    "job.mode"="BATCH"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select id,name from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select user_id,score from user02"
            "result_table_name"="user02"
        }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select user_cid,money from user03"
            "result_table_name"="user03"
        }
}
transform {
    FieldMapper  {
         source_table_name = "user01"
         result_table_name = "userall1"
         field_mapper  {
            id = id
            name=name
         }
    }
    FieldMapper  {
         source_table_name = "user02"
         result_table_name = "userall2"
         field_mapper  {
            user_id = id
            score=score
         }
    }
    FieldMapper  {
         source_table_name = "user03"
         result_table_name = "userall3"
         field_mapper  {
            user_cid = id
            money=money
         }
    }
}
sink {
    Jdbc {
        "source_table_name"="userall1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
    Jdbc {
        "source_table_name"="userall2"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
	    joyadata_join_type ="left_join"
        joyadata_join_on=["user_id=id"]
    }
    Jdbc {
        "source_table_name"="userall3"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="user_all"
        "generate_sink_sql"="true"
	    enable_upsert = true
	    joyadata_join_type ="LEFT_JOIN"
        joyadata_join_on=["user_cid=id","cname=name"]
    }
}
