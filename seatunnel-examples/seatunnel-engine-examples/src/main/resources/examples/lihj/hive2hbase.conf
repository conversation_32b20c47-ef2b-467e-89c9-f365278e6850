env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
       Jdbc {
        url="*****************************************"
        driver="org.apache.hive.jdbc.HiveDriver"
        user="hive"
        password="hive"
        query="select empno,ename,job,mgr,hiredate,sal,comm,deptno from default.emp2"
       }
}
transform {
}
sink {
  HbaseBulkload {
     zookeeper_quorum = "192.168.90.113:2181"
     table = "bkl"
     rowkey_column=["empno"]
     family_name = {
         all_columns = "cf1"
     }
   }

}

