env {
    "job.mode"="STREAMING"
    "job.name"="10"
}
source {
    SqlServer-CDC {
        result_table_name = "SQLServer_cdc_test_table"
         username = "SA"
         password = "Sa@12345678"
            database-names = ["lihj"]
            table-names = ["lihj.dbo.test_table"]
            base-url = "******************************************************"
        }

}
transform {
}
sink {
    Jdbc {
        url="jdbc:dm://192.168.90.104:30236"
        driver="dm.jdbc.driver.DmDriver"
            user="SYSDBA"
            password="SYSDBA001"
        database="SYSDBA"
        table="SYSDBA.TEST_TABLE1"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="SQLServer_cdc_test_table"
        "generate_sink_sql"="true"
    }
}