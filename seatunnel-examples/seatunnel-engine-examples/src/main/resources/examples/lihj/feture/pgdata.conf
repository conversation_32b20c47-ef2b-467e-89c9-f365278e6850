env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="**********************************************"
        driver="org.postgresql.Driver"
        user="postgres"
        password="Cdyanfa_123456"
        query="select "id", "name","create_time" from "test"."test" Where 1=1  and create_time > '1900-01-01 01:01:01.000'and create_time <= '2024-10-31 16:16:16.999'"
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "partition_column"="create_time"
            "partition_num"="5"
            "parallelism"=1
            "empty_data_strategy"=false
        }



}
transform {
}
sink {
        Jdbc {
        url="**********************************************"
        driver="org.postgresql.Driver"
        user="postgres"
        password="Cdyanfa_123456"
        database="postgres"
        table="test.test_1031"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
        "field_ide"=LOWERCASE
        "enable_upsert"="false"
        "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        "empty_data_strategy"=false
        }

}