env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/module/backend/test/zl/"
    file_format_type = "binary"
  }
}
transform {
}
 sink {
     LocalFile {
         path="d://tmp1//sftp"
         file_format_type="binary"
         validate_file="d://tmp//sftp//lihj_20240920//20241030.ok"
         validate_content="000000000${file_name}_${file_size}_${file_md5}_${file_count}_${folder_count}"
     }

 }
