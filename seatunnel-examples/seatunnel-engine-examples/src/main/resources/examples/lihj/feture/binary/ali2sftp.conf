env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     OssFile {
         path="/lihj_sftp/apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
         bucket="oss://joyadata"
         access_key="LTAI5tPWRfPWF18QECwdDJRZ"
         access_secret="******************************"
         endpoint="http://cn-beijing.oss.aliyuncs.com"
         file_format_type="binary"
     }



 }
 transform {
 }
sink {
    SftpFile {
        host = "**************"
        port = 22
        user = "root"
        password = "joyadata"
        path = "/opt/module/backend/test/zl_ali"
        tmp_path = "/opt/module/backend/test/tmp"
        file_format_type = "binary"
    }
}
