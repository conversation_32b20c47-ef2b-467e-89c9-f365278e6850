env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="system"
            password="joyadata"
        query="select \"ID\",\"USERNAME\",\"PASSWORD\",\"BIRTH_DATE\",\"REGISTRATION_TIME\",\"AGE\",\"HEIGHT\",\"WEIGHT\",\"IS_ACTIVE\",\"IP_ADDRESS\",\"STATUS\",\"CREATED_BY\",\"NOTES\",\"EMAIL\",\"LAST_LOGIN_TIME\",\"PROFILE_PICTURE\" from \"C##QJQ\".\"EXAMPLE_TABLE\" Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
        }




}
transform {
}
sink {
        Doris {
        fenodes="**************:8030"
        query-port="9030"
        username="root"
            password="Cdyanfa_123456"
        database="test"
        table="example_table08"
            table.identifier="test.example_table08"
            source_table_name="E000001_source_1"
            sink.label-prefix="122862c8-64ba-4285-a8d9-492cc3610030"
            sink.buffer-size="256"
            sink.buffer-count="2"
            doris.batch.size=1024
            data_save_mode="APPEND_DATA"
            doris.config={
                format=json
                read_json_by_line=true
            }
        }

}
