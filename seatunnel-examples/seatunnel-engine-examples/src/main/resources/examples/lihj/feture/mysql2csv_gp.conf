env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select * from csvtest"
        }
}
transform {
}
sink {
 Jdbc {
        url="***********************************************"
        driver="org.postgresql.Driver"
        user="gp"
        password="Cdyanfa_123456"
        database="postgres"
        table="public.csv_test"
        "batch_size"="1000"
        "support_upsert_by_query_primary_key_exist"="true"
        "generate_sink_sql"="true"
        "use_copy_statement"="true"
        }
}