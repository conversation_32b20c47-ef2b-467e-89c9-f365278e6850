 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
Jdbc {
url="jdbc:mysql://**************:3306/nacos?useUnicode=true&rewriteBatchedStatements=true&zeroDateTimeBehavior=convertToNull&characterEncoding=utf-8&useSSL=false"
driver="com.mysql.cj.jdbc.Driver"
user="root"
password="Cdyanfa_123456"
query="select `id`,`data_id`,`group_id`,`content`,`md5`,`gmt_create`,`gmt_modified`,`src_user`,`src_ip`,`app_name`,`tenant_id`,`c_desc`,`c_use`,`effect`,`type`,`c_schema`,`encrypted_data_key` from `nacos`.`config_info` Where 1=1 "
"fetch_size"="1000"
"split.size"="1000"
"connection_check_timeout_sec"="30"
"snapshot.split.size"="8096"
"snapshot.fetch.size"="1024"
"connection.pool.size"="20"
"chunk-key.even-distribution.factor.lower-bound"="0.05"
"chunk-key.even-distribution.factor.upper-bound"="100"
"sample-sharding.threshold"="1000"
"inverse-sampling.rate"="1000"
"startup.mode"="INITIAL"
"stop.mode"="NEVER"
"result_table_name"="E000004_source_1"
"parallelism"=1
"empty_data_strategy"=false
}




}
transform {
}
sink {
MergeLocalFile {
final_name="/test/ld/ld2/测试文件名后缀.txt"
path="/test/ld/ld2"
source_table_name = "E000004_source_1"
tmp_path="/test/ld/ld2/tmp"
file_format_type="text"
field_delimiter="\u0001"
row_delimiter="\n"
sink_columns=["id","data_id","group_id","content","md5","gmt_create","gmt_modified","src_user","src_ip","app_name","tenant_id","c_desc","c_use","effect","type","c_schema","encrypted_data_key"]
batch_size="1000"
compress_codec="none"
overwrite_file="false"
"empty_data_strategy"=false
}

}