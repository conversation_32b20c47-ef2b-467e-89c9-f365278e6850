env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     S3File {
         path="/lihj_20241106_sftp"
         bucket="s3a://joyadata"
         access_key="********************"
         secret_key="6ySgiYBTckBkBv7P8LA224LnjKWareQsIIIeZBZt"
         fs.s3a.endpoint="s3.us-west-2.amazonaws.com"
         file_format_type="binary"
         fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
     }
 }
 transform {
 }
 sink {
     LocalFile {
         path="d://tmp1//aws"
         file_format_type="binary"
     }

 }
  
