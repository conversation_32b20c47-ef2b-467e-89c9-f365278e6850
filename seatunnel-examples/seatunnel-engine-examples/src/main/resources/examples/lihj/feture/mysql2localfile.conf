env {
"job.mode"="BATCH"
"job.name"="10"
"parallelism"="10"
}
source {
        Jdbc {
        url="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from t_ds_task_logs"
        "parallelism"=1
        }


}
transform {

}


sink {
   LocalFile {
       path="d://tmp"
       tmp_path="d://tmp"
       file_format_type="text"
       field_delimiter=","
       row_delimiter="\n"
       validate_file="d://tmp//222.ok"
       validate_content="000000000${file_name}_${file_size}_${file_row}_${file_md5}_${file_count}_${folder_count}"
   }
}