env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     LocalFile {
         path="G:\\github\\开源\\seatunnel\\seatunnel\\seatunnel-dist\\target\\apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
         file_format_type="binary"
     }

 }
 sink {
     OssFile {
         path="/lihj"
         tmp_path="/tmp/seatunnel"
         bucket="oss://joyadata"
         access_key="LTAI5tPWRfPWF18QECwdDJRZ"
         access_secret="******************************"
         endpoint="http://cn-beijing.oss.aliyuncs.com"
         file_format_type="binary"
     }
 }
