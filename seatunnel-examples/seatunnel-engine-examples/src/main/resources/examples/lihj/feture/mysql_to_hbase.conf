env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id` from emp_quality"
    }
}
transform {

}
sink {
Hbase {
  zookeeper_quorum = "192.168.90.113:2181"
  table = "my_table"
  rowkey_column = ["emp_id"]
  family_name {
    all_columns ="cf1"
  }
}
}
