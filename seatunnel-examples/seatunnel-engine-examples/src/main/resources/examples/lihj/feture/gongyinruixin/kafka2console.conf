env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
	 Kafka {
		 topic="test"
		 bootstrap.servers="localhost:9092"
		 pattern="false"
		 consumer.group="SeaTunnel-Consumer-Group"
		 result_table_name="kafka_source_1"
		 commit_on_checkpoint="true"
		 schema= {
			 fields {
			 agent_send_timestamp=STRING
			 collector_recv_timestamp=STRING
			 k8s=STRING
			 ip=STRING
			 source=STRING
			 hostname=STRING
			 appname=STRING
             tag=STRING
             timestamp=STRING
			 }
		 }
		 format="json"
		 format_error_handle_way="fail"
		 field_delimiter=""
		 start_mode=earliest
		 start_mode.offsets=""
		 start_mode.timestamp=""
		 partition-discovery.interval-millis="-1"
	 }



 }
 transform {

 }
 sink {
	 console{}

 }