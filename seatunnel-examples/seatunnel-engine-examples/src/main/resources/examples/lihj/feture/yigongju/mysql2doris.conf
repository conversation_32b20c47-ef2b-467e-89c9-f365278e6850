env {
"job.mode"="BATCH"
"job.name"="10"
"parallelism"="10"
}
source {
        Jdbc {
        url="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="SELECT CODEID, CATE<PERSON><PERSON>YCODE, CATEGORYNAME, CODE, ASSISTANTCODE1, ASSISTANTCODE2, ASSISTANTCODE3, ASSISTANTCODE4, MNEMONICCODE, PARENTID, R<PERSON><PERSON><PERSON>RCODE, R<PERSON><PERSON><PERSON>RDES<PERSON>, REC<PERSON><PERSON><PERSON><PERSON>, PRETRIALCODE, PRETRIALDESC, PRETRIALTIME, AUDITORCODE, AUDITORDESC, AUDITTIME, AUDITLEVEL, AUDITFLAG, RETRIEVEFLAG, RETRIEVERCODE, RETRIEVERDESC, RETRIEVETIME, FREEZEFLAG, FREEZERCODE, FREEZERDESC, FREEZETIME, WORKFLOWID, RECORDERCORP, MDMCODE, AUDITLEVELNAME, VERSION, SYSCODEVERSION, FILINGFLAG, FILINGUSERCODE, FILINGUSERNAME, FILINGDATE, LUCENCEFLAG, LUCENCETIME, SUBMITCORP, DESCLONG, DESCSHORT, DESC1, DESC2, DESC3, DESC4, DESC5, DESC6, DESC7, DESC8, DESC9, DESC10, DESC100, REMARK, VALIDATEMSG, MODIFYPROPERTYGCODE, CATEGORYVERSION, MDMCODECREATEABLE, LASTMODIFYRECORDERCODE, LASTMODIFYRECORDERDESC, LASTMODIFYRECORDTIME, SUBMITTIME, FLOWPARA, FILECOUNT, TEMPSAVEFLAG, TASKFLAG, UUID, ERRORMSG, AUDITINGFLAG, RELEASEFLAG, DATASOURCEFLAG, LASTMODIFYSUBMITCORP, USEPARENTWORKFLOW, STANDINFO, SECURITYLEVELCODE, SUBMITCORPDESC, DESC20 ,'2024-11-11' AS ETL_PART from mdm_xmlx_query"
        "parallelism"=1
        }


}
transform {
        FieldMapper{
                source_table_name="test101901m_source_1"
                result_table_name="test101901m_source_1_trans_2"
                        field_mapper={
                            CODEID=codeid
                            CATEGORYCODE=categorycode
                            CATEGORYNAME=categoryname
                            CODE=code
                            ASSISTANTCODE1=assistantcode1
                            ASSISTANTCODE2=assistantcode2
                            ASSISTANTCODE3=assistantcode3
                            ASSISTANTCODE4=assistantcode4
                            MNEMONICCODE=mnemoniccode
                            PARENTID=parentid
                            RECORDERCODE=recordercode
                            RECORDERDESC=recorderdesc
                            RECORDTIME=recordtime
                            PRETRIALCODE=pretrialcode
                            PRETRIALDESC=pretrialdesc
                            PRETRIALTIME=pretrialtime
                            AUDITORCODE=auditorcode
                            AUDITORDESC=auditordesc
                            AUDITTIME=audittime
                            AUDITLEVEL=auditlevel
                            AUDITFLAG=auditflag
                            RETRIEVEFLAG=retrieveflag
                            RETRIEVERCODE=retrievercode
                            RETRIEVERDESC=retrieverdesc
                            RETRIEVETIME=retrievetime
                            FREEZEFLAG=freezeflag
                            FREEZERCODE=freezercode
                            FREEZERDESC=freezerdesc
                            FREEZETIME=freezetime
                            WORKFLOWID=workflowid
                            RECORDERCORP=recordercorp
                            MDMCODE=mdmcode
                            AUDITLEVELNAME=auditlevelname
                            VERSION=version
                            SYSCODEVERSION=syscodeversion
                            FILINGFLAG=filingflag
                            FILINGUSERCODE=filingusercode
                            FILINGUSERNAME=filingusername
                            FILINGDATE=filingdate
                            LUCENCEFLAG=lucenceflag
                            LUCENCETIME=lucencetime
                            SUBMITCORP=submitcorp
                            DESCLONG=desclong
                            DESCSHORT=descshort
                            DESC1=desc1
                            DESC2=desc2
                            DESC3=desc3
                            DESC4=desc4
                            DESC5=desc5
                            DESC6=desc6
                            DESC7=desc7
                            DESC8=desc8
                            DESC9=desc9
                            DESC10=desc10
                            DESC100=desc100
                            REMARK=remark
                            VALIDATEMSG=validatemsg
                            MODIFYPROPERTYGCODE=modifypropertygcode
                            CATEGORYVERSION=categoryversion
                            MDMCODECREATEABLE=mdmcodecreateable
                            LASTMODIFYRECORDERCODE=lastmodifyrecordercode
                            LASTMODIFYRECORDERDESC=lastmodifyrecorderdesc
                            LASTMODIFYRECORDTIME=lastmodifyrecordtime
                            SUBMITTIME=submittime
                            FLOWPARA=flowpara
                            FILECOUNT=filecount
                            TEMPSAVEFLAG=tempsaveflag
                            TASKFLAG=taskflag
                            UUID=uuid
                            ERRORMSG=errormsg
                            AUDITINGFLAG=auditingflag
                            RELEASEFLAG=releaseflag
                            DATASOURCEFLAG=datasourceflag
                            LASTMODIFYSUBMITCORP=lastmodifysubmitcorp
                            USEPARENTWORKFLOW=useparentworkflow
                            STANDINFO=standinfo
                            SECURITYLEVELCODE=securitylevelcode
                            SUBMITCORPDESC=submitcorpdesc
                            DESC20=desc20
                            ETL_PART=etl_part
                        }
            }
}


sink {
      Doris {
      fenodes="192.168.90.221:8030"
      query-port="9030"
      username="root"
      password=""
      database="ykw"
      table="stg_cfhec_mdm_xmlx_query_f_d"
      table.identifier="ykw.stg_cfhec_mdm_xmlx_query_f_d"
      sink.enable-2pc="false"
      data_save_mode="APPEND_DATA"
      doris.config={
      format=json
      read_json_by_line=true
      }
      }
}