env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="**********************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality_millions_100w` Where 1=1 and emp_id<=10000"
        "batch_size"="1000"
        "split.size"="1000"
        result_table_name="01"
    }
}
transform {
}
sink {
    MergeS3File {
        source_table_name="01"
        path="/lihj_20240920"
        tmp_path="/tmp/seatunnel"
        bucket="s3a://joyadata"
        access_key="minio"
        secret_key="Cdyanfa_123456"
        fs.s3a.endpoint="http://192.168.90.221:9000"
        fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        file_format_type="text"
        field_delimiter="\u0001"
        row_delimiter="\n"
        have_partition="false"
        is_enable_transaction="true"
        batch_size="1000000"
        filename_time_format="yyyy.MM.dd"
        compress_codec="none"
        final_name="aa.txt"
    }

}