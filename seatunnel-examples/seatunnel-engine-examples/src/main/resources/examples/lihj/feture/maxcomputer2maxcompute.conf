env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
  Maxcompute {
    accessId="LTAI5t6QbR82f91qfpr1KgHA"
    accesskey="******************************"
    endpoint="https://service.cn-beijing.maxcompute.aliyun.com/api"
    project="dsg_2024mc"
    table_name="user_info"
    partition_spec="dt='20230715'"
    split_row = 10000
    schema {
        fields {
            user_id = string
            user_name = string
            age = int
            gender = string
            register_time = timestamp
        }
    }
  }
}
transform {
}
sink {
  Maxcompute {
    accessId="LTAI5t6QbR82f91qfpr1KgHA"
    accesskey="******************************"
    endpoint="https://service.cn-beijing.maxcompute.aliyun.com/api"
    project="dsg_2024mc"
    table_name="user_info_sink"
    partition_spec="dt='20230716'"
    #overwrite = false
  }
}