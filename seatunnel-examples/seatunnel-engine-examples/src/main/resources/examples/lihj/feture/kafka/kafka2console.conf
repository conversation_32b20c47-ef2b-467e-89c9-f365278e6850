env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Kafka {
            bootstrap.servers = "192.168.100.58:9092"
            topic = "DATASOURCE_AUTHORIZATION_V1_R2P1"
            consumer.group = "tdsql1_test_group"
            start_mode = "earliest"
            format=text
            field_delimiter=","
            schema{
               fields{
                     name="string"
               }
            }
         kafka.config = {
            security.protocol=SASL_PLAINTEXT
            sasl.mechanism=SCRAM-SHA-256
            sasl.jaas.config="org.apache.kafka.common.security.scram.ScramLoginModule required username=\"admin\" password=\"Cdyanfa_123456\";"
        }
       }
}
transform {
}
sink {
    console{}


}

