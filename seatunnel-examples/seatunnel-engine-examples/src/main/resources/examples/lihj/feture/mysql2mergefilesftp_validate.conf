env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="**********************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality_millions_100w` Where 1=1 and emp_id<=10"
        "batch_size"="1000"
        "split.size"="1000"
    }
}
transform {
}
sink {
    MergeSFtpFile{
        host="**************"
        port="22"
        user="root"
        password="joyadata"
        path="/tmp/lihj"
            tmp_path="/tmp/seatunnel"
            custom_filename=true
            final_name="/tmp/lihj/a.txt"
            file_name_expression="a.txt_${transactionId}"
            is_enable_transaction=false
            filename_time_format="yyyy.MM.dd"
            file_format_type="text"
            field_delimiter="\u0001"
            row_delimiter="\n"
            have_partition="false"
            batch_size="1000000"
            compress_codec="none"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            validates="false"
            validate_file="/tmp/lihj/ok.flg"
            validate_content="123"
        clean_target_folder="false"
    }
}