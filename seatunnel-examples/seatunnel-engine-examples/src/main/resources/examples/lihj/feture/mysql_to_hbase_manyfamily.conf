env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id` from emp_quality where emp_id<=10"
    }
}
transform {

}
sink {
Hbase {
  zookeeper_quorum = "192.168.90.113:2181"
  table = "my_table1"
  rowkey_column = ["emp_id"]
  family_name {
    "emp_name" ="cf1"
    "gender"="cf1"
    "account"="cf2"
    "org_id"="cf2"
  }
}
}
