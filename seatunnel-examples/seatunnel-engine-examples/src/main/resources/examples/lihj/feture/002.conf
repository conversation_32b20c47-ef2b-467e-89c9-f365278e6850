 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="**********************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `CODEID`,`CATEGORYCODE`,`CATEGORYNAME`,`CODE`,`ASSISTANTCODE1`,`ASSISTANTCODE2`,`ASSISTANTCODE3`,`ASSISTANTCODE4`,`MNEMONICCODE`,`PARENTID`,`RECORDERCODE`,`RECORDERDESC`,`RECORDTIME`,`PRETRIALCODE`,`PRETRIALDESC`,`PRETRIALTIME`,`AUDITORCODE`,`AUDITORDESC`,`AUDITTIME`,`AUDI<PERSON>EVEL`,`AUDITFLAG`,`RET<PERSON>EVEFLAG`,`RETRIEVERCODE`,`RET<PERSON>E<PERSON><PERSON>ESC`,`RETRIEVETIME`,`FREEZEFLAG`,`FREEZERCODE`,`FREEZERDESC`,`FREEZETIME`,`WORKFLOWID`,`RECORDERCORP`,`MDMCODE`,`AUDITLEVELNAME`,`VERSION`,`SYSCODEVERSION`,`FILINGFLAG`,`FILINGUSERCODE`,`FILINGUSERNAME`,`FILINGDATE`,`LUCENCEFLAG`,`LUCENCETIME`,`SUBMITCORP`,`DESCLONG`,`DESCSHORT`,`DESC1`,`DESC2`,`DESC3`,`DESC4`,`DESC5`,`DESC6`,`DESC7`,`DESC8`,`DESC9`,`DESC10`,`DESC100`,`REMARK`,`VALIDATEMSG`,`MODIFYPROPERTYGCODE`,`CATEGORYVERSION`,`MDMCODECREATEABLE`,`LASTMODIFYRECORDERCODE`,`LASTMODIFYRECORDERDESC`,`LASTMODIFYRECORDTIME`,`SUBMITTIME`,`FLOWPARA`,`FILECOUNT`,`TEMPSAVEFLAG`,`TASKFLAG`,`UUID`,`ERRORMSG`,`AUDITINGFLAG`,`RELEASEFLAG`,`DATASOURCEFLAG`,`LASTMODIFYSUBMITCORP`,`USEPARENTWORKFLOW`,`STANDINFO`,`SECURITYLEVELCODE`,`SUBMITCORPDESC`,`DESC11`,`DESC12`,`DESC13`,`DESC14`,`DESC15`,`DESC16`,`DESC17`,`DESC18`,`DESC19`,`DESC20`,`DESC21`,`DESC22`,`DESC23`,`DESC24`,`DESC25`,`DESC26`,'2024-10-24' as `etl_part` from `seatunnel_source`.`mdm_12ywgx_query` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="E000001_source_1"
        "parallelism"=1
    }



}
transform {
}
sink {
    Doris {
        fenodes="192.168.90.221:8030"
        query-port="9030"
        username="root"
        password=""
        database="test"
        table="stg_cfhec_mdm_12ywgx_query_f_d"
        table.identifier="test.stg_cfhec_mdm_12ywgx_query_f_d"
        source_table_name="E000001_source_1"
        sink.enable-2pc="false"
        data_save_mode="APPEND_DATA"
        doris.config={
        format=json
        read_json_by_line=true
        }
    }
}