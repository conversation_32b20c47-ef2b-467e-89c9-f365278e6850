env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id` from emp_quality "
    }
}
transform {

}
sink {
   Gds2DwsFile {
       path="/usr/local/soft/dws/input_data/"
       tmp_path="/usr/local/soft/dws/input_data/tmp"
       file_format_type="csv"
       field_delimiter=","
       row_delimiter="\n"
       final_name="/usr/local/soft/dws/input_data/emp_quality_millions_100w.csv"
       batch_size ="***********"
       dws_url="**************************************"
       dws_driver="org.postgresql.Driver"
       dws_user="dsg"
       dws_password="Dsgdata@123"
       dws_table="emp_20241224"
       dws_schema="dsg"
       dws_gds_address="gsfs://**************:5000"
       dws_fields=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
       dws_clean_cache=true
   }
}
