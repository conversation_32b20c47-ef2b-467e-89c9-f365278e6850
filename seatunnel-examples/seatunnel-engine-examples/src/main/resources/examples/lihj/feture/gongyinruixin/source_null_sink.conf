env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
     Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select emp_id,emp_name from emp_quality where emp_id='-1'"
        }

}
transform {
}
sink {
        MergeSftpFile {
        final_name="/dsg/dai/20250714/dai01.txt"
        host="**************"
        port="22"
        user="root"
        password="joyadata"
        path="/dsg/dai/20250714"
        source_table_name="E000001_source_1"
        tmp_path="/tmp/seatunnel"
        custom_filename=true
        file_name_expression="dai01_${transactionId}"
        is_enable_transaction=false
        filename_time_format="yyyy.MM.dd"
        file_format_type="text"
        row_delimiter="\n"
        batch_size="1000000"
        compress_codec="none"
        date_format="yyyy-MM-dd"
        datetime_format="yyyy-MM-dd HH:mm:ss"
        time_format="HH:mm:ss"
        validates="false"
        validate_file="/dsg/dai/20250714/dai01.ok"
        "create_empty_file_when_no_data"=true
        clean_target_folder="false"
        encoding="UTF-8"
        }
}
