env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
LocalFile {
    path = "d://tmp//opt//zsp//file"
    file_format_type = csv
    delimiter=","
    schema = {
      fields {
        context = string
        date = timestamp
      }
    }
  }
}
transform {
}
sink {
 Jdbc {
        url="***********************************************"
        driver="org.postgresql.Driver"
        user="gp"
        password="Cdyanfa_123456"
        database="postgres"
        table="public.csv_test"
        "batch_size"="1000"
        "support_upsert_by_query_primary_key_exist"="true"
        "generate_sink_sql"="true"
        }
}