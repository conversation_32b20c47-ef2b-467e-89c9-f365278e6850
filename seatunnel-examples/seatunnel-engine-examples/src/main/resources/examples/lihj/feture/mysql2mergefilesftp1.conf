env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="***********************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_5000w_copy` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "table_path"=1
        }





}
transform {
}
sink {
         MergeLocalFile {
             final_name="d://tmp//last.txt"
             path="d://tmp"
             tmp_path="d://tmp1"
             file_format_type="text"
             field_delimiter=","
             row_delimiter="\n"
             sink_columns=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
             batch_size="10000"
             compress_codec="none"
             overwrite_file=false
         }
}