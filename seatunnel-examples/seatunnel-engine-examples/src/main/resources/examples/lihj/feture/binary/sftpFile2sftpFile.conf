env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/module/backend/test/zl/apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
    file_format_type = "binary"
  }
}
transform {
}
sink {
    SftpFile {
        host = "**************"
        port = 22
        user = root
        password = joyadata
        path = "/tmp/zl1/"
        file_format_type = "binary"
    }
}