env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
    url="****************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="joyadata"
    query="select emp_id as id,emp_name as name from seatunnel_source.emp_104_1"
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="E000008_source_1"
    "parallelism"=1
    }
}
transform {
}
sink {
    Jdbc {
    url="jdbc:informix-sqli://192.168.5.127:9088/lr:INFORMIXSERVER=ol_informix1410"
    driver="com.informix.jdbc.IfxDriver"
    user="itest"
    password="itest"
    database="itest"
    table="test111"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="E000008_source_1"
    "generate_sink_sql"="true"
    }

}