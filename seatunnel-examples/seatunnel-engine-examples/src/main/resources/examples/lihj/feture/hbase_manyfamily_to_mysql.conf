env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Hbase {
        zookeeper_quorum = "192.168.90.113:2181"
        table="my_table1"
        query_columns=["cf2:org_id","cf2:account","cf1:emp_name","cf1:gender"]
        schema={
            columns=[{
                name="cf2:org_id"
                type=string
            },{
                name="cf2:account"
                type=string
            },{
                name="cf1:emp_name"
                type=string
            },{
                name="cf1:gender"
                type=string
            }]
        }
        result_table_name = hbase_source
    }
}
transform {
        FieldMapper {
            source_table_name = "hbase_source"
            result_table_name = "hbase_source1"
            field_mapper = {
                "cf2:org_id" = org_id
                "cf2:account" = account
                "cf1:emp_name" = emp_name
                "cf1:gender" = gender
            }
          }
}
sink {
    Jdbc {
            source_table_name = "hbase_source1"
            url="****************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="emp_104_19"
            "connection_check_timeout_sec"="30"
            "batch_size"="1000"
            "is_exactly_once"="false"
            "max_commit_attempts"="3"
            "transaction_timeout_sec"="-1"
            "max_retries"="0"
            "auto_commit"="true"
            "generate_sink_sql"="true"
        }

}
