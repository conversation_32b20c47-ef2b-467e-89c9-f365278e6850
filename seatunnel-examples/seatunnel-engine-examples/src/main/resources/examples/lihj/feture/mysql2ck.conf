env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select emp_id,emp_name from emp_quality"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="starrocks_source_1"
            "parallelism"=1
        }
}
transform {
}
sink {
    Clickhouse {
        host = "**************:8123"
        database = "default"
        table = "lihj_20240919"
        username = "default"
        password = "joyadata"
        support_upsert = false
        "field_ide"="lowercase"
        "pre_sql"=["ALTER TABLE lihj_20240919 DELETE WHERE emp_id = '21'","CREATE TABLE default.lihj_20240920 (emp_id String,emp_name String) ENGINE = MergeTree()ORDER BY emp_id"]
        "post_sql"=["ALTER TABLE lihj_20240919 DELETE WHERE emp_id = '31'","CREATE TABLE default.lihj_20240921 (emp_id String,emp_name String) ENGINE = MergeTree()ORDER BY emp_id"]
    }
}