env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/module/backend/test/zl/apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
    file_format_type = "binary"
  }
}
transform {
}
 sink {
     LocalFile {
         path="d://tmp1//sftp"
         file_format_type="binary"
         is_file=true
         file_name="aa.zip"
         validate_file="d://tmp1//sftp//lihj_20240920//20241030.ok"
         validate_content="这里是测试内容${file_name}_${file_size}_${file_md5}"
     }

 }
