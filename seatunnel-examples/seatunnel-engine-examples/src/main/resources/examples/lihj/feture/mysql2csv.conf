env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select * from csvtest"
        }
}
transform {
}
sink {
        LocalFile {
             path="d://tmp//opt//zsp//file"
             source_table_name = "E000001_source_1"
             "custom_filename"="true"
             tmp_path="d://tmp//opt//zsp//file//tmp"
             file_format_type="csv"
             field_delimiter=","
             row_delimiter="\n"
             batch_size="1000"
             compress_codec="none"
         }
}