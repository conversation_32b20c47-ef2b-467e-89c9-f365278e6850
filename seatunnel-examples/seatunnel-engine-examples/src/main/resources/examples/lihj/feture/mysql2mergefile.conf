env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="**********************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality_millions_100w` Where 1=1 AND emp_id<100001"
        "batch_size"="1000"
        "split.size"="1000"
        "table.path"="`emp_quality_millions_100w`"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="E000001_source_1"
        "parallelism"=1
    }
}
transform {
}
sink {
    MergeLocalFile {
        final_name="d://tmp//last.txt"
        path="d://tmp"
        tmp_path="d://tmp1"
        file_format_type="text"
        field_delimiter=","
        row_delimiter="\n"
        sink_columns=["emp_id","emp_name","gender","account","org_id","birth_date","age","nationality","province","city","email","phone","begin_date","remark","create_time","update_time"]
        batch_size="10000"
        compress_codec="none"
        overwrite_file=false
    }

}