env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
    url="*****************************************"
    driver="org.apache.hive.jdbc.HiveDriver"
    user="root"
    query="select `user_id`,`username`,`real_name`,`gender`,`age`,`mobile`,`email`,`id_card`,`address`,`education`,`occupation`,`reg_year` ,`register_date`from `user_registration` Where 1=1 "
    "fetch_size"="1000"
    "result_table_name"="E000005_source_1"
    "parallelism"=1
    "table_path"=1
    }
}
transform {
}
sink {
    Jdbc {
    url="**************************************"
    driver="org.apache.hive.jdbc.HiveDriver"
    user="root"
    database="default"
    table="ceshi013_lihj"
    "connection_check_timeout_sec"="30"
    "batch_size"="1024"
    "is_exactly_once"="false"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="E000005_source_1"
    "generate_sink_sql"="true"
    "field_ide"=LOWERCASE
    "enable_upsert"="false"
    "pk_strategy"="stop"
     "partition_keys"=["reg_year","ss"]
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }

}