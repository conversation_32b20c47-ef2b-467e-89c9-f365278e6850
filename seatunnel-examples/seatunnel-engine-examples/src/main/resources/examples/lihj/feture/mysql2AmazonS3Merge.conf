env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select * from emp_quality_millions_100w where emp_id <=10000"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="0"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
         "result_table_name"="starrocks_source_1"
        "partition_column"="emp_id"
         "partition_num"="10"
         "parallelism"=15
        }
}
transform {
}
sink {
    S3File {
        path="/lihj_20240920"
        tmp_path="/tmp/seatunnel"
        final_name="/lihj_20240920/********.ok"
        bucket="s3a://joyadata"
        "custom_filename"="true"
        file_name_expression="erp_emp_quality_millions_100w_000_add_${transactionId}"
        access_key="********************"
        secret_key="6ySgiYBTckBkBv7P8LA224LnjKWareQsIIIeZBZt"
        fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        fs.s3a.endpoint="s3.us-west-2.amazonaws.com"
        file_format_type="text"
        field_delimiter="\u0001"
        row_delimiter="\n"
        have_partition="false"
        sink_columns=["emp_id","emp_name","gender","account"]
        is_enable_transaction="true"
        batch_size="10000"
        filename_time_format="yyyy.MM.dd"
        compress_codec="none"
        validate_file="/lihj_20240920/********.ok"
        validate_content="${file_name}_${file_size}_${file_row}_${file_md5}"
    }
}