env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
	 Kafka {
		 topic="test"
		 bootstrap.servers="localhost:9092"
		 pattern="false"
		 consumer.group="SeaTunnel-Consumer-Group"
		 result_table_name="kafka_source_1"
		 commit_on_checkpoint="true"
		 schema= {
			 fields {
			 agent_send_timestamp=STRING
			 collector_recv_timestamp=STRING
			 "$.k8s.k8s_namespace"=STRING
			 "$.k8s.runtime"=STRING
			 "$.k8s.k8s_labels.app"=STRING
			 "$.k8s.image_id"=STRING
			 ip=STRING
			 source=STRING
			 hostname=STRING
			 appname=STRING
             tag=STRING
             timestamp=STRING
			 }
		 }
		 format="json"
		 format_error_handle_way="fail"
		 field_delimiter=""
		 start_mode=earliest
		 start_mode.offsets=""
		 start_mode.timestamp=""
		 partition-discovery.interval-millis="-1"
	 }



 }
 transform {
    FieldMapper{
            field_mapper={
            agent_send_timestamp=agent_send_timestamp
			 collector_recv_timestamp=collector_recv_timestamp
			 "$.k8s.k8s_namespace"=k8s_namespace
			 "$.k8s.runtime"=runtime
			 "$.k8s.k8s_labels.app"=app
			 "$.k8s.image_id"=image_id
			 ip=ip
			 source=source
			 hostname=hostname
			 appname=appname
             tag=tag
             timestamp=timestamp
            }
    }
 }
 sink {
        Jdbc {
            url="jdbc:mysql://**************:13306/seatunnel_sink?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="gongyinruixin"
            "connection_check_timeout_sec"="30"
            "batch_size"="1000"
            "is_exactly_once"="false"
            "auto_commit"="true"
            "support_upsert_by_query_primary_key_exist"="true"
            "generate_sink_sql"="true"
            "enable_upsert"="true"
        }

 }