env {
"job.mode"="BATCH"
"job.name"="10"
"parallelism"="10"
}
source {
        Jdbc {
        url="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select CODEID,CATE<PERSON><PERSON>YCODE,CATEGORYNAME,CODE,ASSISTANTCODE1,ASSISTANTCODE2,ASSISTANTCODE3,ASSISTANTCODE4,MNEMONICCODE,PARENTID,RECORDERCODE,R<PERSON><PERSON><PERSON>RDESC,REC<PERSON><PERSON><PERSON><PERSON>,PRETRIALCODE,PRETRIALDESC,PRETRIALTIME,AUDITORCODE,AUDITORDESC,AUDITTIME,AUDITLEVEL,AUDITFLAG,RETRIEVEFLAG,RETRIEVERCODE,RETRIEVERDESC,RETRIEVETIME,FREEZEFLAG,FREEZERCODE,FREEZERDESC,FREEZETIME,WORKFLOWID,RECORDERCORP,MDMCODE,AUDITLEVELNAME,VERSION,SYSCODEVERSION,FILINGFLAG,FILINGUSERCODE,FILINGUSERNAME,FILINGDATE,LUCENCEFLAG,LUCENCETIME,SUBMITCORP,DESCLONG,DESCSHORT,DESC1,DESC2,DESC3,DESC4,DESC5,DESC6,DESC7,DESC8,DESC9,DESC10,DESC100,REMARK,VALIDATEMSG,MODIFYPROPERTYGCODE,CATEGORYVERSION,MDMCODECREATEABLE,LASTMODIFYRECORDERCODE,LASTMODIFYRECORDERDESC,LASTMODIFYRECORDTIME,SUBMITTIME,FLOWPARA,FILECOUNT,TEMPSAVEFLAG,TASKFLAG,UUID,ERRORMSG,AUDITINGFLAG,RELEASEFLAG,DATASOURCEFLAG,LASTMODIFYSUBMITCORP,USEPARENTWORKFLOW,STANDINFO,SECURITYLEVELCODE,SUBMITCORPDESC,DESC11,DESC12,DESC13,DESC14,DESC15,DESC16,DESC17,DESC18,DESC19,DESC20,DESC21,DESC22,DESC23,DESC24,DESC25,DESC26,'2024-10-24' as etl_part from seatunnel_source.mdm_12ywgx_query  where 1=1 "
        "batch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "parallelism"=1
        }


}
transform {

}


sink {
        Jdbc {
        url="*************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="root"
            password=""
        database="test"
        table="stg_cfhec_mdm_12ywgx_query_f_d"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="mdm_12ywgx_query_source"
        "generate_sink_sql"="true"
            "field_ide"=LOWERCASE
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }


}