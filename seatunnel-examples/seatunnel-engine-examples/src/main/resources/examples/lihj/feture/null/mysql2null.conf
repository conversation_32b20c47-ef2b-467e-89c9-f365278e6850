env {
"job.mode"="BATCH"
"job.name"="10"
"parallelism"="10"
}
source {
        Jdbc {
        url="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from seatunnel_source.emp_104_1 where 1=1 and emp_id >= 1999 "
        "parallelism"=1
        "empty_data_strategy"="true"
        }


}
transform {

}


sink {
    LocalFile {
       path="d://tmp"
       tmp_path="d://tmp1"
       file_format_type="csv"
       field_delimiter=","
       row_delimiter="\n"
       final_name="d://tmp//emp_quality_millions_100w.csv"
       validate_file="d://tmp//emp_quality_millions_100w.ok"
       "empty_data_strategy"="true"
   }
}