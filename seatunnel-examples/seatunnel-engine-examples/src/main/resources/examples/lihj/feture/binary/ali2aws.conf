env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     OssFile {
             path="/lihj_sftp/apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
             bucket="oss://joyadata"
             access_key="LTAI5tPWRfPWF18QECwdDJRZ"
             access_secret="******************************"
             endpoint="http://cn-beijing.oss.aliyuncs.com"
             file_format_type="binary"
     }
 }
 transform {
 }
  sink {
      S3File {
          path="/lihj_20241106"
          tmp_path="/tmp/seatunnel"
          bucket="s3a://joyadata"
          access_key="********************"
          secret_key="6ySgiYBTckBkBv7P8LA224LnjKWareQsIIIeZBZt"
          fs.s3a.endpoint="s3.us-west-2.amazonaws.com"
          fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
          file_format_type="binary"
      }

  }