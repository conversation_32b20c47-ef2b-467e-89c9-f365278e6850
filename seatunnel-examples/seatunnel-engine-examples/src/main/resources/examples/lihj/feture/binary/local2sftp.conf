env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     LocalFile {
         path="G:\\github\\开源\\seatunnel\\seatunnel\\seatunnel-dist\\target\\apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
         file_format_type="binary"
     }

 }

sink {
    SftpFile {
        host = "**************"
        port = 22
        user = "root"
        password = "joyadata"
        path = "/opt/module/backend/test/zl"
        tmp_path = "/data/tmp"
        file_format_type = "binary"
    }
}
