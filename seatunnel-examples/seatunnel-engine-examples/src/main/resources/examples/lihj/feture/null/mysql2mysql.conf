env {
"job.mode"="BATCH"
"job.name"="10"
"parallelism"="10"
}
source {
        Jdbc {
        url="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from seatunnel_source.emp_104_1 where 1=1 and emp_id >= 1999 "
        "parallelism"=1
        "empty_data_strategy"="true"
        }


}
transform {

}


sink {
    Jdbc {
        url="****************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_source"
        table="emp_quality1112"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
        "enable_upsert"="false"
        "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        "empty_data_strategy"=true
    }
}