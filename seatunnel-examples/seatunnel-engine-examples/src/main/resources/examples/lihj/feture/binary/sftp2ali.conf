env {
   "job.mode"="BATCH"
    "job.name"="10"
}
source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/module/backend/test/zl/apache-seatunnel-2.3.9-SNAPSHOT-src.tar.gz"
    file_format_type = "binary"
  }
}
transform {
}
 sink {
     OssFile {
         path="/lihj_sftp"
         tmp_path="/tmp/seatunnel"
         bucket="oss://joyadata"
         access_key="LTAI5tPWRfPWF18QECwdDJRZ"
         access_secret="******************************"
         endpoint="http://cn-beijing.oss.aliyuncs.com"
         file_format_type="binary"
     }

 }
