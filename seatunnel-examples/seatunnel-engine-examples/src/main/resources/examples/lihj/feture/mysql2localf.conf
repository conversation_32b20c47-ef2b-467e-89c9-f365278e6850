env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select * from emp_quality_millions_100w where emp_id <=10000"
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="0"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
         "result_table_name"="starrocks_source_1"
        "partition_column"="emp_id"
         "partition_num"="10"
         "parallelism"=15
        }
}
transform {
}
sink {
        LocalFile {
             path="d://tmp//opt//zsp//file"
             source_table_name = "E000001_source_1"
               "custom_filename"="true"
                     file_name_expression="erp_emp_quality_millions_100w_000_add_${transactionId}"
             tmp_path="d:///tmp//opt//zsp//file//tmp"
             file_format_type="text"
             field_delimiter=","
             row_delimiter="\n"
             batch_size="1000"
             compress_codec="none"
             validate_file="d://tmp//lihj_20240920//20241030.ok"
             validate_content="${file_name}_${file_size}_${file_row}_${file_md5}"
         }
}