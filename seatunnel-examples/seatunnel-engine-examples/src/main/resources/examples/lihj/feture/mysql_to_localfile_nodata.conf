env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id` from emp_quality where emp_id<=-10"
    }
}
transform {

}
sink {
   MergeLocalFile {
       path="d://tmp"
       tmp_path="d://tmp1"
       file_format_type="csv"
       field_delimiter=","
       row_delimiter="\n"
       final_name="d://tmp//emp_quality_millions_100w.csv"
   }
}
