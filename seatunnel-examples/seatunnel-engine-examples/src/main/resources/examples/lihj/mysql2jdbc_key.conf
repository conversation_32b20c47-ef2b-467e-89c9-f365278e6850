env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
     Jdbc {
         url="**************************************************************************************************************************************************************************************"
         driver="com.mysql.cj.jdbc.Driver"
         user="root"
         password="joyadata"
         query="select * from `zsp_test`.`ck1` Where 1=1"
         "batch_size"="1000"
         "split.size"="1000"
         "connection_check_timeout_sec"="30"
         "snapshot.split.size"="8096"
         "snapshot.fetch.size"="1024"
         "connect.timeout.ms"="30000"
         "connection.pool.size"="20"
         "chunk-key.even-distribution.factor.lower-bound"="0.05"
         "chunk-key.even-distribution.factor.upper-bound"="100"
         "sample-sharding.threshold"="1000"
         "inverse-sampling.rate"="1000"
         "startup.mode"="INITIAL"
         "stop.mode"="NEVER"
         "result_table_name"="E000001_source_1"
     }
 
 
 
 }
 transform {
 }
 sink {
	 Jdbc {
		 url="*****************************************"
		 driver="org.apache.hive.jdbc.HiveDriver"
		 user="hive"
		 password="hive"
		 database="default"
		 table="zsp_test0828_name3"
		 "connection_check_timeout_sec"="30"
		 "batch_size"="1024"
		 "is_exactly_once"="false"
		 "max_commit_attempts"="3"
		 "transaction_timeout_sec"="-1"
		 "max_retries"="0"
		 "auto_commit"="true"
		 "support_upsert_by_query_primary_key_exist"="false"
		 "source_table_name"="kafka_source_1_trans_2"
		 "generate_sink_sql"="true"
		 "enable_upsert"="false"
		 "pk_strategy"="stop"
		 schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
	 }
 
 }
  