 env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        <PERSON> {
        fenodes="192.168.90.221:8030"
        username="root"
        password=""
        database="test"
        table="emp_20240703"
        query-port="9030"
            result_table_name="doris_source_1"
            doris.filter.query=" 1 = 1 "
            doris.batch.size="1024"
            doris.request.query.timeout.s="3600"
            doris.request.retries="3"
            doris.request.read.timeout.ms="30000"
            doris.request.connect.timeout.ms=30000
        }
}
transform {
}
sink {

       ConsoleHole{
       }
}