 env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
	Jdbc {
		url="***********************************************************************************************************************************************************************************************"
		driver="com.mysql.cj.jdbc.Driver"
		user="root"
		password="joyadata"
		query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality_millions_100w` Where 1=1 limit 30000 "
		"fetch_size"="1000"
	}




}
transform {
}
sink {
	console{}

}
