env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from user022"
            "result_table_name"="user02"
        }

}
transform {
    Filter {
        source_table_name = "user01"
        result_table_name = "user01_1"
        fields = [id, name]
        join_state="master"
      }
    Filter {
        source_table_name = "user02"
        result_table_name = "user02_1"
        fields = [user_id, score]
        join_state="slave"
      }
}
sink {
    Jdbc {
        "source_table_name"="user01_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
    Jdbc {
        "source_table_name"="user02_1"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }
}
