env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`age` from `seatunnel_source`.`emp_quality` where 1=1  "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "partition_column"="emp_id"
        "parallelism"=2
        }

}
transform {
}
sink {
  ObsFile {
       path="/********/zsp1"
       bucket="obs://cdyanfa"
       access_key="********************"
       security_key="AnpGOuW0GCAWBTcPrARQgG0fKKZ9K9QKO3uueG0W"
       endpoint="obs.cn-east-5.myhuaweicloud.com"
       file_format_type="text"
   }
}