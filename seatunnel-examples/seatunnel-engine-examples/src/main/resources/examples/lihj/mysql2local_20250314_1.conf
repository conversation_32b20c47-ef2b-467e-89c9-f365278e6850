env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="root"
            password="joyadata"
        query="select `id`,`name`,`amount` from zsp_0305 Where 1=1 limit 20"
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }




}
transform {
}
sink {
    MergeLocalFile {
            path="D://opt//zsp//file"
            final_name="D:\\opt\\zsp\\file\\zsp_hebing.txt"
            source_table_name = "E000001_source_1"
            tmp_path="d://opt//zsp//file//tmp"
            file_format_type="text"
            field_delimiter=","
            row_delimiter="\n"
            batch_size="10"
            compress_codec="none"
            "empty_data_strategy"=false
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            fixed_field_length_strategy="false"
            validates = true
            validate_file="D:\\opt\\zsp\\lihj"
            validate_content="${file_name}-${file_size}-${file_row}"
            clean_target_folder=true
            }
}