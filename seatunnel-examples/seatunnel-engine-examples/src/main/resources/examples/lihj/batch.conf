env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="*************************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select record_id,patient_id,visit_date,symptoms,diagnosis,ds from maoyantest_0906 where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="maoyantest_0906_source"
        "pre_sql"=[""]
        "post_sql"=[""]
        "parallelism"=1
        "empty_data_strategy"=true
    }
Jdbc {
    url="*************************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="Cdyanfa_123456"
    query="select test_id,patient_id,test_type,test_date,result_date from lab_test_out_3 where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="lab_test_out_3_source"
    "pre_sql"=[""]
    "post_sql"=[""]
    "parallelism"=1
    "empty_data_strategy"=true
}
Jdbc {
    url="*************************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="Cdyanfa_123456"
    query="select test_id,patient_id,test_type,test_date,result_date from lab_test_out_2 where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="lab_test_out_2_source"
    "pre_sql"=[""]
    "post_sql"=[""]
    "parallelism"=1
    "empty_data_strategy"=false
}
Jdbc {
    url="*************************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="Cdyanfa_123456"
    query="select patient_id,test_type,test_date,result_date,test_id from lab_test_out_0724 where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="lab_test_out_0724_source"
    "pre_sql"=[""]
    "post_sql"=[""]
    "parallelism"=1
    "empty_data_strategy"=false
}
Jdbc {
    url="*************************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="Cdyanfa_123456"
    query="select test_id,patient_id,test_type,test_date,result_date from lab_test_gpload_3 where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "snapshot.split.size"="8096"
    "snapshot.fetch.size"="1024"
    "connection.pool.size"="20"
    "chunk-key.even-distribution.factor.lower-bound"="0.05"
    "chunk-key.even-distribution.factor.upper-bound"="100"
    "sample-sharding.threshold"="1000"
    "inverse-sampling.rate"="1000"
    "startup.mode"="INITIAL"
    "stop.mode"="NEVER"
    "result_table_name"="lab_test_gpload_3_source"
    "pre_sql"=[""]
    "post_sql"=[""]
    "parallelism"=1
    "empty_data_strategy"=true
}



}
transform {
FieldMapper{
source_table_name="maoyantest_0906_source"
result_table_name="maoyantest_0906_source_trans_1"
field_mapper={
"record_id"="record_id"
"patient_id"="patient_id"
"visit_date"="visit_date"
"symptoms"="symptoms"
"diagnosis"="diagnosis"
"ds"="ds"
}
}
FieldMapper{
source_table_name="lab_test_out_3_source"
result_table_name="lab_test_out_3_source_trans_2"
field_mapper={
"test_id"="test_id"
"patient_id"="patient_id"
"test_type"="test_type"
"test_date"="test_date"
"result_date"="result_date"
}
}
FieldMapper{
source_table_name="lab_test_out_2_source"
result_table_name="lab_test_out_2_source_trans_3"
field_mapper={
"test_id"="test_id"
"patient_id"="patient_id"
"test_type"="test_type"
"test_date"="test_date"
"result_date"="result_date"
}
}
FieldMapper{
source_table_name="lab_test_out_0724_source"
result_table_name="lab_test_out_0724_source_trans_4"
field_mapper={
"patient_id"="patient_id"
"test_type"="test_type"
"test_date"="test_date"
"result_date"="result_date"
"test_id"="test_id"
}
}
FieldMapper{
source_table_name="lab_test_gpload_3_source"
result_table_name="lab_test_gpload_3_source_trans_5"
field_mapper={
"test_id"="test_id"
"patient_id"="patient_id"
"test_type"="test_type"
"test_date"="test_date"
"result_date"="result_date"
}
}
}
sink {
Jdbc {
url="************************************************"
driver="oracle.jdbc.driver.OracleDriver"
user="c##BJ"
    password="BJ"
database="ORCLCDB"
table="C##BJ.ODS_MAOYANTEST_0906_NNN1"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="maoyantest_0906_source_trans_1"
"generate_sink_sql"="true"
"field_ide"=UPPERCASE
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
"empty_data_strategy"=true
}
Jdbc {
url="************************************************"
driver="oracle.jdbc.driver.OracleDriver"
user="c##BJ"
    password="BJ"
database="ORCLCDB"
table="C##BJ.ODS_LAB_TEST_OUT_NNN1"
"connection_check_timeout_sec"="30"
"batch_size"="1000"
"is_exactly_once"="false"
"max_commit_attempts"="3"
"transaction_timeout_sec"="-1"
"auto_commit"="true"
"support_upsert_by_query_primary_key_exist"="false"
"source_table_name"="lab_test_out_3_source_trans_2"
"generate_sink_sql"="true"
"field_ide"=UPPERCASE
"enable_upsert"="false"
"pk_strategy"="stop"
schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
"empty_data_strategy"=true
}
Jdbc {
    url="************************************************"
    driver="oracle.jdbc.driver.OracleDriver"
    user="c##BJ"
    password="BJ"
    database="ORCLCDB"
    table="C##BJ.ODS_LAB_TEST_OUT_NNN1"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "max_commit_attempts"="3"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="lab_test_out_2_source_trans_3"
    "generate_sink_sql"="true"
    "field_ide"=UPPERCASE
    "enable_upsert"="false"
    "pk_strategy"="stop"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    "empty_data_strategy"=false
}
Jdbc {
    url="************************************************"
    driver="oracle.jdbc.driver.OracleDriver"
    user="c##BJ"
            password="BJ"
    database="ORCLCDB"
    table="C##BJ.ODS_LAB_TEST_OUT_0724_NNN1"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "max_commit_attempts"="3"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="lab_test_out_0724_source_trans_4"
    "generate_sink_sql"="true"
    "field_ide"=UPPERCASE
    "enable_upsert"="false"
    "pk_strategy"="stop"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    "empty_data_strategy"=false
}
    Jdbc {
        url="************************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="c##BJ"
        password="BJ"
        database="ORCLCDB"
        table="C##BJ.ODS_LAB_TEST_GPLOAD_3_NNN1"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="lab_test_gpload_3_source_trans_5"
        "generate_sink_sql"="true"
        "field_ide"=UPPERCASE
        "enable_upsert"="false"
        "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        "empty_data_strategy"=true
    }

}