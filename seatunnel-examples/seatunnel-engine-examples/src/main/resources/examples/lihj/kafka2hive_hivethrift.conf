env {
 "job.mode"="BATCH"
 "job.name"="10"
 }
 source {
	 Kafka {
		 topic="ljw_test"
		 bootstrap.servers="**************:9092"
		 pattern="false"
		 consumer.group="SeaTunnel-Consumer-Group"
		 result_table_name="kafka_source_1"
		 commit_on_checkpoint="true"
		 schema= {
			 fields {
			 agent_send_timestamp=STRING
			 collector_recv_timestamp=STRING
			 raw_message=STRING
			 ip=STRING
			 index=STRING
			 logical_index=STRING
			 logtype=STRING
			 hostname=STRING
			 appname=STRING
			 domain=STRING
			 context_id=STRING
			 tag=STRING
			 id=STRING
			 raw_message_length=INT
			 timestamp=STRING
			 }
		 }
		 format="json"
		 format_error_handle_way="fail"
		 field_delimiter=""
		 start_mode=earliest
		 start_mode.offsets=""
		 start_mode.timestamp=""
		 partition-discovery.interval-millis="-1"
	 }



 }
 transform {
	 SQL{
		source_table_name="kafka_source_1"
		result_table_name="kafka_source_1_trans_2"
		query="select agent_send_timestamp, collector_recv_timestamp, raw_message, ip, index, logical_index, logtype, hostname, appname, domain, context_id, tag, id, raw_message_length, timestamp from kafka_source_1 where 1=1"
	 }
	 SQL{
		source_table_name="kafka_source_1_trans_2"
		result_table_name="kafka_source_1_trans_2_trans_3"
		query="select agent_send_timestamp,collector_recv_timestamp,raw_message,ip,index,logical_index,logtype,hostname,appname,domain,context_id,tag,id,raw_message_length,substr(timestamp,1,10) as etl_date from kafka_source_1_trans_2"
	 }
 }
 sink {
	 Jdbc {
		 url="jdbc:hive2://**************:10000/default"
		 driver="org.apache.hive.jdbc.HiveDriver"
		 database="default"
		 table="ljw_table_test"
		 "connection_check_timeout_sec"="30"
		 "batch_size"="1024"
		 "is_exactly_once"="false"
		 "max_commit_attempts"="3"
		 "transaction_timeout_sec"="-1"
		 "max_retries"="0"
		 "auto_commit"="true"
		 "support_upsert_by_query_primary_key_exist"="false"
		 "source_table_name"="kafka_source_1_trans_2_trans_3"
		 "generate_sink_sql"="true"
		 "field_ide"=LOWERCASE
		 "enable_upsert"="false"
		 "pk_strategy"="continue"
		 schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
	 }
	 Hive {
		 source_table_name = "kafka_source_1_trans_2_trans_3"
		 table_name = "default.ljw_file_test"
		 metastore_uri = "thrift://**************:9083"
	 }

 }