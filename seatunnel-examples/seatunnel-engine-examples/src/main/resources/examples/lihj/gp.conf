env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="******************************************************************"
        driver="com.pivotal.jdbc.GreenplumDriver"
        user="gpadmin"
        password="gpadmin"
        query="select empno,ename,job,mgr,hiredate,sal,comm,deptno from emp Where 1=1 "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="emp_source_1"
        "parallelism"=2
    }

}
transform {

}
sink {
    Jdbc {
        url="******************************************************************"
        driver="com.pivotal.jdbc.GreenplumDriver"
            user="gpadmin"
            password="gpadmin"
        database="postgres"
        table="public.emp111"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="merge"
        "source_table_name"="emp_source_1_trans_2"
        "generate_sink_sql"="true"
    }
}