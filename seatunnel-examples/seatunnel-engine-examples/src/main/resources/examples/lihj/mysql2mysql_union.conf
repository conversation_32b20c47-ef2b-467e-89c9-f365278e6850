env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
            url="******************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select emp_id,emp_name,gender from emp_104_1 union select emp_id,emp_name,gender from emp_104"
            "batch_size"="1024"
            result_table_name="source1"
        }

}
transform {


}
sink {
    console{
    }
}
