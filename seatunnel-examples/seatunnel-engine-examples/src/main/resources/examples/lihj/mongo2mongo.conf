env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
    MongoDB{
        uri="mongodb://192.168.90.129:27017/"
        database="admin"
        collection="lihj"
        match.query = "{emp_id: {$gte:200}}"
        schema={
            fields{
                emp_id=bigint
                emp_name=string
                gender=string
                account=string
                org_id=string
                age=string
            }
        }

    }
}
transform {
}
sink {
    MongoDB{
        uri="mongodb://192.168.90.129:27017/"
        database="admin1"
        collection="lihj"
        schema={
            fields{
                emp_id=bigint
                emp_name=string
                gender=string
                account=string
                org_id=string
                age=string
            }
        }

    }
}