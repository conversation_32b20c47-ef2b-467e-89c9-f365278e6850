env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    HdfsFile {
    fs.defaultFS="hdfs://192.168.90.113:8020"
    path="/tmp/seatunnel/seatunnel/805687582955405313/ee3754f221/T_805687582955405313_ee3754f221_0_10/NON_PARTITION/T_805687582955405313_ee3754f221_0_10_0.txt"
    file_format_type="text"
    }

}
transform {

}
sink {
    HdfsFile{
        fs.defaultFS = "hdfs://192.168.90.113:8020"
        path = "/tmp/joyadata/20240528"
        file_format_type = "text"
        field_delimiter = ","
        row_delimiter = "\n"
        is_enable_transaction = true
    }

}
