env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
   LocalFile {
       path="d://emp_test111.csv"
       schema={
        fields{
            emp_id = int
            emp_name=string
            gender=string
            account=string
            org_id=string
            birth_date=string
            age=int
            nationality=string
            province=string
            city=string
            email=string
            phone=string
            begin_date=string
            remark=string
            create_time=string
            update_time=string
            }
       }
       file_format_type="csv"
       delimiter=","
   }
}
transform {
}
sink {
     Console{
     }
}

