env {
    "job.mode"="BATCH"
    "job.name"="10"
    execution.parallelism = 1
}
source {
    Jdbc {
        url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select * from user01"
        "result_table_name"="user01"
    }
        Jdbc {
            url="jdbc:mysql://**************:13306/seatunnel_source?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            query="select * from user022"
            "result_table_name"="user02"
        }
}
transform {
    RightJoin {
        source_table_name = "user02"
        result_table_name = "user02_1"
        fields = [user_id, score]
        primary_key="id"
        join_keys={
            id="user_id"
            name="chinese"
        }
        join_state="slave"
        table_id="20240604"
      }
      RightJoin {
            source_table_name = "user01"
            result_table_name = "user01_1"
            fields = [id,name,address,phone]
            master_fields=[id,name,address,phone,user_id, score]
            add_fields=[user_id, score]
            join_keys={
                id="user_id"
                name="chinese"
            }
            join_state="master"
            table_id="20240604"
      }
}
sink {
    console{
     "source_table_name"="user02_1"
    }

    Jdbc {
        "source_table_name"="user01_1"
        url="jdbc:mysql://**************:13306/seatunnel_sink?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="user_all_3"
        "generate_sink_sql"="true"
	    enable_upsert = true
    }

}
