env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="root"
            password="joyadata"
        query="select `id`,`name`,`amount` from zsp_0305 Where 1=1 limit 20"
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }




}
transform {
}
sink {
LocalFile {
        path="D://opt//aa//data//"
        custom_filename=true
        file_name_expression="test001_${transactionId}"
        is_enable_transaction=true
        tmp_path="d://opt//aa//data//tmp"
        file_format_type="text"
        field_delimiter="\u0001"
        row_delimiter="\n"
        batch_size="1000"
        compress_codec="none"
        validates="false"
        validate_file="d://opt//aa//flag//bbb"
        "empty_data_strategy"=false
        date_format="yyyy-MM-dd"
        datetime_format="yyyy-MM-dd HH:mm:ss"
        time_format="HH:mm:ss"
        fixed_field_length_strategy="false"
        clean_target_folder="true"
        }

}
