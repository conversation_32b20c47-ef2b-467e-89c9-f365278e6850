env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_quality` where 1=1   and `emp_id` > 100 and emp_id >'0' and emp_id <='1000'"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "partition_column"="emp_id"
        "parallelism"=2
        }




}
transform {
}
sink {
   Jdbc {
     driver = dm.jdbc.driver.DmDriver
     url = "jdbc:dm://192.168.90.232:5236"
     user = "SYSDBA"
     password = SYSDBA001
     primary_keys = [emp_id]
     database = "SYSDBA"
     table = "SYSDBA.emp_20240305"
     generate_sink_sql = true
     support_upsert_by_query_primary_key_exist = true
     query = ""
     fetch_size = 1000
   }
}
