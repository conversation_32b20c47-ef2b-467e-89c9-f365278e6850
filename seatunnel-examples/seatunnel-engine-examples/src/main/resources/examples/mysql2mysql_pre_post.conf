env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from emp_quality_millions_100w where emp_id<=20000 "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "partition_column"="emp_id"
        "parallelism"=2
        }




}
transform {
}
sink {
        Jdbc {
            url="****************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="emp_104_9"
            "connection_check_timeout_sec"="30"
            "batch_size"="1000"
            "is_exactly_once"="false"
            "max_commit_attempts"="3"
            "transaction_timeout_sec"="-1"
            "max_retries"="0"
            "auto_commit"="true"
            "support_upsert_by_query_primary_key_exist"="false"
            "generate_sink_sql"="true"
            "primary_keys"=[emp_id]
            "enable_upsert"="true"
        }


}
