env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="***********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="JOYADATA"
        password="joyadata"
        query="select * from JOYADATA.TEST1102"
        "batch_size"="1024"
        }




}
transform {
}
sink {
        Jdbc {
            url="****************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="clob_test"
            "support_upsert_by_query_primary_key_exist"="true"
            "generate_sink_sql"="true"
            "enable_upsert"="true"
        }


}
