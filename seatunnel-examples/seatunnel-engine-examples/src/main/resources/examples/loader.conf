env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="*****************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="xxx"
        query="select dbid,username from cms_user"
        "batch_size"="1024"
    }
}
transform {

}
sink {
    Jdbc {
        url="*********************************"
        driver="org.postgresql.Driver"
        user="xxx"
        password="xxx"
        database="xxx"
        table="dsg_test.dsg_20240422"
        "generate_sink_sql"="true"
        "use_copy_statement"="true"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
    Jdbc {
        url="*********************************"
        driver="org.postgresql.Driver"
        user="xxx"
        password="xxx"
        database="report"
        table="dsg_test.dsg_20240422"
        "generate_sink_sql"="true"
        "use_copy_statement"="true"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    }
}