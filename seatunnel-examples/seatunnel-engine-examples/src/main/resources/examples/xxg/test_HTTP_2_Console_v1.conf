env {
  execution.parallelism = 1
  #job.mode = "STREAMING"  # HTTP 作为数据源，支持批量和流式模式
  job.mode = "BATCH"
  checkpoint.interval = 10000  # 执行间隔（毫秒）
}

source {
  Http {
    url = "http://192.168.100.58:8000/api/tms/products/index"
    method = "get"
    format = "json"
    headers = {
          Authorization="Bearer example-token",
          language="zh",
          token="7745917fb03b4de186e1b65d0c5e1896"
    }
    params = {
          token="7745917fb03b4de186e1b65d0c5e1896"
          userId="fa438165b2c84d8dbe9175d152718437"
    }
    body = "{}"
    schema = {
          fields = {
            dbid = bigint
            tenantCode = string
            id = string
            groupType = string
            url = string
            isDefault = boolean
            name = string
          }
     }
    content_field = "$.result[*]"
    json_field = {
        "dbid": "$.dbid",
        "tenantCode": "$.tenantCode",
        "id": "$.id"
        "groupType": "$.groupType"
        "url": "$.url"
        "isDefault": "$.isDefault"
        "name": "$.name"
    }

    body_send_type = "raw"
    result_table_name = "user_info"
    connect_timeout_ms = "35"
    timeFormat="HH:mm:ss",
    dateFormat="yyyy/MM/dd",
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
  }
}

transform {
    Sql {
      source_table_name = "user_info"
      result_table_name = "user_info_out"
      query = "SELECT   *  FROM user_info"
    }
}

sink {
    Console {
       source_table_name = "user_info_out"
     }

}