env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Doris {
    fenodes="192.168.100.12:8030"
    username="root"
    password="Cdyanfa_123456"
    database="test"
    table="ods_emr_emr_coverpage"
    query-port="9030"
    result_table_name="doris_source_1"
    doris.filter.query=" 1 = 1  AND cureno > '1163186' and cureno >'0' and cureno <='1163239'"
    doris.batch.size="1024"
    doris.request.read.timeout.ms="30000"
    doris.request.connect.timeout.ms=30000
  }

}
transform {
  XmlPath{
    source_table_name="doris_source_1"
    result_table_name="doris_source_1_trans_1"
    timeFormat="HH:mm:ss"
    dateFormat="yyyy-MM-dd"
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
    lineSplit="#"
    mapping_keys= [
      {
        srcField="newcoverpage"
        xpath="/EmrCoverPage/Status"
        destField="Status"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="newcoverpage"
        xpath="/EmrCoverPage/CCHIInfo"
        destField="CCHIInfo"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
    ]
    output_fields= [
      {
        fieldName="cureno"
        fieldType="varchar"
        dataFormat=""
      }
      {
        fieldName="newcoverpage"
        fieldType="varchar"
        dataFormat=""
      }
      {
        fieldName="last_updatetime"
        fieldType="datetime"
        dataFormat=""
      }
    ]
  }
  FieldMapper{
    source_table_name="doris_source_1_trans_1"
    result_table_name="doris_source_1_trans_1_trans_2"
    field_mapper={
      "Status"="Status"
      "CCHIInfo"="CCHIInfo"
      "cureno"="cureno"
      "last_updatetime"="last_updatetime"
    }
  }
}
sink {
  Doris {
    fenodes="192.168.100.12:8030"
    query-port="9030"
    username="root"
    password="Cdyanfa_123456"
    database="test"
    table="ods_emr_emr_coverpage_target_xxg5_doris"
    table.identifier="test.ods_emr_emr_coverpage_target_xxg5_doris"
    source_table_name="doris_source_1_trans_1_trans_2"
    sink.label-prefix="8f5f66f2-eb39-4e7b-935e-078c95f49818"
    sink.buffer-size="256"
    sink.buffer-count="2"
    doris.batch.size=1024
    data_save_mode="CUSTOM_PROCESSING"
    custom_sql="delete from  test.ods_emr_emr_coverpage_target_xxg4_doris  where cureno > '1163186'"
    doris.config={
      format=json
      read_json_by_line=true
    }
  }

}
