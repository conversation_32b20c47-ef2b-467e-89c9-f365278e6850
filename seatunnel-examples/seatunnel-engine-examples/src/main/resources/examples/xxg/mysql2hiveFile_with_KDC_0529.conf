env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="************************************************************************************************************************************************************************************************"
    driver="com.mysql.jdbc.Driver"
    schema="test"
    user="root"
    password="Cdyanfa_123456"
    query="select `name`,`age` from `test`.`user` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000004_source_1"
    "parallelism"=1
    "table_path"=1
  }

}
transform {
}
sink {
  Hive {
    source_table_name = "E000004_source_1"
    table_name = "default.test_hive_file_0605"
    metastore_uri = "thrift://**************:9083"
    hdfs_site_path = "C:\\kdc\\hive-conf\\hdfs-site.xml"
    hive_site_path = "C:\\kdc\\hive-conf\\hive-site.xml"
    krb5_path = "C:\\kdc\\krb5.conf"
    kerberos_principal = "hive/<EMAIL>"
    "kerberos_keytab_path" = "C:\\kdc\\hive.keytab"
  }

}
