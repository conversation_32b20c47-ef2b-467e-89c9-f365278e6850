env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="*****************************************"
    driver="com.mysql.jdbc.Driver"
    schema="gmall_sc"
    user="root"
    password="Cdyanfa_123456"
    query="select `emr_id`,`sectionno`,`cureno`,`sectionname`,`view_date`,`type`,`xmlisland`,`creator`,`creator_code`,`create_date`,`update_date`,`isdeleted`,`isarchive`,`remark`,`checker`,`checker_code`,`check_date`,`savestatus`,`template_id`,`dynamicrule`,`templateversion`,`queueid`,`flag`,`DSG_OPC`,`dsg_scn`,`last_updatetime` from `ods_nursingrecord_emr_doc` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000001_source_1"
    "parallelism"=1
    "table_path"=1
  }

}
transform {
  XmlPath{
    source_table_name="E000001_source_1"
    result_table_name="E000001_source_1_trans_1"
    timeFormat="HH:mm:ss"
    dateFormat="yyyy-MM-dd"
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
    lineSplit="###"
    mapping_keys= [
      {
        srcField="xmlisland"
        xpath="/DocObjContent/NewCtrl[@Type]"
        destField="TypeStr"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="xmlisland"
        xpath="/DocObjContent/NewCtrl/Content_Text"
        destField="Content_Text"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="xmlisland"
        xpath="/DocObjContent/NewCtrl[@Id]"
        destField="Id"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="xmlisland"
        xpath="/DocObjContent/NewCtrl[@SelectItemIndex]"
        destField="SelectItemIndex"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="xmlisland"
        xpath="/DocObjContent/NewCtrl[@IsChecked]"
        destField="IsChecked"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
    ]
    output_fields= [
    ]
  }
}
sink {
  console {
    source_table_name="E000001_source_1_trans_1"
  }

    Doris {
    fenodes="192.168.100.12:8030"
    query-port="9030"
    username="root"
    password="Cdyanfa_123456"
    database="gmall_sc"
    table="ods_emr_emr_coverpage_target"
    table.identifier="gmall_sc.ods_emr_emr_coverpage_target_v1"
    source_table_name="E000001_source_1_trans_1"
    sink.enable-2pc="false"
    data_save_mode="APPEND_DATA"
    doris.config={
      format=json
      read_json_by_line=true
    }
  }

}
