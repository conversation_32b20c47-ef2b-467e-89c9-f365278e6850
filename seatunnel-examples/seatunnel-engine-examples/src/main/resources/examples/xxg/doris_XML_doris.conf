env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            schema="test"
            user="root"
            password="***"
            query="select `id`,`user_info_xml`,`other_info_xml`,`create_time` from `test`.`source_xml_user_new` Where 1=1 "
            "fetch_size"="1000"
            "split.size"="1000"
            "connection_check_timeout_sec"="30"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "table_path"=1
        }
}
transform {
        XmlPath{
            source_table_name="E000001_source_1"
            result_table_name="E000001_source_1_trans_1"
                    timeFormat="HH:mm:ss"
                    dateFormat="yyyy-MM-dd"
                    datetimeFormat="yyyy-MM-dd HH:mm:ss"
                    lineSplit="#"
                        mapping_keys= [
                           {
                                srcField="user_info_xml"
                                xpath="/DocObjContent/NewCtrl/Content_Text"
                                destField="Content_Text"
                                destType="STRING"
                                dataFormat=""
                                defaultValue=""
                           }
                           {
                                srcField="other_info_xml"
                                xpath="/EmrCoverPage/PatientBasicInfo/TransferTime3[@nil]"
                                destField="nil"
                                destType="STRING"
                                dataFormat=""
                                defaultValue="false"
                           }
                        ]
                    output_fields= [
                        {
                            fieldName="id"
                            fieldType="BIGINT"
                            dataFormat=""
                        }
                        {
                            fieldName="user_info_xml"
                            fieldType="LONGTEXT"
                            dataFormat=""
                        }
                        {
                            fieldName="other_info_xml"
                            fieldType="LONGTEXT"
                            dataFormat=""
                        }
                        {
                            fieldName="create_time"
                            fieldType="TIMESTAMP"
                            dataFormat=""
                        }
                    ]
        }
        FieldMapper{
            source_table_name="E000001_source_1_trans_1"
            result_table_name="E000001_source_1_trans_1_trans_2"
                    field_mapper={
                        "Content_Text"="Content_Text"
                        "nil"="nil"
                        "id"="id"
                        "other_info_xml"="custom_field_uuid71"
                        "create_time"="create_time"
                    }
        }
}
sink {
        Doris {
        fenodes="192.168.100.12:8030"
        query-port="9030"
        username="root"
            password="***"
        database="test"
        table="target_user_doris_test"
            table.identifier="test.target_user_doris_test"
            source_table_name="E000001_source_1_trans_1_trans_2"
            sink.enable-2pc="false"
            data_save_mode="APPEND_DATA"
            doris.config={
                format=json
                read_json_by_line=true
            }
        }

}
