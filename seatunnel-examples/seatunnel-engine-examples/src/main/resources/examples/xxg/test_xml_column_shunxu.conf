env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Doris {
    fenodes="192.168.90.215:8030"
    username="root"
    password="Cdyanfa_123456"
    database="test"
    table="ods_emr_emr_coverpage_v3"
    query-port="9030"
    result_table_name="doris_source_1"
    doris.filter.query=" 1 = 1 "
    doris.batch.size="1024"
    doris.request.read.timeout.ms="30000"
    doris.request.connect.timeout.ms=30000
    query= "select lastprinttime ,last_updatetime  from  test.ods_emr_emr_coverpage_v3 ",
  }

}
transform {
  XmlPath{
    source_table_name="doris_source_1"
    result_table_name="doris_source_1_trans_1"
    timeFormat="HH:mm:ss"
    dateFormat="yyyy-MM-dd"
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
    lineSplit="@@"
    mapping_keys= [
      {
        srcField="newattachedpagepublicinfos"
        xpath="/values/value"
        destField="value"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="newattachedpagepublicinfos"
        xpath="/values/value[@id]"
        destField="id"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
    ]
    output_fields= [
      {
        fieldName="cureno"
        fieldType="varchar"
        dataFormat=""
      }
      {
        fieldName="last_updatetime"
        fieldType="datetime"
        dataFormat=""
      }
      {
        fieldName="newattachedpagepublicinfos"
        fieldType="varchar"
        dataFormat=""
      }
    ]
  }
  FieldMapper{
    source_table_name="doris_source_1_trans_1"
    result_table_name="doris_source_1_trans_1_trans_2"
    field_mapper={
      "value"="value"
      "id"="id"
      "cureno"="cureno"
      "last_updatetime"="last_updatetime"
    }
  }
}
sink {
  Doris {
    fenodes="192.168.90.215:8030"
    query-port="9030"
    username="root"
    password="Cdyanfa_123456"
    database="test"
    table="ods_emr_emr_coverpage_public_04263"
    table.identifier="test.ods_emr_emr_coverpage_public_04263"
    source_table_name="doris_source_1_trans_1_trans_2"
    sink.label-prefix="2b68144f-d77e-4596-b523-b7af27b7e130"
    sink.buffer-size="256"
    sink.buffer-count="2"
    doris.batch.size=1024
    data_save_mode="DROP_DATA"
    doris.config={
      format=json
      read_json_by_line=true
    }
  }

}
