env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="************************************************************************************************************************************************************************************************"
    driver="com.mysql.jdbc.Driver"
    schema="test"
    user="root"
    password="Cdyanfa_123456"
    query="select `id`,`id_name`,`id_age`,`text`,`subject_id`,`city_name`,`city_id`,`birth_date`,`postalcode`,`ipaddress`,`address`,`bankcard`,`idcard`,`phone` from `test`.`t_user` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000001_source_1"
    "parallelism"=1
    "table_path"=1
  }





}
transform {
}
sink {
  Hive {
    source_table_name = "E000001_source_1"
    table_name = "test.test_hive_file_0524"
    metastore_uri = "thrift://**************:9083"
    hdfs_site_path = "C://kdc/86f81ee6aace400aae6045558dc8e099/hdfs-site.xml"
    hive_site_path = "C://kdc/86f81ee6aace400aae6045558dc8e099/hive-site.xml"
  }

}
