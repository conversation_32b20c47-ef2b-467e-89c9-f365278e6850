env {
  execution.parallelism = 1
  #job.mode = "STREAMING"  # HTTP 作为数据源，支持批量和流式模式
  job.mode = "BATCH"
  checkpoint.interval = 10000  # 执行间隔（毫秒）
}

source {
  Http {
    url = "http://localhost:18189/dedp/v1/di/api/testPutNoPageNum"
    method = "put"
    format = "json"
    headers = {
          Authorization="Bearer example-token",
          language="zh",
          token="7745917fb03b4de186e1b65d0c5e1896"
    }
    params = {
    }
    body = "{\"types\":\"ALL\",\"depth\":\"2\",\"idTo\":\"\",\"nameTo\":\"XXXX 公司\",\"idFrom\":\"\",\"nameFrom\":\"XXXX 公司\"}"
    schema = {
          fields = {
            proportion = string
            graphId = string
            tenPercent = string
            type = string
            tenTotal = string
            id = string
            name = string
          }
     }
    json_field = {
        proportion="$.result[*].holderList[*].proportion",
        graphId= "$.result[*].holderList[*].graphId",
         tenPercent= "$.result[*].holderList[*].tenPercent"
         type= "$.result[*].holderList[*].type"
         tenTotal= "$.result[*].holderList[*].tenTotal"
         id= "$.result[*].holderList[*].id"
         name= "$.result[*].holderList[*].name"
    }

    body_send_type = "raw"
    result_table_name = "user_info"
    connect_timeout_ms = "15"
    timeFormat="HH:mm:ss",
    dateFormat="yyyy/MM/dd",
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
  }
}

transform {
    Sql {
      source_table_name = "user_info"
      result_table_name = "user_info_out"
      query = "SELECT *  FROM user_info"
    }
}

sink {
    Console {
       source_table_name = "user_info_out"
     }
}