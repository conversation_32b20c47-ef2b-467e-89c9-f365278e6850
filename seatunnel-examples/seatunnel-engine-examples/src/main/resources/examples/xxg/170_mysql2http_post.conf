env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="******************************************************************************************************************************************************************************************"
    driver="com.mysql.jdbc.Driver"
    schema="qjq"
    user="root"
    password="Cdyanfa_123456"
    query="select `task_id`,`task_name`,`run_type`,`task_type`,`current_version_id`,`tmp_version_id`,`engine_type`,`source_type`,`sub_import_mode`,`create_by`,`create_time`,`update_by`,`update_time`,`create_user_id`,`create_dept_id`,`remark`,`project_id`,`tenant_id`,`offline_type`,`online_status`,`version_status`,`debug_task_instance_id`,`task_instance_id`,`version_num`,`catalogue_id`,`parent_id`,`parent_level`,`parent_name`,`submit_status`,`source_ip`,`target_ip`,`config_status`,`task_status`,`catalog_name`,`engine_condition_params_entity`,`engine_condition_all_params_entity`,`task_catalogue_name`,`name`,`is_leaf` from `qjq`.`task` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000008_source_1"
    "parallelism"=1
    "table_path"=1
  }





}
transform {
  FieldMapper{
    source_table_name="E000008_source_1"
    result_table_name="E000008_source_1_trans_1"
    field_mapper={
      "task_id"="taskId"
      "task_name"="taskName"
      "run_type"="runType"
      "task_type"="taskType"
      "current_version_id"="currentVersionId"
      "tmp_version_id"="tmpVersionId"
      "engine_type"="engineType"
      "source_type"="sourceType"
      "sub_import_mode"="subImportMode"
      "create_by"="createBy"
      "create_time"="createTime"
      "update_by"="updateBy"
      "update_time"="updateTime"
      "create_user_id"="createUserId"
      "create_dept_id"="createDeptId"
      "remark"="remark"
      "project_id"="projectId"
      "tenant_id"="tenantId"
      "offline_type"="offlineType"
      "online_status"="onlineStatus"
      "version_status"="versionStatus"
      "debug_task_instance_id"="debugTaskInstanceId"
      "task_instance_id"="taskInstanceId"
      "version_num"="versionNum"
      "catalogue_id"="catalogueId"
      "parent_id"="parentId"
      "parent_level"="parentLevel"
      "parent_name"="parentName"
      "submit_status"="submitStatus"
      "source_ip"="sourceIp"
      "target_ip"="targetIp"
      "config_status"="configStatus"
      "task_status"="taskStatus"
      "catalog_name"="catalogName"
      "engine_condition_params_entity"="engineConditionParamsEntity"
      "engine_condition_all_params_entity"="engineConditionAllParamsEntity"
      "task_catalogue_name"="taskCatalogueName"
      "name"="name"
      "is_leaf"="isLeaf"
    }
  }
}
sink {

  HTTP {
    source_table_name="E000008_source_1_trans_1"
    url="http://***************:8000/api/integration/catalogue?method=PUT"
    method="POST"
    params= {
    }
    headers = {
      product="11053212232192"
      productid="11053212232192"
      project="15854169415552"
      projectid="15854169415552"
      Content-Type="application/json;charset=UTF-8"
      token="57a3f42bc0eb4de99fcb330572c1a0a5"
    }
    sink_output_columns = [
      {
        srcColumnName="task_id"
        srcColumnType="VARCHAR"
        destFieldName="taskId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="task_name"
        srcColumnType="VARCHAR"
        destFieldName="taskName"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="run_type"
        srcColumnType="VARCHAR"
        destFieldName="runType"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="task_type"
        srcColumnType="TINYINT"
        destFieldName="taskType"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="current_version_id"
        srcColumnType="VARCHAR"
        destFieldName="currentVersionId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="tmp_version_id"
        srcColumnType="VARCHAR"
        destFieldName="tmpVersionId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="engine_type"
        srcColumnType="TINYINT"
        destFieldName="engineType"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="source_type"
        srcColumnType="TINYINT"
        destFieldName="sourceType"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="sub_import_mode"
        srcColumnType="VARCHAR"
        destFieldName="subImportMode"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="create_by"
        srcColumnType="VARCHAR"
        destFieldName="createBy"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="create_time"
        srcColumnType="DATETIME"
        destFieldName="createTime"
        destFieldType="String"
        destLength="19"
        defaultValue=""
      }
      {
        srcColumnName="update_by"
        srcColumnType="VARCHAR"
        destFieldName="updateBy"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="update_time"
        srcColumnType="DATETIME"
        destFieldName="updateTime"
        destFieldType="String"
        destLength="19"
        defaultValue=""
      }
      {
        srcColumnName="create_user_id"
        srcColumnType="VARCHAR"
        destFieldName="createUserId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="create_dept_id"
        srcColumnType="VARCHAR"
        destFieldName="createDeptId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="remark"
        srcColumnType="VARCHAR"
        destFieldName="remark"
        destFieldType="String"
        destLength="500"
        defaultValue=""
      }
      {
        srcColumnName="project_id"
        srcColumnType="VARCHAR"
        destFieldName="projectId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="tenant_id"
        srcColumnType="VARCHAR"
        destFieldName="tenantId"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="offline_type"
        srcColumnType="TINYINT"
        destFieldName="offlineType"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="online_status"
        srcColumnType="TINYINT"
        destFieldName="onlineStatus"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="version_status"
        srcColumnType="TINYINT"
        destFieldName="versionStatus"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="debug_task_instance_id"
        srcColumnType="VARCHAR"
        destFieldName="debugTaskInstanceId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="task_instance_id"
        srcColumnType="VARCHAR"
        destFieldName="taskInstanceId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="version_num"
        srcColumnType="VARCHAR"
        destFieldName="versionNum"
        destFieldType="String"
        destLength="20"
        defaultValue=""
      }
      {
        srcColumnName="catalogue_id"
        srcColumnType="VARCHAR"
        destFieldName="catalogueId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="parent_id"
        srcColumnType="VARCHAR"
        destFieldName="parentId"
        destFieldType="String"
        destLength="32"
        defaultValue=""
      }
      {
        srcColumnName="parent_level"
        srcColumnType="VARCHAR"
        destFieldName="parentLevel"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="parent_name"
        srcColumnType="VARCHAR"
        destFieldName="parentName"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="submit_status"
        srcColumnType="TINYINT"
        destFieldName="submitStatus"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
      {
        srcColumnName="source_ip"
        srcColumnType="VARCHAR"
        destFieldName="sourceIp"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="target_ip"
        srcColumnType="VARCHAR"
        destFieldName="targetIp"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="config_status"
        srcColumnType="VARCHAR"
        destFieldName="configStatus"
        destFieldType="String"
        destLength="20"
        defaultValue=""
      }
      {
        srcColumnName="task_status"
        srcColumnType="VARCHAR"
        destFieldName="taskStatus"
        destFieldType="String"
        destLength="20"
        defaultValue=""
      }
      {
        srcColumnName="catalog_name"
        srcColumnType="VARCHAR"
        destFieldName="catalogName"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="engine_condition_params_entity"
        srcColumnType="TEXT"
        destFieldName="engineConditionParamsEntity"
        destFieldType="String"
        destLength="65535"
        defaultValue=""
      }
      {
        srcColumnName="engine_condition_all_params_entity"
        srcColumnType="TEXT"
        destFieldName="engineConditionAllParamsEntity"
        destFieldType="String"
        destLength="65535"
        defaultValue=""
      }
      {
        srcColumnName="task_catalogue_name"
        srcColumnType="VARCHAR"
        destFieldName="taskCatalogueName"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="name"
        srcColumnType="VARCHAR"
        destFieldName="name"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="is_leaf"
        srcColumnType="TINYINT"
        destFieldName="isLeaf"
        destFieldType="number"
        destLength="3"
        defaultValue=""
      }
    ]
    body_send_type="raw"
    connect_timeout_ms = "15"
    datetimeFormat = "yyyy-MM-dd HH:mm:ss"
    dateFormat ="yyyy-MM-dd"
    timeFormat ="HH:mm:ss"
  }
}
