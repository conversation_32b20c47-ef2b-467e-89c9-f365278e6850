env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="************************************************************************************************************************************************************************************************"
    driver="com.mysql.jdbc.Driver"
    schema="test"
    user="root"
    password="Cdyanfa_123456"
    query="select `id`,`id_name`,`id_age`,`text`,`subject_id`,`city_name`,`city_id`,`birth_date`,`postalcode`,`ipaddress`,`address`,`bankcard`,`idcard`,`phone` from `test`.`t_user` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000001_source_1"
    "parallelism"=1
    "table_path"=1
  }





}
transform {
}
sink {
  Jdbc {
    url="*********************************************************"
    driver="org.apache.hive.jdbc.HiveDriver"
    user="root"
    password="***"
    database="test"
    table="test_xxg_05241"
    "connection_check_timeout_sec"="30"
    "batch_size"="1024"
    "is_exactly_once"="false"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="E000001_source_1"
    "generate_sink_sql"="true"
    "field_ide"=LOWERCASE
    "enable_upsert"="false"
    "pk_strategy"="stop"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    "partition_keys"=[]
  }

}
