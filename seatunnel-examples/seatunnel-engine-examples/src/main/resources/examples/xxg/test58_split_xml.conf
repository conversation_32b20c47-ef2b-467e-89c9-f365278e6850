env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="*****************************************"
    driver="com.mysql.jdbc.Driver"
    schema="gmall_sc"
    user="root"
    password="Cdyanfa_123456"
    query="select `cureno`,`newattachedpagepublicinfos` from `ods_emr_emr_coverpage` Where 1=1 "
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000014_source_1"
    "parallelism"=1
    "table_path"=1
  }





}
transform {
  XmlPath{
    source_table_name="E000014_source_1"
    result_table_name="E000014_source_1_trans_1"
    timeFormat="HH:mm:ss"
    dateFormat="yyyy-MM-dd"
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
    lineSplit="@$$@"
    mapping_keys= [
      {
        srcField="newattachedpagepublicinfos"
        xpath="/values/value"
        destField="value"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
      {
        srcField="newattachedpagepublicinfos"
        xpath="/values/value[@id]"
        destField="id"
        destType="STRING"
        dataFormat=""
        defaultValue=""
      }
    ]
    output_fields= [
      {
        fieldName="cureno"
        fieldType="varchar"
        dataFormat=""
      }
      {
        fieldName="newattachedpagepublicinfos"
        fieldType="varchar"
        dataFormat=""
      }
    ]
  }
  FieldMapper{
    source_table_name="E000014_source_1_trans_1"
    result_table_name="E000014_source_1_trans_1_trans_2"
    field_mapper={
      "value"="value"
      "id"="id"
      "cureno"="cureno"
    }
  }
}
sink {
  Doris {
    fenodes="192.168.100.12:8030"
    query-port="9030"
    username="root"
    password="Cdyanfa_123456"
    database="gmall_sc"
    table="test_0422"
    table.identifier="gmall_sc.test_0422"
    source_table_name="E000014_source_1_trans_1_trans_2"
    sink.enable-2pc="false"
    data_save_mode="APPEND_DATA"
    doris.config={
      format=json
      read_json_by_line=true
    }
  }

}
