env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*****************************************"
        driver="com.mysql.jdbc.Driver"
            schema="gmall_sc"
            user="root"
            password="Cdyanfa_123456"
            query="select `cureno`,`coverpage`,`attachedpage`,`newcoverpage`,`newattachedpagepublicinfos`,`newattachedpagedeptinfos`,`newcoverpagestatus`,`coverpagehtml`,`createcode`,`createname`,`createtime`,`lastsavecode`,`lastsavename`,`lastsavetime`,`lastprintcode`,`lastprintname`,`lastprinttime`,`DSG_OPC`,`dsg_scn`,`last_updatetime` from `ods_emr_emr_coverpage` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
            "result_table_name"="E000004_source_1"
            "parallelism"=1
            "table_path"=1
        }
}
transform {
        XmlPath{
            source_table_name="E000004_source_1"
            result_table_name="E000004_source_1_trans_1"
                    timeFormat="HH:mm:ss"
                    dateFormat="yyyy-MM-dd"
                    datetimeFormat="yyyy-MM-dd HH:mm:ss"
                    lineSplit="##"
                        mapping_keys= [
                           {
                                srcField="newcoverpage"
                                xpath="/EmrCoverPage/PatientInspectionInfo/PrincipaliDagnosisCureInfo"
                                destField="PrincipaliDagnosisCureInfo"
                                destType="STRING"
                                dataFormat=""
                                defaultValue=""
                           }
                           {
                                srcField="newcoverpage"
                                xpath="/EmrCoverPage/PatientDiagnosisInfo/PatientDiagnosisInfo/DiagnosisType"
                                destField="DiagnosisType"
                                destType="STRING"
                                dataFormat=""
                                defaultValue=""
                           }
                           {
                                srcField="newcoverpage"
                                xpath="/EmrCoverPage/PatientDiagnosisInfo/PatientDiagnosisInfo/DiagnosisTypeDisPlay"
                                destField="DiagnosisTypeDisPlay"
                                destType="STRING"
                                dataFormat=""
                                defaultValue="无"
                           }
                           {
                                srcField="newcoverpage"
                                xpath="/EmrCoverPage/PatientDiagnosisInfo/PatientDiagnosisInfo/DiagnosisDate"
                                destField="DiagnosisDate"
                                destType="STRING"
                                dataFormat=""
                                defaultValue="2025-04-10T08:24:01"
                           }
                        ]
                    output_fields= [
                        {
                            fieldName="cureno"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="coverpage"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="attachedpage"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="newcoverpage"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="newattachedpagepublicinfos"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="newattachedpagedeptinfos"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="newcoverpagestatus"
                            fieldType="int"
                            dataFormat=""
                        }
                        {
                            fieldName="coverpagehtml"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="createcode"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="createname"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="createtime"
                            fieldType="datetime"
                            dataFormat=""
                        }
                        {
                            fieldName="lastsavecode"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="lastsavename"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="lastsavetime"
                            fieldType="datetime"
                            dataFormat=""
                        }
                        {
                            fieldName="lastprintcode"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="lastprintname"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="lastprinttime"
                            fieldType="datetime"
                            dataFormat=""
                        }
                        {
                            fieldName="DSG_OPC"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="dsg_scn"
                            fieldType="varchar"
                            dataFormat=""
                        }
                        {
                            fieldName="last_updatetime"
                            fieldType="datetime"
                            dataFormat=""
                        }
                    ]
        }
}
sink {
          console {
                  source_table_name="E000004_source_1_trans_1"
                }

}

