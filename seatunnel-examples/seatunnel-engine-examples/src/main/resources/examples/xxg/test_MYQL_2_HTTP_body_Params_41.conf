env {
  execution.parallelism = 2
  job.mode = "BATCH"  # MySQL 作为数据源，只支持批量同步
}

source {
   jdbc {
     url =  "*****************************************"
     driver = "com.mysql.cj.jdbc.Driver"
     connection_check_timeout_sec = 100
     user = "root"
     password = "Cdyanfa_123456"
     query = "SELECT * FROM csc_event_record ORDER BY create_time   limit  5"
     result_table_name = "user_info_out"
  }
}

transform {
    Sql {
      source_table_name = "user_info_out"
      result_table_name = "user_info_sink"
      query = "select  dbid, event_name,event_code,project_id,project_name,business_id,business_name,content,data,tenant_code,create_time,error_msg from user_info_out"
    }
}

sink {
  Console {
    source_table_name = "user_info_sink"
  }

  http {
    source_table_name = "user_info_sink"
    url = "http://192.168.100.58:8858/dedp/v1/csc/event_record"
    method = "POST"
    params= {
          token: "08d5c05dda1d49658df8d845a2227f68"
    }
    sink_output_columns = [
             {srcColumnName:"event_name",destFieldName:"eventName",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"event_code",destFieldName:"eventCode",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"tenant_code",destFieldName:"tenantCode",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"project_id",destFieldName:"projectId",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"create_time",destFieldName:"date",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"business_id",destFieldName:"businessId",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"data",destFieldName:"data",destFieldType:"object",destLength:"11",defaultValue:"0"}
             {srcColumnName:"business_name",destFieldName:"data.businessName",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"error_msg",destFieldName:"data.errorMsg",destFieldType:"string",destLength:"11",defaultValue:"0"},
             {srcColumnName:"dbid",destFieldName:"dbid",destFieldType:"number",destLength:"11",defaultValue:"0"},
    		     {srcColumnName:"create_time",destFieldName:"create_time",destFieldType:"string",destLength:"11",defaultValue:"0"}
    ]
    headers = {
      token = "08d5c05dda1d49658df8d845a2227f68"
    }
    connect_timeout_ms = "15"
    body_send_type = "raw"
    timeFormat="HH:mm:ss",
    dateFormat="yyyy/MM/dd",
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
  }
}