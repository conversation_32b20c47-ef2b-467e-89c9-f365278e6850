 env   {
    job.name= "test"
  }
 
 
 source  {
    Jdbc{
       query= "SELECT  *  FROM   source_xml_user ",
       fetch_size= 10,
       url= "**********************************",
       driver= "com.mysql.cj.jdbc.Driver",
       user= "root",
       password= "123456$",
       plugin_name= "Jdbc",
       result_table_name="E000001_source_1"
    }
  }
  
transform {
    XmlPathV2 {
       plugin_name= "XmlPathV2",
       timeFormat="HH:mm:ss",
       dateFormat="yyyy/MM/dd",
       datetimeFormat="yyyy-MM-dd HH:mm:ss"
       mapping_keys= [
        {
           srcField= "user_info_xml"
           xpath= "/user/name"
           destField= "name"
           destType= "string"
           dataFormat=""
        }
        {
           srcField= "user_info_xml"
           xpath= "/user/age"
           destField= "age"
           destType= "int"
           dataFormat=""
        }
        {
           srcField= "user_info_xml"
           xpath= "/user/addr"
           destField= "addr"
           destType= "string"
           dataFormat=""
        }
        {
                   srcField= "user_info_xml"
                   xpath= "/user/beijingTime"
                   destField= "beijingTime"
                   destType= "DATE"
                   dataFormat =""
        }
        {
           srcField= "other_info_xml"
           xpath= "/conf/teacher/teacherName"
           destField= "teacherName"
           destType= "string"
           dataFormat=""
        }
        {
           srcField= "other_info_xml",
           xpath= "/conf/grades/grade[@class!=\"003\"]/studentName"
           destField= "studentName"
           destType= "string"
           dataFormat=""
        }
      ]
      output_fields= [
          {
             fieldName="id"
             fieldType="BIGINT"
             dataFormat=""
          }
        {
               fieldName="create_time"
               fieldType="DATE"
               dataFormat="yyyy-MM-dd"
         }
      ]
      source_table_name="E000001_source_1",
      result_table_name="E000001_source_1_trans_1"
    }
}
  
sink {
    Jdbc{
       url= "**********************************",
       driver= "com.mysql.cj.jdbc.Driver",
       user= "root",
       password= "123456$",
       plugin_name= "Jdbc",
       database= "dbtest",
       table= "target_user_v1",
       support_upsert_by_query_primary_key_exist= true,
       generate_sink_sql= true,
       primary_keys= [
        "id"
      ],
       max_retries= 3,
       batch_size= 300
    }
 }
 