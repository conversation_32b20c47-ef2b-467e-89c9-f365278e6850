env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="******************************************************************************************************************************************************************************************"
    driver="com.mysql.jdbc.Driver"
    schema="qjq"
    user="root"
    password="Cdyanfa_123456"
    query="select `id`,`username`,`email`,`created_at` from `qjq`.`user` Where 1=1  AND id=3"
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000008_source_1"
    "parallelism"=1
    "table_path"=1
  }


}
transform {
  FieldMapper{
    source_table_name="E000008_source_1"
    result_table_name="E000008_source_1_trans_1"
    field_mapper={
      "id"="id"
      "username"="username"
      "email"="email"
      "created_at"="createdAt"
    }
  }
}
sink {

  HTTP {
    source_table_name="E000008_source_1_trans_1"
    url="http://192.168.100.58:18019/user"
    method="PUT"
    params= {
    }
    headers = {
      product="11053212232192"
      projectid="12913749529472"
      token="35ca2e35132e480d83a863fd5b647ac6"
    }
    sink_output_columns = [
      {
        srcColumnName="id"
        srcColumnType="INT"
        destFieldName="id"
        destFieldType="number"
        destLength="10"
        defaultValue=""
      }
      {
        srcColumnName="username"
        srcColumnType="VARCHAR"
        destFieldName="username"
        destFieldType="String"
        destLength="50"
        defaultValue=""
      }
      {
        srcColumnName="email"
        srcColumnType="VARCHAR"
        destFieldName="email"
        destFieldType="String"
        destLength="100"
        defaultValue=""
      }
      {
        srcColumnName="created_at"
        srcColumnType="TIMESTAMP"
        destFieldName="createdAt"
        destFieldType="String"
        destLength="19"
        defaultValue=""
      }
    ]
    body_send_type="raw"
    connect_timeout_ms = "15"
    datetimeFormat = "yyyy-MM-dd HH:mm:ss"
    dateFormat ="yyyy-MM-dd"
    timeFormat ="HH:mm:ss"
  }
}
