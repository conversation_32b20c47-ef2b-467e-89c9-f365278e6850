env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
        query="select `id`,`user_info_xml`,`other_info_xml` from `test`.`source_xml_user` Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "table_path"=1
        }




}
transform {
        XmlPathV2{
            source_table_name="E000001_source_1"
            result_table_name="E000001_source_1_trans_1"
                        mapping_keys= [
                           {
                                srcField="user_info_xml"
                                xpath="/user/name"
                                destField="name"
                                destType="string"
                           }
                           {
                                srcField="user_info_xml"
                                xpath="/user/age"
                                destField="age"
                                destType="int"
                           }
                           {
                                srcField="user_info_xml"
                                xpath="/user/addr"
                                destField="addr"
                                destType="string"
                           }
                           {
                                srcField="other_info_xml"
                                xpath="/conf/teacher/teacherName"
                                 destField="teacherName"
                                destType="string"
                           }
                           {
                                srcField="other_info_xml"
                                xpath="/conf/grades/grade/studentName"
                                destField="studentName"
                                destType="string"
                           }
                        ]
                    output_fields= {
                        id="BIGINT"
                    }
        }
}
sink {
        Jdbc {
        url="******************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
        database="test"
        table="target_user_v2"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="E000001_source_1_trans_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
            "partition_keys"=[]
        }

}