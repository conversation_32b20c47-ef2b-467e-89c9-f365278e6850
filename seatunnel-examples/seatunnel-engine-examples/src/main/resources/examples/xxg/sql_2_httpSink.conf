env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Jdbc {
    url="***********************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    schema="test_new"
    user="root"
    password="joyadata"
    query="SELECT   'DataXone实时同步队列运行异常' AS eventName,   '' AS eventCode,   '业务事件' AS eventCategory,   id AS businessId,   alarm_header AS content,   tags AS tags,   alarm_type AS alarmType,   alarm_header AS alarmHeader,   alarm_content AS alarmContent,   alarm_time AS alarmTime ,   1 AS tenantCode FROM   alarm_info"
    "fetch_size"="1000"
    "split.size"="1000"
    "connection_check_timeout_sec"="30"
    "result_table_name"="E000002_source_1"
    "parallelism"=1
    "table_path"=1
  }





}
transform {
  SQL{
    source_table_name="E000002_source_1"
    result_table_name="E000002_source_1_trans_1"
    query="select eventName, eventCode, eventCategory, businessId, content, tags, alarmType, alarmHeader, alarmContent, alarmTime, tenantCode from E000002_source_1 where 1=1"
  }
  FieldMapper{
    source_table_name="E000002_source_1_trans_1"
    result_table_name="E000002_source_1_trans_1_trans_2"
    field_mapper={
      "eventName"="eventName"
      "eventCode"="eventCode"
      "eventCategory"="eventCategory"
      "businessId"="businessId"
      "content"="content"
      "tags"="data.tags"
      "alarmType"="data.alarmType"
      "alarmHeader"="data.alarmHeader"
      "alarmContent"="data.alarmContent"
      "alarmTime"="data.alarmTime"
      "tenantCode"="tenantCode"
    }
  }
}
sink {

  HTTP {
    source_table_name="E000002_source_1_trans_1_trans_2"
    url="http://192.168.100.58:8858/dedp/v1/csc/event_record"
    method="POST"
    params= {
    }
    headers = {
      token="a08d185dba0543de9cb6ba2ad108ca90"
    }
    sink_output_columns = [
      {
        srcColumnName="eventName"
        destFieldName="eventName"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="eventCode"
        destFieldName="eventCode"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="eventCategory"
        destFieldName="eventCategory"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="businessId"
        destFieldName="businessId"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="content"
        destFieldName="content"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="tags"
        destFieldName="data.tags"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="alarmType"
        destFieldName="data.alarmType"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="alarmHeader"
        destFieldName="data.alarmHeader"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="alarmContent"
        destFieldName="data.alarmContent"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
      {
        srcColumnName="alarmTime"
        destFieldName="data.alarmTime"
        destFieldType="String"
        destLength="19"
        defaultValue=""
      }
      {
        srcColumnName="tenantCode"
        destFieldName="tenantCode"
        destFieldType="String"
        destLength="255"
        defaultValue=""
      }
    ]
    body_send_type="raw"
    connect_timeout_ms = "15"
    datetimeFormat = "yyyy-MM-dd HH:mm:ss"
    dateFormat ="yyyy-MM-dd"
    timeFormat ="HH:mm:ss"
  }
}


