env {
  execution.parallelism = 1
  #job.mode = "STREAMING"  # HTTP 作为数据源，支持批量和流式模式
  job.mode = "BATCH"
  checkpoint.interval = 10000  # 执行间隔（毫秒）
}

source {
  Http {
    url = "http://localhost:18189/dedp/v1/di/api/testGetBody"
    method = "GET"
    format = "json"
    headers = {
      Authorization="Bearer example-token",
      language="zh",
      token="5362014d353f4092b7b3746e81ca2834"
    }
    params = {
      token="5362014d353f4092b7b3746e81ca2834"
      userId="fa438165b2c84d8dbe9175d152718437"
      id="8888"
    }
    body = "{\"name\":\"中交第一公路勘察设计研究院有限公司\",\"startTime\":\"20240101\",\"id\":\"123\",\"endTime\":\"20240731\",\"pageNum\":\"1\",\"pageSize\":20,\"tags\":\"产品质量,安全事件\"}"
    schema = {
      fields = {
        website = string
        abstracts = string
        docid = string
        rtm = bigint
        title = string
        uri = string
        tags = string
      }
    }
    json_field = {
      "website": "$.result.result.items[*].website",
      "abstracts": "$.result.result.items[*].abstracts",
      "docid": "$.result.result.items[*].docid"
      "rtm": "$.result.result.items[*].rtm"
      "title": "$.result.result.items[*].title"
      "uri": "$.result.result.items[*].uri"
      "tags": "$.result.result.items[*].tags"
    }

    body_send_type = "raw"
    result_table_name = "user_info"
    connect_timeout_ms = "12"
    timeFormat="HH:mm:ss",
    dateFormat="yyyy/MM/dd",
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
  }
}


sink {
  Console {
    source_table_name = "user_info"
  }

}