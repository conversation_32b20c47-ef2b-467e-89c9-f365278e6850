env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {




  HTTP {
    result_table_name="E000008_source_1"
    url="http://192.168.100.58:8000/api/database/index/info/datasourceReference"
    method="POST"
    format="json"
    params= {
      pageSize="10"
      pageNum="1"
    }
    headers = {
      token="7745917fb03b4de186e1b65d0c5e1896"
    }
    body="{\"indexOrderType\":1}"
    schema = {
      fields = {
        datasourceInfoId="STRING"
        createTime="TIMESTAMP"
        dataType="STRING"
        newAuthorizationTime="TIMESTAMP"
        businessName="STRING"
        dataName="STRING"
        id="BIGINT"
      }
    }
    json_field = {
      datasourceInfoId="$.data[*].datasourceInfoId"
      createTime="$.data[*].createTime"
      dataType="$.data[*].dataType"
      newAuthorizationTime="$.data[*].newAuthorizationTime"
      businessName="$.data[*].businessName"
      dataName="$.data[*].dataName"
      id="$.data[*].id"
    }
    pageing = {
      page_field="pageNum"
      batch_size="10"
      total_page_size="-1"
      start_page_number="1"
    }
    body_send_type="raw"
    connect_timeout_ms = "15"
    datetimeFormat = "yyyy-MM-dd HH:mm:ss"
    dateFormat ="yyyy-MM-dd"
    timeFormat ="HH:mm:ss"
    connect_timeout_ms=120
  }

}
transform {
  SQL{
    source_table_name="E000008_source_1"
    result_table_name="E000008_source_1_trans_2"
    query="select id, datasourceInfoId, dataType, dataName, createTime, businessName, newAuthorizationTime from E000008_source_1 where 1=1"
  }
}
sink {
  Jdbc {
    url="**************************************************************************************************************************************************************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password="Cdyanfa_123456"
    database="test"
    table="test_http_source_2_mysql_post"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "transaction_timeout_sec"="-1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="E000008_source_1_trans_2"
    "generate_sink_sql"="true"
    "enable_upsert"="false"
    "pk_strategy"="stop"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
    "partition_keys"=[]
  }

}
