env {
  "job.mode"="BATCH"
  "job.name"="10"
}
source {
  Doris {
    fenodes="**************:8030"
    username="root"
    password="Cdyanfa_123456"
    database="test"
    table="employee"
    query-port="9030"
    result_table_name="doris_source_1"
    doris.filter.query=" 1 = 1 "
    doris.batch.size="1024"
    doris.request.read.timeout.ms="30000"
    doris.request.connect.timeout.ms=30000
    "parallelism"=1
  }
}
transform {
}
sink {
  Elasticsearch {
    source_table_name="doris_source_1"
    hosts = ["***************:9200"]
    index = "my_index_0528"
    index_type = "_doc"
    username = "dedp"
    password="Cdyanfa_123456"
    tls_verify_certificate = "true"
    tls_verify_hostname = "true"
    schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
    data_save_mode = "APPEND_DATA"
  }

}
