env {
  execution.parallelism = 1
  #job.mode = "STREAMING"  # HTTP 作为数据源，支持批量和流式模式
  job.mode = "BATCH"
  checkpoint.interval = 10000  # 执行间隔（毫秒）
}

source {
  Http {
    url = "http://localhost:18189/dedp/v1/di/api/testPostBodyByPageNum"
    method = "POST"
    format = "json"
    headers = {
          Authorization="Bearer example-token",
          language="zh",
          token="7745917fb03b4de186e1b65d0c5e1896"
    }
    params = {
          token="7745917fb03b4de186e1b65d0c5e1896"
          userId="fa438165b2c84d8dbe9175d152718437"
          id="8888"
    }
    body = "{\"name\":\"中交第一公路勘察设计研究院有限公司\",\"startTime\":\"20240101\",\"id\":\"123\",\"endTime\":\"20240731\",\"pageNum\":\"1\",\"pageSize\":20,\"tags\":\"产品质量,安全事件\"}"
    schema = {
          fields = {
            website = string
            abstracts = string
            docid = string
            rtm = bigint
            title = string
            uri = string
            tags = string
          }
     }
    content_field = "$.result.items[*]"
    json_field = {
        "website": "$.website",
        "abstracts": "$.abstracts",
        "docid": "$.docid"
        "rtm": "$.rtm"
        "title": "$.title"
        "uri": "$.uri"
        "tags": "$.tags"
    }
    pageing = {
        page_field =  "pageNum",
        total_page_size="-1"  # 总的分页数
        start_page_number="1" # 分页索引
        batch_size = 20    #每页多少条
    }
    body_send_type = "form-data"
    result_table_name = "user_info"
    connect_timeout_ms = "15"
    timeFormat="HH:mm:ss",
    dateFormat="yyyy/MM/dd",
    datetimeFormat="yyyy-MM-dd HH:mm:ss"
  }
}

transform {
    Sql {
      source_table_name = "user_info"
      result_table_name = "user_info_out"
      query = "SELECT  rtm as id,  website , abstracts, docid, rtm, title,uri,  tags FROM user_info"
    }
}

sink {
    Console {
       source_table_name = "user_info_out"
     }
     Jdbc{
            source_table_name = "user_info_out"
           url= "**********************************",
           driver= "com.mysql.cj.jdbc.Driver",
           user= "root",
           password= "123456$",
           plugin_name= "Jdbc",
           database= "dbtest",
           table= "test_xinwenyuqing",
           support_upsert_by_query_primary_key_exist= true,
           generate_sink_sql= true,
           primary_keys= [
            "id"
          ],
           max_retries= 3,
           batch_size= 300
        }
}