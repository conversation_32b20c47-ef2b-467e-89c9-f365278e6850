env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="**********************************************"
        driver="org.postgresql.Driver"
        user="postgres"
        password="Cdyanfa_123456"
        database="postgres"
        query="select * from public.emp Where 1=1 "
        "fetch_size"="1000"
        "split.size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
        }




}
transform {
}
sink {
        Jdbc {
       url="**********************************************"
               driver="org.postgresql.Driver"
               user="postgres"
               password="Cdyanfa_123456"
        database="public"
        table="emp2"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "transaction_timeout_sec"="-1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="E000001_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="true"
            "pk_strategy"="stop"
            primary_keys="emp_id"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}
