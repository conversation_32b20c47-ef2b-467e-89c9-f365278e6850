 env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select ID,                                POLICY_NO,                                POLICY_NO_END,                                TOTAL_ACTUAL_PREMIUM,                                TOTAL_INSURED_AMOUNT,                                CASUALTY_AMOUNT,                                THE_TIR_PERSON_CSLTY_AMOUNT,                                PROPERTY_LOSS_AMOUNT,                                EMERGENCY_RESCUE_AMOUNT,                                MEDICAL_AID_AMOUNT,                                ACCIDENT_SURVEY_AMOUNT,                                LAWSUIT_AMOUNT,                                TOTAL_INDEMNITY_AMOUNT,                                PER_ACCIDENT_AMOUNT,                                PER_ACCIDENT_RESCUE_AMOUNT,                                PER_ACCIDENT_IDENTIF_AMOUNT,                                PER_ACCIDENT_LEGAL_AMOUNT,                                TOTAL_ACCIDENT_RESCUE_AMOUNT,                                PER_INVESTIGATION_AMOUNT,                                PER_PERSON_AMOUNT,                                INSURANCE_BEGIN_DATE,                                INSURANCE_END_DATE,                                COMPANY_ID,                                ID_CODE,                                COMPANY_NAME,                                COMPANY_CITY_CODE,                                COMPANY_DISTRICT_CODE,                                COMPANY_INDUSTRY,                                CONTACT_ADDRESS,                                INSURANT_NAME,                                INSURANT_ID_CODE,                                ORG_LICENSE_FILE,                                ORG_LICENSE_FILE_URL,                                INSURANT_ADDRESS,                                INSURANT_EMAIL,                                INSURANT_LEADER,                                INSURANT_PHONE,                                INSURANT_INDUSTRY_FIELD,                                INSURANT_ENTERPRISE_SCALE,                                INSURANT_STANDARD,                                INSURANT_ORG_LICENSE_NO,                                INSURANT_ORG_LICENSE_BGN_DATE,                                INSURANT_ORG_LICENSE_END_DATE,                                RESOLUTION,                                CITY_CODE,                                DISTRICT_CODE,                                to_char(sysdate-1,'yyyy-mm-dd') as DATA_DATE,                                CREATE_DATE,                                UPDATE_DATE                                from WEB_POLICY_INFO_05"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }





}
transform {
}
sink {
        Jdbc {
        url="*********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Hello_dc_user"
        database="ABYDKF20"
        table="DC_USER.DC_POLICY_INFO_05"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "primary_keys" = ["ID"]
            "field_ide"=UPPERCASE
            "enable_upsert"="true"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }









}