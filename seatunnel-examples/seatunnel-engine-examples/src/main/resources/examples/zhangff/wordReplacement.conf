env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select * from test_student"
        }

}
transform {
   Dah{
        "dah_identifier"="wordReplacement"
        "dah_column"="sex"
        "dah_algorithmRegex"="男|女"
        "dah_algorithmParameter"="*"
  }

}
sink {
        Jdbc {
            url="*****************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
            database="test"
            table="test_student"
            "connection_check_timeout_sec"="30"
            "batch_size"="1000"
            "is_exactly_once"="false"
            "max_commit_attempts"="3"
            "transaction_timeout_sec"="-1"
            "max_retries"="0"
            "auto_commit"="true"
            "support_upsert_by_query_primary_key_exist"="false"
            "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="stop"
            "insert_error_strategy"="continue"
        }


}