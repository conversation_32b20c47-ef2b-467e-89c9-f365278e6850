env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="jdbc:oracle:thin:@//************:1521/DJDB"
        driver="oracle.jdbc.driver.OracleDriver"
        user="business_nx"
        password="ojQu0$f91_rN"
        query="SELECT PROPOSALNO,        CLASSCODE,        RISKCODE,        CONTRACTNO,        POLICYSORT,        PRINTNO,        BUSINESSNATURE,        LANGUAGE,        POLICYTYPE,        APPLICODE,        APPLINAME,        APPLIADDRESS,        INSUREDCODE,        INSUREDNAME,        INSUREDADDRESS,        OPERATEDATE,        STARTDATE,        STARTHOUR,        ENDDATE,        ENDHOUR,        PURERATE,        DISRATE,        DISCOUNT,        CURRENCY,        SUMVALUE,        SUMAMOUNT,        SUMDISCOUNT,        SUMPREMIUM,        <PERSON><PERSON><PERSON>BPR<PERSON>,        S<PERSON>Q<PERSON><PERSON>TITY,        J<PERSON><PERSON><PERSON><PERSON>OD<PERSON>,        <PERSON><PERSON>DICA<PERSON>COP<PERSON>,        AUTOTRANSRENEWFLAG,        ARGUESOLUTION,        ARBITBOARDNAME,        PAYTIMES,        ENDORSETIMES,        CLAIMTIMES,        MAKECOM,        OPERATESITE,        COMCODE,        HANDLERCODE,        HANDLER1CODE,        APPROVERCODE,        UNDERWRITECODE,        UNDERWRITENAME,        OPERATORCODE,        INPUTDATE,        INPUTHOUR,        UNDERWRITEENDDATE,        STATISTICSYM,        AGENTCODE,        COINSFLAG,        REINSFLAG,        ALLINSFLAG,        UNDERWRITEFLAG,        OTHFLAG,        FLAG,        DISRATE1,        BUSINESSFLAG,        UPDATERCODE,        UPDATEDATE,        UPDATEHOUR,        SIGNDATE,        SHAREHOLDERFLAG,        AGREEMENTNO,        INQUIRYNO,        PAYMODE,        REMARK,        POLICYNO,        VISACODE,        MANUALTYPE,        POLICYBIZTYPE,        BUSINESSTYPE,        BUSINESSTYPE1,        UNITCODE,        STATQUANTITY,        STATUNITCODE,        SUMINSURED,        ARTICLETYPE,        BUSINESSPROVINCE,        BUSINESSTOWN,        BUSINESSCOUNTY,        BUSINESSAREANAME,        STARTMINUTE,        ENDMINUTE,        LIMITAMOUNT,        THIRDKNOW,        AGENTREMARK,        NCARPERPFLAG,        RICHFLYAREASCODE,        RICHFLYAREASCNAME,        RICHFLYCODE,        RICHFLYCNAME,        GROUPNO,        GROUPFLAG,        BASEPERFORMANCERATE,        ENCOURAGEPERFORMANCERATE,        ISSEEFEEFLAG,        VALIDCOUNTDATE,        SUMRATE,        STANDARDRATE,        AGRIFLAG,        VERSIONNO,        BIGMEDICALTYPE,        EARNINGSRATE,        INSUREDLISTTYPE,        CHANNELCODE,        CHANNELNAME,        MANAGEMODEL,        SALERCODE,        INSTCODE,        INSUREINFORMOPERATETYPE,        INFORMNUMBER,        HANDINGFEERATE,        HANDINGFEEAMT,        CENTRALUNITFLAG,        CENTRALUNITCODE,        RECCHILDCHANNEL,        AUTHORIZATIONFLAG,        AUTHORIZATIONDATE,        PROPOSALNOAES,        RISKEVALUATEFLAG,        ISNEWMODE,        DATASRC,        DATAFLAG,        ORDERNO,        COINSHANDLINGFEEFLAG,        COINSPREMIUMFLAG,        '0'  FROM PRPTMAIN MAI  WHERE MAI.SIGNDATE >= (SYSDATE - 1)    AND MAI.UNDERWRITEFLAG IN ('1', '3')    AND COMCODE LIKE '22%'"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }

}
transform {
}
sink {
        Jdbc {
        url="******************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Dc_user#123"
        database="DJDB"
        table="DC_USER.PRPTMAIN"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "primary_keys" = ["PROPOSALNO"]
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}