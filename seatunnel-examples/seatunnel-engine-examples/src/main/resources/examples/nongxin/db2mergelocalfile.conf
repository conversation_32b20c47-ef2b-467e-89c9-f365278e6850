env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="************************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="db2inst1"
        password="JOYADATA"
        query="select * from JOYADATA.EMP"
    }
}
transform {
}
sink {

  MergeLocalFile {
          final_name="d://tmp//opt//zsp//file//test3.txt"
          path="d://tmp//opt//zsp//file"
          source_table_name = "E000001_source_1"
          tmp_path="d://tmp//opt//zsp//file//tmp"
          file_format_type="text"
          field_delimiter=","
          row_delimiter="\n"
          batch_size="1000"
          compress_codec="none"
          "encoding"="GBK"
      }
}