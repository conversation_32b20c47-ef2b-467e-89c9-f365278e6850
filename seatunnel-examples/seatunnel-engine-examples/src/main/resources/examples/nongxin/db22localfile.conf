env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
    Jdbc {
        url="************************************"
        driver="com.ibm.db2.jcc.DB2Driver"
        user="db2inst1"
        password="JOYADATA"
        query="select * from JOYADATA.EMP"
    }
}
transform {
}
sink {
    LocalFile {
    path = "d://tmp//hive//warehouse//test1"
    field_delimiter = "\t"
    row_delimiter = "\n"
    file_format_type = "text"
    "encoding"="GBK"
  }
}