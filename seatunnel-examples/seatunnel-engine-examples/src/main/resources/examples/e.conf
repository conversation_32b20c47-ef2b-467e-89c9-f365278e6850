env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select   base.c_app_no as id,
  base.c_app_no as app_number ,---申请单号
       applicant.c_app_nme as name, ---客户名称
 case applicant.c_clnt_mrk
         when '1' then
          '1'
         else
          '2' end as org_type, ----申请人类型
 case applicant.c_certf_cls
         when '01' then
          '1'
         when '02' then
          '2'
         when '03' then
          '255'
         when '04' then
          '4'
         when '10' then
          '2'
         when '11' then
          '1'
         when '12' then
          '255'
         when '204' then
          '255'
         when '99' then
          '255'
       end as card_type, ---证件类型
 case applicant.c_certf_cls
         when '01' then
          '1'
         when '02' then
          '2'
         when '03' then
          '255'
         when '04' then
          '4'
         when '10' then
          '2'
         when '11' then
          '1'
         when '12' then
          '255'
         when '204' then
          '255'
         when '99' then
          '255'
       end as org_card_type, ---证件类型
 case applicant.c_work_dpt
         when '1' then
          '1'
         when '10' then
          '1'
         when '11' then
          '1'
         when '12' then
          '255'
         when '2' then
          '2'
         when '3' then
          '255'
         when '5' then
          '3'
         when '6' then
          '3'
         when '9' then
          '255'
       end as company_type, ---单位性质
       applicant.c_certf_cde as id_card, ---证件号码
       applicant.c_mobile as phone,
       applicant.c_tel as tel_number,
       applicant.c_work_area as address,
       TO_CHAR(applicant.t_birthday,'YYYY-MM-DD') as csrq,
case applicant.c_sex
                when '106001' then
          '1'
         when '106002' then
          '2' end   as xb     ,
 case applicant.c_clnt_mrk
         when '1' then
          '其他'
         else
          applicant.c_app_nme
       end as company_user, ----法人/负责人
sysdate as create_date
  from web_ply_base      base,
       web_ply_applicant applicant
 where base.c_app_no = applicant.c_app_no
   and base.c_prod_no in ('0498', '04A1')
   and base.c_dpt_cde like '29%'
   and base.t_crt_tm>trunc(sysdate-1)"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }
}
transform {
}
sink {
        Jdbc {
        url="******************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Dc_user#123"
        database="DJDB"
        table="DC_USER.DC_PLY_APPLICANT_02"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}