env {
"job.mode"="BATCH"
"job.name"="1"
"execution.parallelism"="1"
}
source {
        Jdbc {
        url="***************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password=joyadata
        query="select * from seatunnel_source.emp_104_1 "
        "batch_size"="1000"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="db2_test_source_1"
            "parallelism"=1
        }




}
transform {
}
sink {
Doris {
        fenodes="192.168.90.221:8030"
        query-port="9030"
        username="root"
        password=""
        database="test"
        table="emp"
        table.identifier="test.emp"
        sink.label-prefix="702102be-c1d0-465c-8fb9-94defe1452ac"
        sink.enable-2pc="true"
        sink.check-interval="10000"
        sink.max-retries="3"
        sink.buffer-size="262144"
        sink.buffer-count="3"
        doris.batch.size=1024
        needs_unsupported_type_casting="false"
        data_save_mode="APPEND_DATA"
        field_ide=LOWERCASE
        doris.config={
            format=json
            read_json_by_line=true
            }
        }
}