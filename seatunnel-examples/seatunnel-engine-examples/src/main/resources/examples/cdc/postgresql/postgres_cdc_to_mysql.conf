env {
  # You can set engine configuration here
  execution.parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
  #execution.checkpoint.data-uri = "hdfs://localhost:9000/checkpoint"
}
source {
  Postgres-CDC  {
    base-url = "**********************************************"
    username = "postgres"
    password = "joyadata"
    database-names = ["postgres"]
    schema-names = ["postgres"]
    table-names = ["postgres.postgres.emp_quality_20240305"]
    result_table_name = "customers"
    incremental.parallelism=1
   }
}

transform {
}

sink {
 JDBC {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_104_61"
        "batch_size"="1000"
        "generate_sink_sql"="true"
	     enable_upsert = true
     	 generate_sink_sql = true
     	 primary_keys = ["emp_id"]
         query = ""
  }
}