env {
  # You can set engine configuration here
  execution.parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
  read_limit.bytes_per_second=7000000
  read_limit.rows_per_second=400
}
source {
  MySQL-CDC {
    result_table_name = "customers_mysql_cdc"
    server-id = 232
    username = "root"
    password = "joyadata"
    table-names = ["seatunnel_source.mysql_cdc_e2e_source_table"]
    base-url = "******************************************************************************************************************************************************************************"
    startup.mode="LATEST"
    catalog {
      factory = MySQL
    }
  }
}
transform {
}
sink {
  jdbc {
    source_table_name = "customers_mysql_cdc"
    url = "****************************************************************************************************************************************************************************"
    driver = "com.mysql.cj.jdbc.Driver"
    user = "root"
    password = "joyadata"
    generate_sink_sql = true
    database = seatunnel_sink
    table = "seatunnel_sink.mysql_cdc_e2e_sink_table"
    "support_upsert_by_query_primary_key_exist"="true"
    "enable_upsert"="true"
    primary_keys = ["emp_id"]
  }
}