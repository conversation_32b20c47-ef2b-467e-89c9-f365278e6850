env {
"job.mode"="STREAMING"
"job.name"="10"
}
source {
	SqlServer-CDC {
		result_table_name = "SQLServer_cdc_personnel_information_1w"
		username = "sa"
		password = "<PERSON><PERSON>an<PERSON>@123456"
		database-names = ["qjq"]
		table-names = ["qjq.test.personnel_information_1w"]
		base-url = "**************************************************************** Set=UTF-8;"
		result_table_name="emp_source_1"
}



}
transform {
    FieldMapper{
            source_table_name="emp_source_1"
            result_table_name="emp_source_1_trans_2"
                    field_mapper={
                       id=id,
                       name=name,
                       gender=name1
                    }
        }
}
sink {
    Doris {
        fenodes="192.168.90.221:8030"
        query-port="9030"
        username="root"
        password=""
        database="test"
        table="personnel_information_1w_cdc_20240821"
        table.identifier="test.personnel_information_1w_cdc_20240821"
        source_table_name="emp_source_1_trans_2"
        sink.label-prefix="702102be-c1d0-465c-8fb9-94defe1452ac"
        sink.enable-2pc="true"
        sink.check-interval="10000"
        sink.max-retries="3"
        sink.buffer-size="262144"
        sink.buffer-count="3"
        doris.batch.size=1024
        needs_unsupported_type_casting="false"
        data_save_mode="APPEND_DATA"
        field_ide=LOWERCASE
        doris.config={
            format=json
            read_json_by_line=true
            }
        }
}
