env {
"job.mode"="STREAMING"
"job.name"="10"
}
source {
	SqlServer-CDC {
		result_table_name = "SQLServer_cdc_personnel_information_1w"
		username = "sa"
		password = "<PERSON><PERSON>an<PERSON>@123456"
		database-names = ["lihj"]
		table-names = ["lihj.dbo.emp_2040810"]
		base-url = "***************************************************************** Set=UTF-8;"
		startup.mode="INITIAL"

}



}
transform {
}
sink {
 Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_20240809_cdc"
        "batch_size"="1000"
        "generate_sink_sql"="true"
	     enable_upsert = true
	     "support_upsert_by_query_primary_key_exist"="true"
         "primary_keys"=[emp_id]
     	 generate_sink_sql = true
         query = ""
  }

}
