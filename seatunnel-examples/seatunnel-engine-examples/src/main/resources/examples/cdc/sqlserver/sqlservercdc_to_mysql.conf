env {
  # You can set engine configuration here
  execution.parallelism = 2
  job.mode = "STREAMING"
  checkpoint.interval=2000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  SqlServer-CDC {
    result_table_name = "customers"
    username = "SA"
    password = "Sa@12345678"
    database-names = ["lihj"]
    table-names = ["lihj.dbo.emp_20240305"]
    base-url = "******************************************************"
  }
}

transform {
}
sink {
 Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_104_62"
        "batch_size"="1000"
        "generate_sink_sql"="true"
	     enable_upsert = true
	     "support_upsert_by_query_primary_key_exist"="true"
         "primary_keys"=[emp_id]
     	 generate_sink_sql = true
         query = ""
  }
}