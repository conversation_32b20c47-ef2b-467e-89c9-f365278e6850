env {
    "job.mode"="STREAMING"
    "job.name"="20"
}
source {

    Oracle-CDC {
        result_table_name = "customers_oracle_cdc"
        username = "joyadata"
        password = joyadata
        database-names = ["HELOWIN"]
        schema-name = ["JOYADATA"]
        table-names = ["HELOWIN.JOYADATA.EMP_20240304"]
        base-url = "***********************************************"
    }



}
transform {
}
sink {
    Jdbc {
        url="************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_104_60"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
        "source_table_name"="customers_oracle_cdc"

        "generate_sink_sql"="true"
            "primary_keys"=["EMP_ID"]
    }


}