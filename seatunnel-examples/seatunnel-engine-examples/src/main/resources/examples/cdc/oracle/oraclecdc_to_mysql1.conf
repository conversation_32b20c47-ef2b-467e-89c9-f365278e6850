env {
  # You can set engine configuration here
  execution.parallelism = 1
  job.mode = "STREAMING"
  execution.checkpoint.interval = 5000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  Oracle-CDC {
    result_table_name = "customers"
    username = "JOYADATA"
    password = "joyadata"
    database-names = ["ORCLPDB1"]
    schema-name=["JOYADATA"]
    table-names = ["ORCLPDB1.JOYADATA.EMP_20240304"]
    base-url = "************************************************"
    source.reader.close.timeout = 120000
    startup.mode=initial
    debezium {
        log.mining.strategy = "online_catalog"
        log.mining.continuous.mine = "true"
        database.oracle.jdbc.timezoneAsRegion = "false"
    }
  }
}

transform {
}

sink {
    Jdbc {
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        table="emp_104_60"
        "batch_size"="1000"
        "generate_sink_sql"="true"
	     enable_upsert = true
     	 generate_sink_sql = true
         query = ""
    }
}