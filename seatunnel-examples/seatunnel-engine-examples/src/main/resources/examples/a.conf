env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*******************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password="joyadata"
        query="SELECT `id`,`name`,`date_1`,`data_time` FROM `seatunnel_source`.`zsp_test1017` WHERE 1=1 "
        "fetch_size"="1000"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }



}
transform {
}
sink {
            S3File {
            fs.s3a.endpoint="http://192.168.90.221:9000"
                fs.s3a.aws.credentials.provider="org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        path="/tmp/seatunnel/seatunnel"
        tmp_path="/tmp/seatunnel"
        bucket="s3a://joyadata"
        access_key="minio"
        secret_key="Cdyanfa_123456"
            source_table_name="E000001_source_1"
            custom_filename=true
            file_name_expression="08111_${transactionId}"
            is_enable_transaction=false
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            file_format_type="text"
            field_delimiter="\u0001"
            row_delimiter="\n"
            have_partition="false"
            sink_columns=["id","name","date_1","data_time"]
            batch_size="1000000"
            compress_codec="none"
            validates="false"
            "empty_data_strategy"=false
        clean_target_folder="false"
        }

}
