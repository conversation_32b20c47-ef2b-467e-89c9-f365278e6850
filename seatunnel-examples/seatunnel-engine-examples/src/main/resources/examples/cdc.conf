env {
"job.mode"="STREAMING"
"job.name"="10"
}
source {


        MySQL-CDC {
            result_table_name = "MySQL_cdc_test_code1"
            username = "root"
            password = Cdyanfa_123456
            table-names = ["test.test_code1"]
            base-url = "*************************************"
            server-time-zone = "GMT+08"
        }
}
transform {
}
sink {
        Jdbc {
        url="*************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="Cdyanfa_123456"
        database="test"
        table="test_code10429"
        "connection_check_timeout_sec"="30"

        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"

        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="MySQL_cdc_test_code1"
        "generate_sink_sql"="true"
            "field_ide"=LOWERCASE
            "enable_upsert"="false"
            "pk_strategy"="stop"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}