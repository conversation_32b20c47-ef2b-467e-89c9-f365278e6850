env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
Jdbc {
        url="*****************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="Cdyanfa_123456"
        query="select `id`from `test`.`users` where 1=1"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "incremental.parallelism"="1"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        }

}
transform {
}
sink {
  Elasticsearch {
    hosts = ["**************:9200"]
    tls_verify_certificate = false
    tls_verify_hostname = false
    index = "mysql2es_test1030_10"
    "schema_save_mode"="CREATE_SCHEMA_WHEN_NOT_EXIST"
    "data_save_mode"="DROP_DATA"
    username = "elastic"
    password = "changeme"
  }
}