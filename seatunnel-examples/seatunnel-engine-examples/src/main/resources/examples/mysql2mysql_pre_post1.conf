env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="******************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id`,`emp_name`,`gender`,`account`,`org_id`,`birth_date`,`age`,`nationality`,`province`,`city`,`email`,`phone`,`begin_date`,`remark`,`create_time`,`update_time` from `seatunnel_source`.`emp_104_1` where emp_id<=20000"
        "batch_size"="1024"
        "partition_column"="emp_id"
        "parallelism"=2
        pre_sql=["CREATE TABLE IF NOT EXISTS `emp_104lihj20240407` (`emp_id` int NOT NULL,`emp_name` varchar(64) DEFAULT NULL,`gender` char(1) DEFAULT NULL,`account` varchar(32) DEFAULT NULL,`org_id` varchar(64) DEFAULT NULL,`birth_date` char(10) DEFAULT NULL,`age` int DEFAULT NULL,`nationality` char(3) DEFAULT NULL,`province` varchar(6) DEFAULT NULL,`city` char(6) DEFAULT NULL,`email` varchar(128) DEFAULT NULL,`phone` varchar(16) DEFAULT NULL,`begin_date` date DEFAULT NULL,`remark` varchar(128) DEFAULT NULL,`create_time` datetime DEFAULT NULL,`update_time` datetime DEFAULT NULL,PRIMARY KEY (`emp_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3","CREATE TABLE IF NOT EXISTS `emp_104lihj20240408` (`emp_id` int NOT NULL,`emp_name` varchar(64) DEFAULT NULL,`gender` char(1) DEFAULT NULL,`account` varchar(32) DEFAULT NULL,`org_id` varchar(64) DEFAULT NULL,`birth_date` char(10) DEFAULT NULL,`age` int DEFAULT NULL,`nationality` char(3) DEFAULT NULL,`province` varchar(6) DEFAULT NULL,`city` char(6) DEFAULT NULL,`email` varchar(128) DEFAULT NULL,`phone` varchar(16) DEFAULT NULL,`begin_date` date DEFAULT NULL,`remark` varchar(128) DEFAULT NULL,`create_time` datetime DEFAULT NULL,`update_time` datetime DEFAULT NULL,PRIMARY KEY (`emp_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3"]
        post_sql=["INSERT INTO `emp_104lihj20240407`(`emp_id`, `emp_name`, `gender`, `account`, `org_id`, `birth_date`, `age`, `nationality`, `province`, `city`, `email`, `phone`, `begin_date`, `remark`, `create_time`, `update_time`) VALUES (2, '终斌', 'F', '029318', '105', '1944-12-08', 75, '142', '3', '37', '<EMAIL>', '***********', '2020-10-25', '0', '2017-01-25 14:59:15', '2020-10-28 09:13:21')","INSERT INTO `emp_104lihj20240408`(`emp_id`, `emp_name`, `gender`, `account`, `org_id`, `birth_date`, `age`, `nationality`, `province`, `city`, `email`, `phone`, `begin_date`, `remark`, `create_time`, `update_time`) VALUES (2, '终斌', 'F', '029318', '105', '1944-12-08', 75, '142', '3', '37', '<EMAIL>', '***********', '2020-10-25', '0', '2017-01-25 14:59:15', '2020-10-28 09:13:21')"]
        }
}
transform {
}
sink {
        Jdbc {
            url="****************************************************************************************************************************************************************************"
            driver="com.mysql.cj.jdbc.Driver"
            user="root"
            password="joyadata"
            database="seatunnel_sink"
            table="emp_104_9"
            "support_upsert_by_query_primary_key_exist"="false"
            "generate_sink_sql"="true"
            "primary_keys"=[emp_id]
            "enable_upsert"="false"
            pre_sql=["CREATE TABLE IF NOT EXISTS `emp_104lihj20240407` (`emp_id` int NOT NULL,`emp_name` varchar(64) DEFAULT NULL,`gender` char(1) DEFAULT NULL,`account` varchar(32) DEFAULT NULL,`org_id` varchar(64) DEFAULT NULL,`birth_date` char(10) DEFAULT NULL,`age` int DEFAULT NULL,`nationality` char(3) DEFAULT NULL,`province` varchar(6) DEFAULT NULL,`city` char(6) DEFAULT NULL,`email` varchar(128) DEFAULT NULL,`phone` varchar(16) DEFAULT NULL,`begin_date` date DEFAULT NULL,`remark` varchar(128) DEFAULT NULL,`create_time` datetime DEFAULT NULL,`update_time` datetime DEFAULT NULL,PRIMARY KEY (`emp_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3","CREATE TABLE IF NOT EXISTS `emp_104lihj20240408` (`emp_id` int NOT NULL,`emp_name` varchar(64) DEFAULT NULL,`gender` char(1) DEFAULT NULL,`account` varchar(32) DEFAULT NULL,`org_id` varchar(64) DEFAULT NULL,`birth_date` char(10) DEFAULT NULL,`age` int DEFAULT NULL,`nationality` char(3) DEFAULT NULL,`province` varchar(6) DEFAULT NULL,`city` char(6) DEFAULT NULL,`email` varchar(128) DEFAULT NULL,`phone` varchar(16) DEFAULT NULL,`begin_date` date DEFAULT NULL,`remark` varchar(128) DEFAULT NULL,`create_time` datetime DEFAULT NULL,`update_time` datetime DEFAULT NULL,PRIMARY KEY (`emp_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3"]
            post_sql=["INSERT INTO `emp_104lihj20240407`(`emp_id`, `emp_name`, `gender`, `account`, `org_id`, `birth_date`, `age`, `nationality`, `province`, `city`, `email`, `phone`, `begin_date`, `remark`, `create_time`, `update_time`) VALUES (2, '终斌', 'F', '029318', '105', '1944-12-08', 75, '142', '3', '37', '<EMAIL>', '***********', '2020-10-25', '0', '2017-01-25 14:59:15', '2020-10-28 09:13:21')","INSERT INTO `emp_104lihj20240408`(`emp_id`, `emp_name`, `gender`, `account`, `org_id`, `birth_date`, `age`, `nationality`, `province`, `city`, `email`, `phone`, `begin_date`, `remark`, `create_time`, `update_time`) VALUES (2, '终斌', 'F', '029318', '105', '1944-12-08', 75, '142', '3', '37', '<EMAIL>', '***********', '2020-10-25', '0', '2017-01-25 14:59:15', '2020-10-28 09:13:21')"]
        }


}
