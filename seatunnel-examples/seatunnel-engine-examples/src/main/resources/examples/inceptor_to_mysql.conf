env {
    "job.mode"="BATCH"
    "job.name"="10"
}
source {
    Jdbc {
        url = "***************************************"
        driver = "org.apache.hive.jdbc.HiveDriver"
        connection_check_timeout_sec = 100
        user="hive"
        password="123456"
        query = "select * from test1"
    }
}
transform {
}
sink {
    Jdbc {
        "source_table_name"="table02"
        url="****************************************************************************************************************************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        database="seatunnel_sink"
        primary_keys = [id]
        table="test1"
        "batch_size"="1000"
	     enable_upsert = true
     	 generate_sink_sql = true
         query = ""
    }
}
