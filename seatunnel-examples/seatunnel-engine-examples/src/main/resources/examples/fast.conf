env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="SELECT C_BANKCODE,C_BANKNAME,C_BANKKIND,C_BANKZONE,C_CRT_CDE,T_CRT_TM,C_UPD_CDE,T_UPD_TM,T_ADB_TM,C_IS_VALID,C_BANK_PROVINCE,C_TRANS_MRK,T_TRANS_TM FROM WEB_BAS_FIN_BANKNET"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="WEB_BAS_FIN_BANKNET_source_1"
         "partition_column"="C_BANKCODE"
        "parallelism"=5
        }

}
transform {
}
sink {
        Jdbc {
        url="**************************************************************"
        driver="com.mysql.cj.jdbc.Driver"
            user="Djmysql_dat"
            password="Mysql@#2021"
        database="gis"
        table="utibanknet"
        "connection_check_timeout_sec"="30"
        "batch_size"="10000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="WEB_BAS_FIN_BANKNET_source_1"
        "generate_sink_sql"="true"
            "enable_upsert"="false"
            "pk_strategy"="continue"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }

}