env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select   C_PK_ID as ID,   C_APP_NO,   C_PLY_NO,   C_PROD_NO,   C_DPT_CDE,   C_APP_TYPE,   C_REQUEST,   C_SEND_STATUS,   to_char(sysdate -1,'yyyy-mm-dd'),   C_CRT_CDE,   T_CRT_TM,   C_UPD_CDE,   T_UPD_TM,   '',   null from   WEB_SAFE_PROD_LIAB_EMP_TASK  a   WHERE 	t_crt_tm >= trunc(sysdate) 	OR T_UPD_TM >= trunc(sysdate) "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }





}
transform {
}
sink {
        Jdbc {
        url="******************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Dc_user#123"
        database="DJDB"
        table="DC_USER.WEB_SAFE_PROD_LIAB_EMP_TASK"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "field_ide"=UPPERCASE
            "enable_upsert"="false"
        "schema_save_mode" = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }


}
