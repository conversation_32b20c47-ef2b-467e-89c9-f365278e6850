env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="**********************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="select id,        holder_name,        holder_bl_code,        is_policy_holder,        issuing_company_name,        issuing_company_code,        is_coinsurance,        application_form_code,        policy_no,        start_time,        end_time,        actual_prem,        limit_value,        policy_file_url_list,        sign_time,        prem,        rate_factor,        prem_type_remark,        insure_num_remark,        accident_rate,        accident_type,        frequency,        business_org_id,        business_org_name,        business_org_type,        project_type,        to_char(sysdate-1,'yyyy-mm-dd') as data_date,        create_date,        update_date from  WEB_ply_base_04 "
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }





}
transform {
}
sink {
        Jdbc {
        url="******************************************"
        driver="oracle.jdbc.driver.OracleDriver"
            user="dc_user"
            password="Dc_user#123"
        database="DJDB"
        table="DC_USER.DC_PLY_BASE_04"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
            "source_table_name"="null_source_1"
        "generate_sink_sql"="true"
            "primary_keys" = ["ID"]
            "field_ide"=UPPERCASE
            "enable_upsert"="true"
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
        }