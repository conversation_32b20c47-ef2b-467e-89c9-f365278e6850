env {
"job.mode"="BATCH"
"job.name"="20"
}
source {
        Jdbc {
        url="**************************************************"
        driver="com.mysql.cj.jdbc.Driver"
        user="root"
        password="joyadata"
        query="select `emp_id` as id ,`emp_name` as name from `seatunnel_source`.`emp_quality`"
        "batch_size"="1024"
        result_table_name="s11"
        }




}
transform {
    FieldMapper{
            source_table_name="s11"
            result_table_name="rrr111"
                    field_mapper={
                       id="EMP_ID"
                       name="EMP_NAME"
                    }
        }
        FieldMapper{
                source_table_name="rrr111"
                result_table_name="rrr222"
                        field_mapper={
                           EMP_ID="EMP_ID"
                           EMP_NAME="EMP_NAME"
                        }
            }
}
sink {
        Jdbc {
        source_table_name="rrr111"
        url="************************************************"
        driver="oracle.jdbc.driver.OracleDriver"
        user="SYSTEM"
        password="Cdyanfa_123456"
        database="ORCLCDB"
        table="C##QJQ.EMP"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "primary_keys"=["EMP_ID"]
        "support_upsert_by_query_primary_key_exist"="false"
        "enable_upsert"="true"
        "generate_sink_sql"="true"
        }


}
