 env {
"job.mode"="BATCH"
"job.name"="1"
}
source {
        Jdbc {
        url="jdbc:oracle:thin:@//************:1521/slistest"
        driver="oracle.jdbc.driver.OracleDriver"
        user="abtest"
        password="atestb"
        query="SELECT wcc.C_CHA_CDE    AS agent_code,        wos.C_SEX        AS sex,        wos.T_BIRTHDAY   AS birthday,        wos.C_NTN_CDE    AS nation,        wos.C_TEL        AS phone,        wos.C_MOBILE     AS mobile,        wos.C_EMAIL      AS email,        wos.C_ADDR       AS address,        wos.C_ZIP_CDE    AS post_code,        wose.C_EDU_CDE   AS edu_credentials,        wose.C_PRTY_TYP  AS political_nature,        wose.c_major_cde AS major_code,        wose.c_title_cde AS title_code,        wose.c_duty      AS duty_code   FROM WEB_CUS_CHA     wcc,        WEB_CUS_PER_AGT wcpa,        WEB_OPER_TEMP   wot,        WEB_ORG_SALES   wos,        web_org_dpt     wod,        WEB_ORG_SALES_EXT wose  WHERE wcc.c_sls_cde(+) = wos.c_sls_cde    and wos.c_dpt_cde = wod.c_dpt_cde    and wcc.c_sls_cde = wose.c_sls_cde(+)    and wcc.c_cha_cde = wcpa.c_cha_cde(+)    and wos.c_emp_typ = '0'    and wcc.C_CERTF_NO = wot.C_ID_NO(+)    and ((wos.C_IS_VALID = '1' and wcc.t_adb_tm is null) or         wcc.t_adb_tm is null or         (wos.C_IS_VALID = '0' and wcc.t_adb_tm is not null))    and nvl(wcc.c_cha_mrk, '1') = '1'    and (wos.c_main_company is null or wos.c_main_company in ('EE00'))    and wos.C_SLS_TYP = '020003'    and nvl(wcc.c_audit_status, '1') = '1'    and wcc.C_CHA_CDE is not null and (wcc.t_crt_tm >= (SYSDATE - TO_DSINTERVAL('0 1:00:00')) or wcc.t_upd_tm >= (SYSDATE - TO_DSINTERVAL('0 1:00:00'))) and wod.c_dpt_cde not in (select d.C_DPT_CDE from WEB_ORG_DPT d where d.C_IS_VALID ='1' start with d.C_DPT_CDE IN ('04','17','07') connect by prior d.C_DPT_CDE=d.C_SNR_DPT and prior d.C_DPT_CDE != d.C_DPT_CDE)"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
            "result_table_name"="null_source_1"
        "parallelism"=2
        }





}
transform {
        FieldMapper{
            source_table_name="null_source_1"
            result_table_name="null_source_1_trans_2"
                    field_mapper{
                        DUTY_CODE=DUTY_CODE
                        TITLE_CODE=TITLE_CODE
                        SEX=SEX
                        PHONE=PHONE
                        JOB_TITLE=JOB_TITLE
                        EMAIL=EMAIL
                        AGENT_CODE=AGENT_CODE
                        POLITICAL_NATURE=POLITICAL_NATURE
                        MAJOR_CODE=MAJOR_CODE
                        NATION=NATION
                        EDU_CREDENTIALS=EDU_CREDENTIALS
                        ADDRESS=ADDRESS
                        POST_CODE=POST_CODE
                        BIRTHDAY=BIRTHDAY
                        MOBILE=MOBILE
                    }
        }
}
sink {
        Jdbc {
        url="jdbc:oracle:thin:@//************:1521/DJDB"
        driver="oracle.jdbc.driver.OracleDriver"
            user="cx_sales"
            password="X!ciw3c"
        database="DJDB"
        table="CX_SALES.SALES_AGENT_SUB"
        "connection_check_timeout_sec"="30"
        "batch_size"="1000"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="1"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="true"
            "source_table_name"="null_source_1_trans_2"
        "generate_sink_sql"="true"
            "primary_keys" = ["AGENT_CODE"]
            "field_ide"=UPPERCASE
        schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
        }
}