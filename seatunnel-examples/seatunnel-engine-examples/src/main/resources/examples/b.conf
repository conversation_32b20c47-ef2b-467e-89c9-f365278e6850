env {
    "job.mode"="BATCH"
    "job.name"="1"
}
source {
    Jdbc {
        url="jdbc:dm://192.168.90.232:5236/SYSDBA"
        driver="dm.jdbc.driver.DmDriver"
        user="SYSD<PERSON>"
        password="SYSDBA001"
        query="select emp_id,emp_name,gender,account,org_id,birth_date,age,nationality,province,city,email,phone,begin_date,remark,create_time,update_time from emp_20240305 Where 1=1"
        "batch_size"="1024"
        "connection_check_timeout_sec"="30"
        "snapshot.split.size"="8096"
        "snapshot.fetch.size"="1024"
        "connect.timeout.ms"="30000"
        "connect.max-retries"="3"
        "connection.pool.size"="20"
        "chunk-key.even-distribution.factor.lower-bound"="0.05"
        "chunk-key.even-distribution.factor.upper-bound"="100"
        "sample-sharding.threshold"="1000"
        "inverse-sampling.rate"="1000"
        "startup.mode"="INITIAL"
        "stop.mode"="NEVER"
        "result_table_name"="emp_20240305_source_1"
        "parallelism"=2
    }




}
transform {
            FieldMapper{
                source_table_name="emp_20240305_source_1"
                result_table_name="emp_20240305_source_1_trans_2"
                        field_mapper={
                            gender=gender
                            create_time=create_time
                            city=city
                            birth_date=birth_date
                            begin_date=begin_date
                            emp_name=emp_name
                            remark=remark
                            update_time=update_time
                            nationality=nationality
                            province=province
                            phone=phone
                            org_id=org_id
                            account=account
                            age=age
                            email=email
                            emp_id=emp_id
                        }
            }
}
sink {
    Jdbc {
        url="jdbc:dm://192.168.90.232:5236"
        driver="dm.jdbc.driver.DmDriver"
            user="SYSDBA"
            password="SYSDBA001"
        database="SYSDBA"
        table="SYSDBA.DAMENG"
        "connection_check_timeout_sec"="30"
        "batch_size"="1024"
        "is_exactly_once"="false"
        "max_commit_attempts"="3"
        "transaction_timeout_sec"="-1"
        "max_retries"="0"
        "auto_commit"="true"
        "support_upsert_by_query_primary_key_exist"="false"
        "source_table_name"="emp_20240305_source_1_trans_2"
        "generate_sink_sql"="true"
            "field_ide"="uppercase"
    }
}