<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~    http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-examples</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-engine-examples</artifactId>
    <name>SeaTunnel : Examples : Engine</name>

    <properties>
        <hadoop3.version>3.1.4</hadoop3.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-starter</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!--   seatunnel-transforms-v2   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-transforms-v2</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!--   seatunnel-transforms-v2   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-fake</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-assert</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-maxcompute</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>gs-jdbc</groupId>
            <artifactId>gsjdbc200</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-elasticsearch</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hive</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-s3</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-s3</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-s3</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!--        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file</artifactId>
            <version>2.3.4</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.27</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.300</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-bundle</artifactId>
            <version>1.11.330</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-jdbc</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-exec</artifactId>
            <version>2.1.1</version>
        </dependency>
        <!-- metastore 不能与hbase一起使用会有冲突 -->
        <dependency>
            <groupId>org.apache.hive</groupId>
            <artifactId>hive-metastore</artifactId>
            <version>2.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.hbase</groupId>
                    <artifactId>hbase-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 若项目中没有包含hadoop相关依赖，可能还需要添加如下依赖 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <!-- 开始  -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-clickhouse</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-cdc-mysql</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>12.2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.2.141</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-cdc-sqlserver</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>9.2.1.jre8</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-cdc-oracle</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-transforms-v2</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-mongodb</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.sap.cloud.db.jdbc</groupId>
            <artifactId>ngdbc</artifactId>
            <version>2.4.51</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-kudu</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-kafka</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-doris</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-base</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-local</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-sftp</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-ftp</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-oss</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-hadoop</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.4.3</version>
        </dependency>
        <dependency>
            <groupId>com.pivotal</groupId>
            <artifactId>greenplum-jdbc</artifactId>
            <version>5.1.4</version>
        </dependency>
        <dependency>
            <groupId>cn.com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.parquet</groupId>
            <artifactId>parquet-avro</artifactId>
            <version>1.12.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.orc</groupId>
            <artifactId>orc-core</artifactId>
            <version>1.5.6</version>
            <classifier>nohive</classifier>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2.jcc</groupId>
            <artifactId>db2jcc</artifactId>
            <version>db2jcc4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console-hole</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!-- <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hive-argofile-hadoop</artifactId>
            <version>2.3.4</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-obs</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>com.cloudera</groupId>
            <artifactId>ImpalaJDBC41</artifactId>
            <version>2.6.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-doris</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!-- <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hive-argofile-local</artifactId>
            <version>2.3.4</version>
        </dependency>-->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-local</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-ftp</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-sftp</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-s3</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!-- 关闭 -->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aws</artifactId>
            <version>3.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-bundle</artifactId>
            <version>1.11.330</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-aliyun</artifactId>
            <version>3.1.4</version>
        </dependency>
        <!--        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hbase</artifactId>
            <version>2.3.4</version>
        </dependency>-->
        <dependency>
            <groupId>com.ibm.informix</groupId>
            <artifactId>jdbc</artifactId>
            <version>4.50.6</version>
        </dependency>
        <!--   <dependency>
            <groupId>com.goldendb</groupId>
            <artifactId>gdb-mysql-connector-java</artifactId>
            <version>5.1.46.28</version>
        </dependency>-->

        <!-- 添加argo2adb start -->
        <!--        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hive-adbargo-local</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-hive-adbargo-hadoop</artifactId>
            <version>${project.version}</version>
        </dependency>-->
        <!-- 添加argo2adb end -->

        <!-- dws使用gds方式 start -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-gds2dws-local</artifactId>
            <version>2.3.4</version>
        </dependency>
        <!-- end -->

        <!-- 本地调试 http-添加依赖-->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-http-base</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
    </dependencies>
</project>
