 env   {
    job.name= "test"
  }
 
 
 source  {
    Jdbc{
       query= "SELECT  *  FROM   source_xml_user_v1 ",
       fetch_size= 10,
       url= "**********************************",
       driver= "com.mysql.cj.jdbc.Driver",
       user= "root",
       password= "123456$",
       plugin_name= "Jdbc",
       result_table_name="E000001_source_1"
    }
  }
  
transform {
    XmlPath {
       plugin_name= "XmlPath",
       timeFormat="HH:mm:ss",
       dateFormat="yyyy/MM/dd",
       datetimeFormat="yyyy-MM-dd HH:mm:ss"
       mapping_keys= [
        {
           srcField= "user_info_xml"
           xpath= "/users/user/name"
           destField= "name"
           destType= "string"
           dataFormat=""
           defaultValue=""
        }
        {
           srcField= "user_info_xml"
           xpath= "/users/user/age"
           destField= "age"
           destType= "int"
           dataFormat=""
            defaultValue="0"
        }
        {
           srcField= "user_info_xml"
           xpath= "/users/user/addr"
           destField= "addr"
           destType= "string"
           dataFormat=""
           defaultValue="北京-10"
        }
        {
                   srcField= "user_info_xml"
                   xpath= "/users/user/beijingTime"
                   destField= "beijingTime"
                   destType= "DATE"
                   dataFormat="yyyy-MM-dd"
                   defaultValue="2025-11-10"
        }
        {
           srcField= "other_info_xml"
           xpath= "/conf/teacher/teacherName"
           destField= "teacherName"
           destType= "string"
           dataFormat=""
           defaultValue="默认"
        }
        {
           srcField= "other_info_xml",
           xpath= "/conf/grades/grade[@class]"
           destField= "studentName"
           destType= "string"
           dataFormat=""
           defaultValue="默认"
        }
      ]
      output_fields= [
          {
             fieldName="id"
             fieldType="BIGINT"
             dataFormat=""
          }
        {
               fieldName="create_time"
               fieldType="DATE"
               dataFormat="yyyy-MM-dd"
         }
      ]
      source_table_name="E000001_source_1",
      result_table_name="E000001_source_1_trans_1"
    }
}
  
sink {
    Jdbc{
       source_table_name="E000001_source_1_trans_1",
       url= "**********************************",
       driver= "com.mysql.cj.jdbc.Driver",
       user= "root",
       password= "123456$",
       plugin_name= "Jdbc",
       database= "dbtest",
       table= "target_user_v1",
       generate_sink_sql= true,
       max_retries= 3,
       batch_size= 300
    }
 }
 