<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-examples</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-flink-connector-v2-example</artifactId>
    <name>SeaTunnel : Examples : Flink Connector V2</name>

    <properties>
        <flink.scope>compile</flink.scope>
        <mock-webserver.version>3.6.0</mock-webserver.version>
    </properties>

    <dependencies>
        <!--Resolve ConfigParser class conflict between SeaTunnel and Flink.
        Native debugging increases flink's default ConfigParser priority.-->
        <dependency>
            <groupId>com.typesafe</groupId>
            <artifactId>config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-flink-15-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--   seatunnel-transforms-v2   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-transforms-v2</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   seatunnel-transforms-v2   -->

        <!--   seatunnel-connector   -->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-fake</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-console</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--   seatunnel connectors   -->
        <!--flink-->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-java</artifactId>
            <version>${flink.1.15.3.version}</version>
            <scope>${flink.scope}</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner-loader</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-runtime</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-clients</artifactId>
            <version>${flink.1.15.3.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>mockwebserver</artifactId>
            <version>${mock-webserver.version}</version>
        </dependency>

        <!-- 本地调试 http-添加依赖-->
        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-http-base</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-jdbc</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-doris</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.27</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>connector-file-mergefile-local</artifactId>
            <version>2.3.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
            <version>2.3.4</version>
        </dependency>
    </dependencies>

</project>
